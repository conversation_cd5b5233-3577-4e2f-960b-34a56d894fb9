# OneCRM - Enterprise Customer Relationship Management Platform

[![License: GPL v3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Build Status](https://github.com/onecrm/onecrm/workflows/CI/badge.svg)](https://github.com/onecrm/onecrm/actions)
[![Coverage Status](https://coveralls.io/repos/github/onecrm/onecrm/badge.svg?branch=main)](https://coveralls.io/github/onecrm/onecrm?branch=main)
[![Docker Pulls](https://img.shields.io/docker/pulls/onecrm/backend.svg)](https://hub.docker.com/r/onecrm/backend)

OneCRM is a modern, enterprise-grade Customer Relationship Management platform built with cutting-edge technologies. It provides comprehensive CRM functionality with multi-tenancy, advanced security, and seamless scalability.

## 🚀 Features

### Core CRM Functionality
- **Contact Management**: Complete contact lifecycle with advanced search and filtering
- **Company Management**: Hierarchical company structures with relationship mapping
- **Deal Pipeline**: Visual sales pipeline with customizable stages and forecasting
- **Activity Tracking**: Comprehensive interaction history and task management
- **Dashboard & Analytics**: Real-time KPI monitoring and business intelligence

### Enterprise Features
- **Multi-Tenancy**: Complete tenant isolation with organization management
- **Single Sign-On**: Keycloak-based authentication with OIDC/SAML support
- **API Gateway**: Kong-powered API management with rate limiting and security
- **Role-Based Access Control**: Granular permissions and security policies
- **Audit Logging**: Complete audit trail for compliance and security

### Technical Excellence
- **Microservices Architecture**: Scalable, maintainable service-oriented design
- **Cloud-Native**: Kubernetes-ready with Docker containerization
- **High Availability**: Blue-green deployments with zero-downtime updates
- **Monitoring & Observability**: Prometheus, Grafana, and Loki integration
- **Comprehensive Testing**: 95%+ test coverage with automated quality gates

## 🏗️ Architecture

OneCRM follows a modern microservices architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Backend API   │
│   (Next.js)     │◄──►│   (Kong)        │◄──►│   (NestJS)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Auth Service  │    │   Database      │
                       │   (Keycloak)    │    │   (PostgreSQL)  │
                       └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Frontend:**
- Next.js 14 with React 18
- TypeScript for type safety
- Material-UI for design system
- TanStack Query for state management
- Playwright for E2E testing

**Backend:**
- NestJS with TypeScript
- PostgreSQL with TypeORM
- Redis for caching
- JWT authentication
- Comprehensive API documentation

**Infrastructure:**
- Docker & Kubernetes
- Kong API Gateway
- Keycloak SSO
- Prometheus & Grafana monitoring
- Automated CI/CD pipelines

## 📋 Prerequisites

- **Node.js** 18+ and npm
- **Docker** and Docker Compose
- **Kubernetes** cluster (for production)
- **PostgreSQL** 15+
- **Redis** 7+

## 🚀 Quick Start

### Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/onecrm/onecrm.git
   cd onecrm
   ```

2. **Install dependencies:**
   ```bash
   # Backend
   cd backend && npm install

   # Frontend
   cd ../frontend && npm install
   ```

3. **Start development environment:**
   ```bash
   # Start infrastructure services
   docker-compose up -d postgres redis keycloak

   # Start backend
   cd backend && npm run start:dev

   # Start frontend (in another terminal)
   cd frontend && npm run dev
   ```

4. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/api-docs
   - Keycloak: http://localhost:8080

### Production Deployment

1. **Generate secrets:**
   ```bash
   ./scripts/manage-secrets.sh production generate
   ```

2. **Deploy to staging:**
   ```bash
   ./scripts/staging-deploy.sh
   ./scripts/test-staging.sh
   ```

3. **Deploy to production:**
   ```bash
   ./scripts/production-deploy.sh v1.0.0 blue-green deploy
   ```

## 📚 Documentation

- **[User Guide](docs/user-guide/README.md)** - End-user documentation
- **[Admin Guide](docs/admin-guide/README.md)** - System administration
- **[Developer Guide](docs/developer-guide/README.md)** - Development setup and guidelines
- **[API Documentation](docs/api/README.md)** - REST API reference
- **[Deployment Guide](docs/deployment/README.md)** - Production deployment

## 🧪 Testing

OneCRM maintains high quality standards with comprehensive testing:

```bash
# Run all tests
./scripts/run-all-tests.sh

# Backend tests
cd backend
npm run test              # Unit tests
npm run test:integration  # Integration tests
npm run test:e2e         # End-to-end tests

# Frontend tests
cd frontend
npm run test             # Unit tests
npm run test:e2e         # E2E tests with Playwright
```

## 📊 Monitoring

OneCRM includes comprehensive monitoring and observability:

```bash
# Setup monitoring stack
./scripts/setup-monitoring.sh

# Access monitoring dashboards
kubectl port-forward -n onecrm-monitoring svc/grafana 3000:3000
kubectl port-forward -n onecrm-monitoring svc/prometheus 9090:9090
```

## 🔒 Security

OneCRM implements enterprise-grade security:

- **Authentication**: Keycloak SSO with OIDC/SAML
- **Authorization**: Role-based access control (RBAC)
- **Data Protection**: Encryption at rest and in transit
- **API Security**: Rate limiting, CORS, and security headers
- **Audit Logging**: Complete audit trail for compliance

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

OneCRM is licensed under the GNU General Public License v3.0. See [LICENSE](LICENSE) for details.

This project is based on [Twenty](https://github.com/twentyhq/twenty) and maintains compliance with GPL v3.0 requirements.

## 🆘 Support

- **Documentation**: [docs.onecrm.example.com](https://docs.onecrm.example.com)
- **Issues**: [GitHub Issues](https://github.com/onecrm/onecrm/issues)
- **Discussions**: [GitHub Discussions](https://github.com/onecrm/onecrm/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

- [ ] Mobile applications (iOS/Android)
- [ ] Advanced analytics and reporting
- [ ] Third-party integrations (Salesforce, HubSpot)
- [ ] AI-powered insights and automation
- [ ] Advanced workflow automation
- [ ] Multi-language support

## 🏆 Acknowledgments

- [Twenty](https://github.com/twentyhq/twenty) - Base CRM platform
- [NestJS](https://nestjs.com/) - Backend framework
- [Next.js](https://nextjs.org/) - Frontend framework
- [Keycloak](https://www.keycloak.org/) - Identity and access management
- [Kong](https://konghq.com/) - API gateway

---

**OneCRM** - Empowering businesses with intelligent customer relationship management.

Made with ❤️ by the OneCRM Team
