{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "onecrm", "affected": {"defaultBase": "main"}, "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "typecheck"]}}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"]}, "typecheck": {"inputs": ["default", "^production"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json"], "sharedGlobals": []}, "generators": {"@nx/next": {"application": {"style": "css", "linter": "eslint"}}, "@nx/node": {"application": {"linter": "eslint"}}}, "defaultProject": "frontend"}