-- OneCRM Mock Data Seeding Script
-- This script populates the database with realistic test data

-- Clear existing data (in reverse order of dependencies)
DELETE FROM activities;
DELETE FROM deals;
DELETE FROM contacts;
DELETE FROM companies;
DELETE FROM users;
DELETE FROM organizations;

-- Insert Organizations
INSERT INTO organizations (id, name, slug, plan, settings, max_users, max_storage_gb, features, status, suspended, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'TechCorp Solutions', 'techcorp', 'pro', '{"theme": "blue", "timezone": "UTC", "currency": "USD"}', 50, 10, '{"advanced_reporting": true, "api_access": true}', 'active', false, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', 'StartupInc', 'startupinc', 'free', '{"theme": "green", "timezone": "PST", "currency": "USD"}', 5, 1, '{"basic_reporting": true}', 'active', false, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 'Enterprise Corp', 'enterprise', 'enterprise', '{"theme": "dark", "timezone": "EST", "currency": "USD"}', 200, 100, '{"advanced_reporting": true, "api_access": true, "custom_fields": true}', 'active', false, NOW(), NOW());

-- Insert Users
INSERT INTO users (id, keycloak_sub, org_id, email, first_name, last_name, role, is_active, created_at, updated_at) VALUES
-- TechCorp Users
('550e8400-e29b-41d4-a716-446655440101', 'kc-admin-techcorp', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'John', 'Admin', 'admin', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440102', 'kc-sales-manager', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Sarah', 'Johnson', 'user', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440103', 'kc-sales-rep1', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Mike', 'Wilson', 'user', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440104', 'kc-sales-rep2', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Emily', 'Davis', 'user', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440105', 'kc-marketing-mgr', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'David', 'Brown', 'user', true, NOW(), NOW()),

-- StartupInc Users
('550e8400-e29b-41d4-a716-446655440201', 'kc-admin-startup', '550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Lisa', 'Founder', 'admin', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-************', 'kc-sales-startup', '550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Tom', 'Seller', 'user', true, NOW(), NOW()),

-- Enterprise Corp Users
('550e8400-e29b-41d4-a716-446655440301', 'kc-admin-enterprise', '550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Robert', 'Executive', 'admin', true, NOW(), NOW());

-- Insert Companies
INSERT INTO companies (id, org_id, name, domain, industry, size, description, website, phone, address, tags, custom_fields, annual_revenue, employee_count, created_by, updated_by, assigned_to, created_at, updated_at) VALUES
-- TechCorp Companies
('550e8400-e29b-41d4-a716-446655440401', '550e8400-e29b-41d4-a716-446655440001', 'Acme Corporation', 'acme.com', 'Technology', 'Large', 'Leading technology solutions provider', 'https://acme.com', '******-0101', '{"street": "123 Tech Street", "city": "San Francisco", "state": "CA", "zip": "94105", "country": "USA"}', '["enterprise", "technology", "saas"]', '{"industry_vertical": "B2B SaaS", "lead_source": "website"}', 5000000.00, 250, '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440103', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440402', '550e8400-e29b-41d4-a716-446655440001', 'Global Dynamics', 'globaldynamics.com', 'Manufacturing', 'Enterprise', 'International manufacturing company', 'https://globaldynamics.com', '******-0102', '{"street": "456 Industrial Ave", "city": "Detroit", "state": "MI", "zip": "48201", "country": "USA"}', '["manufacturing", "global", "b2b"]', '{"industry_vertical": "Manufacturing", "lead_source": "referral"}', 15000000.00, 1200, '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440104', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440403', '550e8400-e29b-41d4-a716-446655440001', 'FinTech Innovations', 'fintech-innovations.com', 'Financial Services', 'Medium', 'Innovative financial technology solutions', 'https://fintech-innovations.com', '******-0103', '{"street": "789 Finance Blvd", "city": "New York", "state": "NY", "zip": "10001", "country": "USA"}', '["fintech", "innovation", "startup"]', '{"industry_vertical": "FinTech", "lead_source": "cold_outreach"}', 2500000.00, 85, '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440103', NOW(), NOW()),

-- StartupInc Companies
('550e8400-e29b-41d4-a716-446655440501', '550e8400-e29b-41d4-a716-************', 'Local Retail Chain', 'localretail.com', 'Retail', 'Small', 'Regional retail chain', 'https://localretail.com', '******-0201', '{"street": "321 Main Street", "city": "Austin", "state": "TX", "zip": "73301", "country": "USA"}', '["retail", "local", "b2c"]', '{"industry_vertical": "Retail", "lead_source": "networking"}', 800000.00, 45, '550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-************', NOW(), NOW());

-- Insert Contacts
INSERT INTO contacts (id, org_id, company_id, first_name, last_name, email, phone, title, lead_status, lead_source, custom_fields, assigned_to, created_by, updated_by, created_at, updated_at) VALUES
-- TechCorp Contacts
('550e8400-e29b-41d4-a716-446655440601', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440401', 'James', 'Smith', '<EMAIL>', '******-1001', 'CTO', 'qualified', 'website', '{"linkedin": "https://linkedin.com/in/jamessmith", "notes": "Very interested in our enterprise solution"}', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440602', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440401', 'Maria', 'Garcia', '<EMAIL>', '******-1002', 'VP Engineering', 'lead', 'referral', '{"linkedin": "https://linkedin.com/in/mariagarcia", "notes": "Referred by James Smith"}', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440603', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440402', 'Robert', 'Johnson', '<EMAIL>', '******-1003', 'CEO', 'prospect', 'cold_outreach', '{"linkedin": "https://linkedin.com/in/robertjohnson", "notes": "Initial contact made, needs follow-up"}', '550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440604', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440403', 'Jennifer', 'Lee', '<EMAIL>', '******-1004', 'Head of Technology', 'customer', 'networking', '{"linkedin": "https://linkedin.com/in/jenniferlee", "notes": "Existing customer, looking to expand"}', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

-- StartupInc Contacts
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440501', 'Michael', 'Brown', '<EMAIL>', '******-2001', 'Store Manager', 'lead', 'networking', '{"notes": "Met at local business meetup"}', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440201', NOW(), NOW());

-- Insert Deals
INSERT INTO deals (id, org_id, contact_id, company_id, title, amount, currency, stage, probability, expected_close_date, description, owner_id, created_by, updated_by, created_at, updated_at) VALUES
-- TechCorp Deals
('550e8400-e29b-41d4-a716-446655440801', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440601', '550e8400-e29b-41d4-a716-446655440401', 'Acme Enterprise License', 150000.00, 'USD', 'proposal', 75, '2025-08-15', 'Enterprise software license for Acme Corporation including implementation and training', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440802', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440603', '550e8400-e29b-41d4-a716-446655440402', 'Global Dynamics Integration', 300000.00, 'USD', 'negotiation', 60, '2025-09-30', 'Custom integration solution for Global Dynamics manufacturing systems', '550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440803', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440604', '550e8400-e29b-41d4-a716-446655440403', 'FinTech Expansion', 75000.00, 'USD', 'closed_won', 100, '2025-07-01', 'Additional modules for existing FinTech customer', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440102', NOW(), NOW()),

-- StartupInc Deals
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440501', 'Local Retail POS System', 25000.00, 'USD', 'discovery', 25, '2025-10-15', 'Point of sale system for local retail chain', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440201', NOW(), NOW());

-- Insert Activities
INSERT INTO activities (id, org_id, user_id, contact_id, deal_id, type, subject, description, scheduled_at, status, priority, is_completed, created_at, updated_at) VALUES
-- TechCorp Activities
('550e8400-e29b-41d4-a716-446655441001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440601', '550e8400-e29b-41d4-a716-446655440801', 'call', 'Discovery Call with James Smith', 'Initial discovery call to understand requirements for enterprise license', '2025-07-20 14:00:00', 'completed', 'high', true, NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655441002', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440601', '550e8400-e29b-41d4-a716-446655440801', 'meeting', 'Technical Demo', 'Product demonstration for Acme technical team', '2025-07-25 10:00:00', 'completed', 'high', true, NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655441003', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440603', '550e8400-e29b-41d4-a716-446655440802', 'email', 'Follow-up Email', 'Sent follow-up email with proposal details', '2025-07-28 09:30:00', 'completed', 'medium', true, NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655441004', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440604', '550e8400-e29b-41d4-a716-446655440803', 'meeting', 'Contract Signing', 'Final contract signing meeting', '2025-07-01 15:00:00', 'completed', 'high', true, NOW(), NOW()),

-- StartupInc Activities
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'call', 'Initial Contact Call', 'First contact call with Michael Brown', '2025-07-15 11:00:00', 'pending', 'medium', false, NOW(), NOW());

-- Mock data seeding completed successfully
-- Note: Using UUID primary keys, so no sequence updates needed
