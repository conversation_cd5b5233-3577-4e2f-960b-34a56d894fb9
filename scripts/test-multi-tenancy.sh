#!/bin/bash

# OneCRM Multi-Tenancy Testing Script
# This script tests tenant isolation and data security

set -e

API_URL="${API_URL:-http://localhost:8000}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-onecrm}"
DB_USER="${DB_USER:-onecrm}"
DB_PASSWORD="${DB_PASSWORD:-onecrm_dev_password}"

echo "🧪 Testing OneCRM Multi-Tenancy..."

# Test organizations
ACME_ORG_ID="550e8400-e29b-41d4-a716-************"
TECHSTART_ORG_ID="550e8400-e29b-41d4-a716-************"
SMALLBIZ_ORG_ID="550e8400-e29b-41d4-a716-446655440003"

# Function to test database RLS policies
test_rls_policies() {
    echo "🔒 Testing Row Level Security policies..."
    
    # Test that RLS is enabled on all tables
    local tables=("organizations" "users" "contacts" "companies" "deals" "activities")
    
    for table in "${tables[@]}"; do
        local rls_enabled=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT relrowsecurity FROM pg_class WHERE relname = '$table';" | tr -d ' ')
        
        if [[ "$rls_enabled" =~ "t" ]]; then
            echo "✅ RLS enabled on $table"
        else
            echo "❌ RLS not enabled on $table"
            return 1
        fi
    done
    
    echo "✅ All tables have RLS enabled"
}

# Function to test tenant data isolation
test_tenant_isolation() {
    echo "🏢 Testing tenant data isolation..."
    
    # Test each organization's data isolation
    local orgs=("$ACME_ORG_ID" "$TECHSTART_ORG_ID" "$SMALLBIZ_ORG_ID")
    
    for org_id in "${orgs[@]}"; do
        echo "🔍 Testing isolation for org: $org_id"
        
        # Set user context for this organization
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT set_user_context(
                '660e8400-e29b-41d4-a716-************'::UUID,
                '$org_id'::UUID,
                'admin',
                'development'
            );
        " > /dev/null
        
        # Count records for this org
        local user_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM users WHERE org_id = '$org_id';" | tr -d ' ')
        local contact_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM contacts WHERE org_id = '$org_id';" | tr -d ' ')
        local company_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM companies WHERE org_id = '$org_id';" | tr -d ' ')
        local deal_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM deals WHERE org_id = '$org_id';" | tr -d ' ')
        
        echo "  📊 Users: $user_count, Contacts: $contact_count, Companies: $company_count, Deals: $deal_count"
        
        # Verify no cross-tenant data leakage
        local other_org_contacts=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM contacts WHERE org_id != '$org_id';" | tr -d ' ')
        
        if [ "$other_org_contacts" -gt 0 ]; then
            echo "  ⚠️  Found $other_org_contacts contacts from other organizations (this should be 0 with proper RLS)"
        else
            echo "  ✅ No cross-tenant data leakage detected"
        fi
    done
    
    # Clear user context
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT clear_user_context();" > /dev/null
    
    echo "✅ Tenant isolation test completed"
}

# Function to test API tenant isolation
test_api_isolation() {
    echo "🌐 Testing API tenant isolation..."
    
    # Test that API endpoints respect tenant context
    local response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "X-Org-Id: $ACME_ORG_ID" \
        "$API_URL/api/organizations/current")
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        echo "✅ API responds to tenant context"
    else
        echo "❌ API tenant context test failed (HTTP $response)"
        return 1
    fi
    
    # Test cross-tenant access prevention
    local cross_tenant_response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "X-Org-Id: $ACME_ORG_ID" \
        "$API_URL/api/users/organization")
    
    if [ "$cross_tenant_response" = "401" ] || [ "$cross_tenant_response" = "403" ]; then
        echo "✅ Cross-tenant access properly blocked"
    elif [ "$cross_tenant_response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ API accessible in development mode"
    else
        echo "⚠️  Unexpected cross-tenant response (HTTP $cross_tenant_response)"
    fi
}

# Function to test user context switching
test_user_context_switching() {
    echo "👤 Testing user context switching..."
    
    # Test switching between different user contexts
    local test_users=(
        "$ACME_ORG_ID:660e8400-e29b-41d4-a716-************:admin"
        "$TECHSTART_ORG_ID:660e8400-e29b-41d4-a716-************:admin"
        "$SMALLBIZ_ORG_ID:660e8400-e29b-41d4-a716-************:admin"
    )
    
    for user_context in "${test_users[@]}"; do
        IFS=':' read -r org_id user_id role <<< "$user_context"
        
        echo "🔄 Testing context: User $user_id in Org $org_id as $role"
        
        # Set user context
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT set_user_context(
                '$user_id'::UUID,
                '$org_id'::UUID,
                '$role',
                'development'
            );
        " > /dev/null
        
        # Test that user can only see their org's data
        local visible_orgs=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(DISTINCT org_id) FROM users;" | tr -d ' ')
        
        if [ "$visible_orgs" = "1" ]; then
            echo "  ✅ User can only see their organization's data"
        else
            echo "  ❌ User can see data from $visible_orgs organizations (should be 1)"
            return 1
        fi
    done
    
    # Clear user context
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT clear_user_context();" > /dev/null
    
    echo "✅ User context switching test completed"
}

# Function to test permission enforcement
test_permission_enforcement() {
    echo "🔐 Testing permission enforcement..."
    
    # Test admin vs user permissions
    local admin_user="660e8400-e29b-41d4-a716-************"
    local regular_user="660e8400-e29b-41d4-a716-************"
    
    # Test admin permissions
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT set_user_context(
            '$admin_user'::UUID,
            '$ACME_ORG_ID'::UUID,
            'admin',
            'development'
        );
    " > /dev/null
    
    local admin_can_see_users=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM users WHERE org_id = '$ACME_ORG_ID';" | tr -d ' ')
    
    if [ "$admin_can_see_users" -gt 0 ]; then
        echo "✅ Admin can access user data"
    else
        echo "❌ Admin cannot access user data"
        return 1
    fi
    
    # Test regular user permissions
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT set_user_context(
            '$regular_user'::UUID,
            '$ACME_ORG_ID'::UUID,
            'user',
            'development'
        );
    " > /dev/null
    
    local user_can_see_users=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM users WHERE org_id = '$ACME_ORG_ID';" | tr -d ' ')
    
    if [ "$user_can_see_users" -gt 0 ]; then
        echo "✅ Regular user can access user data (in development mode)"
    else
        echo "⚠️  Regular user cannot access user data"
    fi
    
    # Clear user context
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT clear_user_context();" > /dev/null
    
    echo "✅ Permission enforcement test completed"
}

# Function to test data consistency
test_data_consistency() {
    echo "📊 Testing data consistency..."
    
    # Verify that all records have proper org_id
    local tables=("users" "contacts" "companies" "deals" "activities")
    
    for table in "${tables[@]}"; do
        local records_without_org=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table WHERE org_id IS NULL;" | tr -d ' ')
        
        if [ "$records_without_org" = "0" ]; then
            echo "✅ All $table records have org_id"
        else
            echo "❌ Found $records_without_org $table records without org_id"
            return 1
        fi
    done
    
    # Verify foreign key consistency
    local orphaned_contacts=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM contacts c 
        LEFT JOIN organizations o ON c.org_id = o.id 
        WHERE o.id IS NULL;
    " | tr -d ' ')
    
    if [ "$orphaned_contacts" = "0" ]; then
        echo "✅ No orphaned contact records"
    else
        echo "❌ Found $orphaned_contacts orphaned contact records"
        return 1
    fi
    
    echo "✅ Data consistency test completed"
}

# Function to test performance with tenant filtering
test_performance() {
    echo "⚡ Testing performance with tenant filtering..."
    
    # Test query performance with tenant context
    local start_time=$(date +%s%N)
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT set_user_context(
            '660e8400-e29b-41d4-a716-************'::UUID,
            '$ACME_ORG_ID'::UUID,
            'admin',
            'development'
        );
        SELECT COUNT(*) FROM contacts;
        SELECT COUNT(*) FROM companies;
        SELECT COUNT(*) FROM deals;
        SELECT clear_user_context();
    " > /dev/null
    
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))
    
    echo "✅ Tenant-filtered queries completed in ${duration}ms"
    
    if [ "$duration" -lt 1000 ]; then
        echo "✅ Performance is acceptable"
    else
        echo "⚠️  Performance may need optimization (${duration}ms)"
    fi
}

# Function to display test results
display_results() {
    echo ""
    echo "🎉 Multi-Tenancy Test Complete!"
    echo ""
    echo "📋 Test Summary:"
    echo "- RLS Policies: $rls_status"
    echo "- Tenant Isolation: $isolation_status"
    echo "- API Isolation: $api_status"
    echo "- Context Switching: $context_status"
    echo "- Permission Enforcement: $permission_status"
    echo "- Data Consistency: $consistency_status"
    echo "- Performance: $performance_status"
    echo ""
    
    if [ "$overall_status" = "PASS" ]; then
        echo "✅ All multi-tenancy tests passed!"
        echo ""
        echo "🔒 Security Features Verified:"
        echo "- Row Level Security (RLS) is properly configured"
        echo "- Tenant data isolation is working"
        echo "- Cross-tenant access is prevented"
        echo "- User permissions are enforced"
        echo "- Data consistency is maintained"
    else
        echo "❌ Some multi-tenancy tests failed. Please review the issues above."
        exit 1
    fi
}

# Main execution
main() {
    local overall_status="PASS"
    
    echo "🔧 Configuration:"
    echo "- API URL: $API_URL"
    echo "- Database: $DB_HOST:$DB_PORT/$DB_NAME"
    echo "- Test Organizations: 3"
    echo ""
    
    # Run tests
    if test_rls_policies; then
        rls_status="✅ PASS"
    else
        rls_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_tenant_isolation; then
        isolation_status="✅ PASS"
    else
        isolation_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_api_isolation; then
        api_status="✅ PASS"
    else
        api_status="⚠️  WARN"
    fi
    
    if test_user_context_switching; then
        context_status="✅ PASS"
    else
        context_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_permission_enforcement; then
        permission_status="✅ PASS"
    else
        permission_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_data_consistency; then
        consistency_status="✅ PASS"
    else
        consistency_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_performance; then
        performance_status="✅ PASS"
    else
        performance_status="⚠️  WARN"
    fi
    
    # Display results
    display_results
}

# Run main function
main "$@"
