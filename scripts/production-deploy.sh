#!/bin/bash

# OneCRM Production Deployment Script
# Blue-Green deployment strategy for zero-downtime production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="production"
NAMESPACE="onecrm"
TAG=${1:-latest}
DEPLOYMENT_STRATEGY=${2:-blue-green}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}

# Blue-Green deployment configuration
CURRENT_COLOR=""
NEW_COLOR=""
ROLLBACK_ENABLED=${ROLLBACK_ENABLED:-true}

echo -e "${BLUE}🚀 OneCRM Production Deployment${NC}"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Tag: $TAG"
echo "Strategy: $DEPLOYMENT_STRATEGY"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Pre-deployment checks
pre_deployment_checks() {
    print_section "🔍 Pre-Deployment Checks"
    
    # Check prerequisites
    if ! command_exists kubectl; then
        echo -e "${RED}❌ kubectl is not installed${NC}"
        exit 1
    fi
    
    if ! command_exists docker; then
        echo -e "${RED}❌ Docker is not installed${NC}"
        exit 1
    fi
    
    # Check Kubernetes connection
    if ! kubectl cluster-info >/dev/null 2>&1; then
        echo -e "${RED}❌ Cannot connect to Kubernetes cluster${NC}"
        exit 1
    fi
    
    # Check if production namespace exists
    if ! kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        echo -e "${RED}❌ Production namespace '$NAMESPACE' does not exist${NC}"
        exit 1
    fi
    
    # Check if secrets exist
    if ! kubectl get secret onecrm-secrets -n $NAMESPACE >/dev/null 2>&1; then
        echo -e "${RED}❌ Production secrets not found${NC}"
        echo "Run: ./scripts/manage-secrets.sh production generate && ./scripts/manage-secrets.sh production apply"
        exit 1
    fi
    
    # Check staging deployment status
    echo "Checking staging deployment status..."
    if ! ./scripts/test-staging.sh >/dev/null 2>&1; then
        echo -e "${RED}❌ Staging tests failed. Fix staging issues before production deployment.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All pre-deployment checks passed${NC}"
}

# Determine current and new colors for blue-green deployment
determine_deployment_colors() {
    print_section "🎨 Determining Deployment Colors"
    
    # Check current active deployment
    if kubectl get deployment onecrm-backend-blue -n $NAMESPACE >/dev/null 2>&1; then
        local blue_replicas=$(kubectl get deployment onecrm-backend-blue -n $NAMESPACE -o jsonpath='{.spec.replicas}')
        if [ "$blue_replicas" -gt "0" ]; then
            CURRENT_COLOR="blue"
            NEW_COLOR="green"
        fi
    fi
    
    if kubectl get deployment onecrm-backend-green -n $NAMESPACE >/dev/null 2>&1; then
        local green_replicas=$(kubectl get deployment onecrm-backend-green -n $NAMESPACE -o jsonpath='{.spec.replicas}')
        if [ "$green_replicas" -gt "0" ]; then
            CURRENT_COLOR="green"
            NEW_COLOR="blue"
        fi
    fi
    
    # If no current deployment, start with blue
    if [ -z "$CURRENT_COLOR" ]; then
        CURRENT_COLOR="none"
        NEW_COLOR="blue"
    fi
    
    echo "Current active deployment: $CURRENT_COLOR"
    echo "New deployment color: $NEW_COLOR"
}

# Build production images
build_production_images() {
    print_section "🏗️  Building Production Images"
    
    echo "Building backend image for production..."
    docker build -t onecrm/backend:$TAG \
        --build-arg NODE_ENV=production \
        -f backend/Dockerfile.prod backend/
    
    echo "Building frontend image for production..."
    docker build -t onecrm/frontend:$TAG \
        --build-arg NODE_ENV=production \
        -f frontend/Dockerfile.prod frontend/
    
    if [ -n "$DOCKER_REGISTRY" ]; then
        echo "Tagging images for registry..."
        docker tag onecrm/backend:$TAG $DOCKER_REGISTRY/onecrm/backend:$TAG
        docker tag onecrm/frontend:$TAG $DOCKER_REGISTRY/onecrm/frontend:$TAG
        
        echo "Pushing images to registry..."
        docker push $DOCKER_REGISTRY/onecrm/backend:$TAG
        docker push $DOCKER_REGISTRY/onecrm/frontend:$TAG
    fi
    
    echo -e "${GREEN}✅ Production images built and pushed${NC}"
}

# Deploy new version (blue-green)
deploy_new_version() {
    print_section "🚀 Deploying New Version ($NEW_COLOR)"
    
    # Create new deployment manifests with color suffix
    echo "Creating $NEW_COLOR deployment manifests..."
    
    # Backend deployment
    sed "s/onecrm-backend/onecrm-backend-$NEW_COLOR/g; s/onecrm/backend:latest/onecrm/backend:$TAG/g" \
        infrastructure/kubernetes/backend-deployment.yaml > /tmp/backend-$NEW_COLOR.yaml
    
    # Frontend deployment
    sed "s/onecrm-frontend/onecrm-frontend-$NEW_COLOR/g; s/onecrm/frontend:latest/onecrm/frontend:$TAG/g" \
        infrastructure/kubernetes/frontend-deployment.yaml > /tmp/frontend-$NEW_COLOR.yaml
    
    # Deploy new version
    kubectl apply -f /tmp/backend-$NEW_COLOR.yaml
    kubectl apply -f /tmp/frontend-$NEW_COLOR.yaml
    
    # Wait for new deployment to be ready
    echo "Waiting for $NEW_COLOR backend deployment to be ready..."
    kubectl rollout status deployment/onecrm-backend-$NEW_COLOR -n $NAMESPACE --timeout=600s
    
    echo "Waiting for $NEW_COLOR frontend deployment to be ready..."
    kubectl rollout status deployment/onecrm-frontend-$NEW_COLOR -n $NAMESPACE --timeout=600s
    
    echo -e "${GREEN}✅ New version ($NEW_COLOR) deployed successfully${NC}"
}

# Run health checks on new deployment
health_check_new_deployment() {
    print_section "🏥 Health Checking New Deployment"
    
    # Get new deployment service IPs
    local backend_ip=$(kubectl get pod -l app=onecrm-backend-$NEW_COLOR -n $NAMESPACE -o jsonpath='{.items[0].status.podIP}')
    local frontend_ip=$(kubectl get pod -l app=onecrm-frontend-$NEW_COLOR -n $NAMESPACE -o jsonpath='{.items[0].status.podIP}')
    
    echo "Testing new backend health..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend-$NEW_COLOR -- curl -f -s http://localhost:8000/health >/dev/null; then
        echo -e "${GREEN}✅ New backend health check passed${NC}"
    else
        echo -e "${RED}❌ New backend health check failed${NC}"
        return 1
    fi
    
    echo "Testing new frontend health..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend-$NEW_COLOR -- wget --spider -q http://localhost:3000/api/health; then
        echo -e "${GREEN}✅ New frontend health check passed${NC}"
    else
        echo -e "${RED}❌ New frontend health check failed${NC}"
        return 1
    fi
    
    echo "Testing database connectivity..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend-$NEW_COLOR -- curl -f -s http://localhost:8000/health/db >/dev/null; then
        echo -e "${GREEN}✅ Database connectivity check passed${NC}"
    else
        echo -e "${RED}❌ Database connectivity check failed${NC}"
        return 1
    fi
    
    echo "Testing Redis connectivity..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend-$NEW_COLOR -- curl -f -s http://localhost:8000/health/redis >/dev/null; then
        echo -e "${GREEN}✅ Redis connectivity check passed${NC}"
    else
        echo -e "${RED}❌ Redis connectivity check failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All health checks passed for new deployment${NC}"
}

# Switch traffic to new deployment
switch_traffic() {
    print_section "🔄 Switching Traffic to New Deployment"
    
    echo "Updating service selectors to point to $NEW_COLOR deployment..."
    
    # Update backend service
    kubectl patch service onecrm-backend-service -n $NAMESPACE -p '{"spec":{"selector":{"app":"onecrm-backend-'$NEW_COLOR'"}}}'
    
    # Update frontend service
    kubectl patch service onecrm-frontend-service -n $NAMESPACE -p '{"spec":{"selector":{"app":"onecrm-frontend-'$NEW_COLOR'"}}}'
    
    echo "Waiting for service endpoints to update..."
    sleep 10
    
    # Verify traffic switch
    echo "Verifying traffic switch..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend-$NEW_COLOR -- curl -f -s http://onecrm-backend-service:8000/health >/dev/null; then
        echo -e "${GREEN}✅ Traffic successfully switched to $NEW_COLOR deployment${NC}"
    else
        echo -e "${RED}❌ Traffic switch verification failed${NC}"
        return 1
    fi
}

# Run production smoke tests
run_production_smoke_tests() {
    print_section "🧪 Running Production Smoke Tests"
    
    echo "Running comprehensive smoke tests..."
    
    # Test external endpoints (if ingress is configured)
    if kubectl get ingress onecrm-ingress -n $NAMESPACE >/dev/null 2>&1; then
        local frontend_url="https://onecrm.example.com"
        local api_url="https://api.onecrm.example.com"
        
        echo "Testing external frontend endpoint..."
        if curl -f -s $frontend_url >/dev/null; then
            echo -e "${GREEN}✅ External frontend accessible${NC}"
        else
            echo -e "${RED}❌ External frontend not accessible${NC}"
            return 1
        fi
        
        echo "Testing external API endpoint..."
        if curl -f -s $api_url/health >/dev/null; then
            echo -e "${GREEN}✅ External API accessible${NC}"
        else
            echo -e "${RED}❌ External API not accessible${NC}"
            return 1
        fi
    fi
    
    # Test internal services
    echo "Testing internal service communication..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend-$NEW_COLOR -- curl -f -s http://onecrm-backend-service:8000/health >/dev/null; then
        echo -e "${GREEN}✅ Internal service communication working${NC}"
    else
        echo -e "${RED}❌ Internal service communication failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All production smoke tests passed${NC}"
}

# Scale down old deployment
scale_down_old_deployment() {
    print_section "📉 Scaling Down Old Deployment"
    
    if [ "$CURRENT_COLOR" != "none" ]; then
        echo "Scaling down $CURRENT_COLOR deployment..."
        
        kubectl scale deployment onecrm-backend-$CURRENT_COLOR -n $NAMESPACE --replicas=0
        kubectl scale deployment onecrm-frontend-$CURRENT_COLOR -n $NAMESPACE --replicas=0
        
        echo -e "${GREEN}✅ Old deployment ($CURRENT_COLOR) scaled down${NC}"
    else
        echo "No previous deployment to scale down"
    fi
}

# Cleanup old deployment (optional)
cleanup_old_deployment() {
    if [ "$ROLLBACK_ENABLED" = "false" ] && [ "$CURRENT_COLOR" != "none" ]; then
        print_section "🧹 Cleaning Up Old Deployment"
        
        echo "Deleting old $CURRENT_COLOR deployment..."
        kubectl delete deployment onecrm-backend-$CURRENT_COLOR -n $NAMESPACE --ignore-not-found=true
        kubectl delete deployment onecrm-frontend-$CURRENT_COLOR -n $NAMESPACE --ignore-not-found=true
        
        echo -e "${GREEN}✅ Old deployment cleaned up${NC}"
    else
        echo -e "${YELLOW}⚠️  Keeping old deployment for potential rollback${NC}"
    fi
}

# Rollback function
rollback_deployment() {
    print_section "🔄 Rolling Back Deployment"
    
    if [ "$CURRENT_COLOR" = "none" ]; then
        echo -e "${RED}❌ No previous deployment to rollback to${NC}"
        exit 1
    fi
    
    echo "Rolling back to $CURRENT_COLOR deployment..."
    
    # Scale up old deployment
    kubectl scale deployment onecrm-backend-$CURRENT_COLOR -n $NAMESPACE --replicas=3
    kubectl scale deployment onecrm-frontend-$CURRENT_COLOR -n $NAMESPACE --replicas=2
    
    # Wait for old deployment to be ready
    kubectl rollout status deployment/onecrm-backend-$CURRENT_COLOR -n $NAMESPACE --timeout=300s
    kubectl rollout status deployment/onecrm-frontend-$CURRENT_COLOR -n $NAMESPACE --timeout=300s
    
    # Switch traffic back
    kubectl patch service onecrm-backend-service -n $NAMESPACE -p '{"spec":{"selector":{"app":"onecrm-backend-'$CURRENT_COLOR'"}}}'
    kubectl patch service onecrm-frontend-service -n $NAMESPACE -p '{"spec":{"selector":{"app":"onecrm-frontend-'$CURRENT_COLOR'"}}}'
    
    # Scale down failed deployment
    kubectl scale deployment onecrm-backend-$NEW_COLOR -n $NAMESPACE --replicas=0
    kubectl scale deployment onecrm-frontend-$NEW_COLOR -n $NAMESPACE --replicas=0
    
    echo -e "${GREEN}✅ Rollback completed successfully${NC}"
}

# Monitor deployment
monitor_deployment() {
    print_section "📊 Monitoring Deployment"
    
    echo "Deployment status:"
    kubectl get deployments -n $NAMESPACE
    
    echo -e "\nPod status:"
    kubectl get pods -n $NAMESPACE
    
    echo -e "\nService endpoints:"
    kubectl get endpoints -n $NAMESPACE
    
    echo -e "\nResource usage:"
    kubectl top pods -n $NAMESPACE 2>/dev/null || echo "Metrics server not available"
}

# Main deployment function
main() {
    case "${3:-deploy}" in
        "deploy")
            echo -e "${BLUE}Starting production deployment...${NC}"
            
            pre_deployment_checks
            determine_deployment_colors
            build_production_images
            deploy_new_version
            
            if health_check_new_deployment; then
                switch_traffic
                if run_production_smoke_tests; then
                    scale_down_old_deployment
                    cleanup_old_deployment
                    monitor_deployment
                    echo -e "\n${GREEN}🎉 Production deployment completed successfully!${NC}"
                else
                    echo -e "\n${RED}💥 Production smoke tests failed. Rolling back...${NC}"
                    rollback_deployment
                    exit 1
                fi
            else
                echo -e "\n${RED}💥 Health checks failed. Rolling back...${NC}"
                rollback_deployment
                exit 1
            fi
            ;;
        "rollback")
            determine_deployment_colors
            rollback_deployment
            ;;
        "status")
            monitor_deployment
            ;;
        *)
            echo "Usage: $0 <tag> <strategy> <action>"
            echo "  tag       - Docker image tag (default: latest)"
            echo "  strategy  - Deployment strategy (default: blue-green)"
            echo "  action    - deploy|rollback|status (default: deploy)"
            echo ""
            echo "Examples:"
            echo "  $0 v1.0.0 blue-green deploy"
            echo "  $0 latest blue-green rollback"
            echo "  $0 latest blue-green status"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
