#!/bin/bash

# OneCRM SSO Integration Test Script
# This script tests the complete SSO integration flow

set -e

KEYCLOAK_URL="${KEYCLOAK_URL:-http://localhost:8080}"
API_URL="${API_URL:-http://localhost:8000}"
FRONTEND_URL="${FRONTEND_URL:-http://localhost:3000}"
REALM_NAME="onecrm"

echo "🧪 Testing OneCRM SSO Integration..."

# Function to test Keycloak availability
test_keycloak_availability() {
    echo "🔐 Testing Keycloak availability..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$KEYCLOAK_URL/realms/master")
    
    if [ "$response" = "200" ]; then
        echo "✅ Keycloak is available"
        return 0
    else
        echo "❌ Keycloak is not available (HTTP $response)"
        return 1
    fi
}

# Function to test realm configuration
test_realm_configuration() {
    echo "🏗️  Testing realm configuration..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$KEYCLOAK_URL/realms/$REALM_NAME/.well-known/openid_configuration")
    
    if [ "$response" = "200" ]; then
        echo "✅ OneCRM realm is configured"
        
        # Test OIDC endpoints
        local oidc_config=$(curl -s "$KEYCLOAK_URL/realms/$REALM_NAME/.well-known/openid_configuration")
        
        if echo "$oidc_config" | grep -q "authorization_endpoint"; then
            echo "✅ OIDC endpoints are available"
        else
            echo "❌ OIDC endpoints are not properly configured"
            return 1
        fi
        
        return 0
    else
        echo "❌ OneCRM realm is not configured (HTTP $response)"
        return 1
    fi
}

# Function to test API health
test_api_health() {
    echo "🏥 Testing API health..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/health")
    
    if [ "$response" = "200" ]; then
        echo "✅ API is healthy"
        
        # Test API response
        local health_data=$(curl -s "$API_URL/api/health")
        
        if echo "$health_data" | grep -q '"status":"ok"'; then
            echo "✅ API health check passed"
        else
            echo "❌ API health check failed"
            return 1
        fi
        
        return 0
    else
        echo "❌ API is not healthy (HTTP $response)"
        return 1
    fi
}

# Function to test Kong Gateway
test_kong_gateway() {
    echo "🦍 Testing Kong Gateway..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/health")
    
    if [ "$response" = "200" ]; then
        echo "✅ Kong Gateway is routing requests"
        
        # Test CORS headers
        local cors_response=$(curl -s -I -X OPTIONS "$API_URL/api/health")
        
        if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
            echo "✅ CORS headers are present"
        else
            echo "⚠️  CORS headers may not be configured"
        fi
        
        return 0
    else
        echo "❌ Kong Gateway is not working (HTTP $response)"
        return 1
    fi
}

# Function to test authentication endpoints
test_auth_endpoints() {
    echo "🔑 Testing authentication endpoints..."
    
    # Test protected endpoint without token (should return 401 in production)
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/users/profile")
    
    if [ "$response" = "401" ]; then
        echo "✅ Protected endpoints require authentication"
    elif [ "$response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ Authentication bypassed in development mode"
    else
        echo "⚠️  Unexpected response from protected endpoint (HTTP $response)"
    fi
    
    # Test public endpoint
    local health_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/health")
    
    if [ "$health_response" = "200" ]; then
        echo "✅ Public endpoints are accessible"
    else
        echo "❌ Public endpoints are not accessible (HTTP $health_response)"
        return 1
    fi
}

# Function to test frontend availability
test_frontend_availability() {
    echo "🌐 Testing frontend availability..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL")
    
    if [ "$response" = "200" ]; then
        echo "✅ Frontend is available"
        
        # Test silent SSO check file
        local sso_response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL/silent-check-sso.html")
        
        if [ "$sso_response" = "200" ]; then
            echo "✅ Silent SSO check file is available"
        else
            echo "❌ Silent SSO check file is missing (HTTP $sso_response)"
            return 1
        fi
        
        return 0
    else
        echo "❌ Frontend is not available (HTTP $response)"
        return 1
    fi
}

# Function to test token validation
test_token_validation() {
    echo "🎫 Testing token validation..."
    
    # Test with invalid token
    local invalid_response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer invalid-token" \
        "$API_URL/api/users/profile")
    
    if [ "$invalid_response" = "401" ]; then
        echo "✅ Invalid tokens are rejected"
    elif [ "$invalid_response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ Token validation bypassed in development mode"
    else
        echo "⚠️  Unexpected response for invalid token (HTTP $invalid_response)"
    fi
}

# Function to test multi-tenancy
test_multi_tenancy() {
    echo "🏢 Testing multi-tenancy..."
    
    # Test with org header
    local org_response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "X-Org-Id: test-org-id" \
        "$API_URL/api/organizations/test-org-id")
    
    if [ "$org_response" = "401" ]; then
        echo "✅ Organization endpoints require authentication"
    elif [ "$org_response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ Multi-tenancy working in development mode"
    else
        echo "⚠️  Unexpected response for organization endpoint (HTTP $org_response)"
    fi
}

# Function to display test results
display_results() {
    echo ""
    echo "🎉 SSO Integration Test Complete!"
    echo ""
    echo "📋 Test Summary:"
    echo "- Keycloak: $keycloak_status"
    echo "- Realm Config: $realm_status"
    echo "- API Health: $api_status"
    echo "- Kong Gateway: $kong_status"
    echo "- Auth Endpoints: $auth_status"
    echo "- Frontend: $frontend_status"
    echo "- Token Validation: $token_status"
    echo "- Multi-tenancy: $tenant_status"
    echo ""
    
    if [ "$overall_status" = "PASS" ]; then
        echo "✅ All critical tests passed!"
        echo ""
        echo "🔗 Access URLs:"
        echo "- Frontend: $FRONTEND_URL"
        echo "- API: $API_URL"
        echo "- Keycloak: $KEYCLOAK_URL"
        echo "- API Docs: $API_URL/api/docs"
    else
        echo "❌ Some tests failed. Please check the logs above."
        exit 1
    fi
}

# Main execution
main() {
    local overall_status="PASS"
    
    # Run tests
    if test_keycloak_availability; then
        keycloak_status="✅ PASS"
    else
        keycloak_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_realm_configuration; then
        realm_status="✅ PASS"
    else
        realm_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_api_health; then
        api_status="✅ PASS"
    else
        api_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_kong_gateway; then
        kong_status="✅ PASS"
    else
        kong_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_auth_endpoints; then
        auth_status="✅ PASS"
    else
        auth_status="⚠️  WARN"
    fi
    
    if test_frontend_availability; then
        frontend_status="✅ PASS"
    else
        frontend_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_token_validation; then
        token_status="✅ PASS"
    else
        token_status="⚠️  WARN"
    fi
    
    if test_multi_tenancy; then
        tenant_status="✅ PASS"
    else
        tenant_status="⚠️  WARN"
    fi
    
    # Display results
    display_results
}

# Run main function
main "$@"
