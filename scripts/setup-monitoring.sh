#!/bin/bash

# OneCRM Monitoring Setup Script
# Sets up comprehensive monitoring, logging, and alerting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

ENVIRONMENT=${1:-production}
NAMESPACE="onecrm-monitoring"

echo -e "${BLUE}📊 OneCRM Monitoring Setup${NC}"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Create monitoring namespace
create_monitoring_namespace() {
    print_section "📁 Creating Monitoring Namespace"
    
    if kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Namespace $NAMESPACE already exists${NC}"
    else
        kubectl create namespace $NAMESPACE
        echo -e "${GREEN}✅ Namespace $NAMESPACE created${NC}"
    fi
}

# Deploy Prometheus
deploy_prometheus() {
    print_section "📈 Deploying Prometheus"
    
    # Create Prometheus ConfigMap
    kubectl create configmap prometheus-config \
        --from-file=infrastructure/monitoring/prometheus-config.yaml \
        --from-file=infrastructure/monitoring/onecrm-alerts.yaml \
        -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Prometheus
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: $NAMESPACE
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        ports:
        - containerPort: 9090
        args:
          - '--config.file=/etc/prometheus/prometheus-config.yaml'
          - '--storage.tsdb.path=/prometheus'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--web.enable-lifecycle'
          - '--storage.tsdb.retention.time=30d'
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
        - name: storage
          mountPath: /prometheus
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: prometheus-config
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: $NAMESPACE
  labels:
    app: prometheus
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: $NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: $NAMESPACE
EOF

    echo -e "${GREEN}✅ Prometheus deployed${NC}"
}

# Deploy Grafana
deploy_grafana() {
    print_section "📊 Deploying Grafana"
    
    # Create Grafana dashboard ConfigMap
    kubectl create configmap grafana-dashboards \
        --from-file=infrastructure/monitoring/grafana-dashboards.json \
        -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Grafana
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: $NAMESPACE
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.0.3
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_USER
          value: "admin"
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: dashboards
          mountPath: /etc/grafana/provisioning/dashboards
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: grafana-storage
        emptyDir: {}
      - name: dashboards
        configMap:
          name: grafana-dashboards
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: $NAMESPACE
  labels:
    app: grafana
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: $NAMESPACE
type: Opaque
stringData:
  admin-password: "$(openssl rand -base64 16)"
EOF

    echo -e "${GREEN}✅ Grafana deployed${NC}"
}

# Deploy Alertmanager
deploy_alertmanager() {
    print_section "🚨 Deploying Alertmanager"
    
    # Create Alertmanager config
    cat << EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: $NAMESPACE
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'localhost:587'
      smtp_from: '<EMAIL>'
    
    route:
      group_by: ['alertname']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'web.hook'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
      - match:
          severity: warning
        receiver: 'warning-alerts'
    
    receivers:
    - name: 'web.hook'
      webhook_configs:
      - url: 'http://localhost:5001/'
    
    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: OneCRM Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
      slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts'
        title: 'CRITICAL: OneCRM Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    
    - name: 'warning-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'WARNING: OneCRM Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: $NAMESPACE
  labels:
    app: alertmanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.25.0
        ports:
        - containerPort: 9093
        args:
          - '--config.file=/etc/alertmanager/alertmanager.yml'
          - '--storage.path=/alertmanager'
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
        - name: storage
          mountPath: /alertmanager
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: config
        configMap:
          name: alertmanager-config
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: $NAMESPACE
  labels:
    app: alertmanager
spec:
  selector:
    app: alertmanager
  ports:
  - port: 9093
    targetPort: 9093
  type: ClusterIP
EOF

    echo -e "${GREEN}✅ Alertmanager deployed${NC}"
}

# Deploy Loki for log aggregation
deploy_loki() {
    print_section "📝 Deploying Loki"
    
    cat << EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-config
  namespace: $NAMESPACE
data:
  loki.yaml: |
    auth_enabled: false
    
    server:
      http_listen_port: 3100
    
    ingester:
      lifecycler:
        address: 127.0.0.1
        ring:
          kvstore:
            store: inmemory
          replication_factor: 1
        final_sleep: 0s
      chunk_idle_period: 5m
      chunk_retain_period: 30s
    
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 168h
    
    storage_config:
      boltdb:
        directory: /loki/index
      filesystem:
        directory: /loki/chunks
    
    limits_config:
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loki
  namespace: $NAMESPACE
  labels:
    app: loki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: loki
  template:
    metadata:
      labels:
        app: loki
    spec:
      containers:
      - name: loki
        image: grafana/loki:2.8.4
        ports:
        - containerPort: 3100
        args:
          - '-config.file=/etc/loki/loki.yaml'
        volumeMounts:
        - name: config
          mountPath: /etc/loki
        - name: storage
          mountPath: /loki
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: loki-config
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: loki
  namespace: $NAMESPACE
  labels:
    app: loki
spec:
  selector:
    app: loki
  ports:
  - port: 3100
    targetPort: 3100
  type: ClusterIP
EOF

    echo -e "${GREEN}✅ Loki deployed${NC}"
}

# Wait for deployments to be ready
wait_for_deployments() {
    print_section "⏳ Waiting for Deployments"
    
    echo "Waiting for Prometheus..."
    kubectl rollout status deployment/prometheus -n $NAMESPACE --timeout=300s
    
    echo "Waiting for Grafana..."
    kubectl rollout status deployment/grafana -n $NAMESPACE --timeout=300s
    
    echo "Waiting for Alertmanager..."
    kubectl rollout status deployment/alertmanager -n $NAMESPACE --timeout=300s
    
    echo "Waiting for Loki..."
    kubectl rollout status deployment/loki -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ All monitoring components are ready${NC}"
}

# Display access information
show_access_info() {
    print_section "🔗 Access Information"
    
    echo "To access monitoring services, use port forwarding:"
    echo ""
    echo "Prometheus:"
    echo "  kubectl port-forward -n $NAMESPACE svc/prometheus 9090:9090"
    echo "  http://localhost:9090"
    echo ""
    echo "Grafana:"
    echo "  kubectl port-forward -n $NAMESPACE svc/grafana 3000:3000"
    echo "  http://localhost:3000"
    echo "  Username: admin"
    echo "  Password: $(kubectl get secret grafana-secrets -n $NAMESPACE -o jsonpath='{.data.admin-password}' | base64 -d)"
    echo ""
    echo "Alertmanager:"
    echo "  kubectl port-forward -n $NAMESPACE svc/alertmanager 9093:9093"
    echo "  http://localhost:9093"
    echo ""
    echo "Loki:"
    echo "  kubectl port-forward -n $NAMESPACE svc/loki 3100:3100"
    echo "  http://localhost:3100"
}

# Main function
main() {
    echo -e "${BLUE}Setting up monitoring infrastructure...${NC}"
    
    create_monitoring_namespace
    deploy_prometheus
    deploy_grafana
    deploy_alertmanager
    deploy_loki
    wait_for_deployments
    show_access_info
    
    echo -e "\n${GREEN}🎉 Monitoring setup completed successfully!${NC}"
    echo -e "${YELLOW}💡 Configure Grafana data sources and import dashboards${NC}"
}

# Run main function
main
