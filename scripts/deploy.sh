#!/bin/bash

# OneCRM Production Deployment Script
# This script handles the complete deployment process for OneCRM

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-staging}
TAG=${2:-latest}
NAMESPACE="onecrm"
STAGING_NAMESPACE="onecrm-staging"

if [ "$ENVIRONMENT" = "staging" ]; then
    NAMESPACE=$STAGING_NAMESPACE
fi

echo -e "${BLUE}🚀 OneCRM Deployment Script${NC}"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Tag: $TAG"
echo "Namespace: $NAMESPACE"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_section "🔍 Checking Prerequisites"
    
    # Check Docker
    if ! command_exists docker; then
        echo -e "${RED}❌ Docker is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1)${NC}"
    
    # Check kubectl
    if ! command_exists kubectl; then
        echo -e "${RED}❌ kubectl is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ kubectl $(kubectl version --client --short | cut -d' ' -f3)${NC}"
    
    # Check Kubernetes connection
    if ! kubectl cluster-info >/dev/null 2>&1; then
        echo -e "${RED}❌ Cannot connect to Kubernetes cluster${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Kubernetes cluster connection${NC}"
    
    # Check Helm (optional)
    if command_exists helm; then
        echo -e "${GREEN}✅ Helm $(helm version --short)${NC}"
    else
        echo -e "${YELLOW}⚠️  Helm not found (optional)${NC}"
    fi
}

# Build Docker images
build_images() {
    print_section "🏗️  Building Docker Images"
    
    echo "Building backend image..."
    docker build -t onecrm/backend:$TAG -f backend/Dockerfile.prod backend/
    
    echo "Building frontend image..."
    docker build -t onecrm/frontend:$TAG -f frontend/Dockerfile.prod frontend/
    
    echo -e "${GREEN}✅ Images built successfully${NC}"
}

# Push images to registry
push_images() {
    print_section "📤 Pushing Images to Registry"
    
    # Tag images for registry
    docker tag onecrm/backend:$TAG $DOCKER_REGISTRY/onecrm/backend:$TAG
    docker tag onecrm/frontend:$TAG $DOCKER_REGISTRY/onecrm/frontend:$TAG
    
    # Push images
    echo "Pushing backend image..."
    docker push $DOCKER_REGISTRY/onecrm/backend:$TAG
    
    echo "Pushing frontend image..."
    docker push $DOCKER_REGISTRY/onecrm/frontend:$TAG
    
    echo -e "${GREEN}✅ Images pushed successfully${NC}"
}

# Create namespace if it doesn't exist
create_namespace() {
    print_section "📁 Setting Up Namespace"
    
    if kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Namespace $NAMESPACE already exists${NC}"
    else
        kubectl apply -f infrastructure/kubernetes/namespace.yaml
        echo -e "${GREEN}✅ Namespace $NAMESPACE created${NC}"
    fi
}

# Deploy secrets
deploy_secrets() {
    print_section "🔐 Deploying Secrets"
    
    # Check if secrets file exists
    if [ ! -f "infrastructure/kubernetes/secrets-$ENVIRONMENT.yaml" ]; then
        echo -e "${RED}❌ Secrets file not found: infrastructure/kubernetes/secrets-$ENVIRONMENT.yaml${NC}"
        echo "Please create the secrets file with the following template:"
        cat << EOF
apiVersion: v1
kind: Secret
metadata:
  name: onecrm-secrets
  namespace: $NAMESPACE
type: Opaque
stringData:
  database-url: "****************************************/database"
  redis-url: "redis://username:password@host:6379"
  jwt-secret: "your-jwt-secret-here"
  keycloak-secret: "your-keycloak-client-secret"
EOF
        exit 1
    fi
    
    kubectl apply -f infrastructure/kubernetes/secrets-$ENVIRONMENT.yaml
    echo -e "${GREEN}✅ Secrets deployed${NC}"
}

# Deploy configuration
deploy_config() {
    print_section "⚙️  Deploying Configuration"
    
    kubectl apply -f infrastructure/kubernetes/configmap.yaml
    echo -e "${GREEN}✅ Configuration deployed${NC}"
}

# Deploy database
deploy_database() {
    print_section "🗄️  Deploying Database"
    
    # Deploy PostgreSQL
    kubectl apply -f infrastructure/kubernetes/postgres-deployment.yaml
    
    # Wait for database to be ready
    echo "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Database deployed${NC}"
}

# Deploy Redis
deploy_redis() {
    print_section "🔴 Deploying Redis"
    
    kubectl apply -f infrastructure/kubernetes/redis-deployment.yaml
    
    # Wait for Redis to be ready
    echo "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Redis deployed${NC}"
}

# Deploy Keycloak
deploy_keycloak() {
    print_section "🔑 Deploying Keycloak"
    
    kubectl apply -f infrastructure/kubernetes/keycloak-deployment.yaml
    
    # Wait for Keycloak to be ready
    echo "Waiting for Keycloak to be ready..."
    kubectl wait --for=condition=ready pod -l app=keycloak -n $NAMESPACE --timeout=600s
    
    echo -e "${GREEN}✅ Keycloak deployed${NC}"
}

# Run database migrations
run_migrations() {
    print_section "🔄 Running Database Migrations"
    
    # Create migration job
    kubectl apply -f infrastructure/kubernetes/migration-job.yaml
    
    # Wait for migration to complete
    echo "Waiting for migrations to complete..."
    kubectl wait --for=condition=complete job/onecrm-migration -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Migrations completed${NC}"
}

# Deploy backend
deploy_backend() {
    print_section "🖥️  Deploying Backend"
    
    # Update image tag in deployment
    sed "s|onecrm/backend:latest|onecrm/backend:$TAG|g" infrastructure/kubernetes/backend-deployment.yaml | kubectl apply -f -
    
    # Wait for deployment to be ready
    echo "Waiting for backend deployment to be ready..."
    kubectl rollout status deployment/onecrm-backend -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Backend deployed${NC}"
}

# Deploy frontend
deploy_frontend() {
    print_section "🌐 Deploying Frontend"
    
    # Update image tag in deployment
    sed "s|onecrm/frontend:latest|onecrm/frontend:$TAG|g" infrastructure/kubernetes/frontend-deployment.yaml | kubectl apply -f -
    
    # Wait for deployment to be ready
    echo "Waiting for frontend deployment to be ready..."
    kubectl rollout status deployment/onecrm-frontend -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Frontend deployed${NC}"
}

# Deploy ingress
deploy_ingress() {
    print_section "🌍 Deploying Ingress"
    
    kubectl apply -f infrastructure/kubernetes/ingress.yaml
    
    echo -e "${GREEN}✅ Ingress deployed${NC}"
}

# Run health checks
run_health_checks() {
    print_section "🏥 Running Health Checks"
    
    echo "Checking backend health..."
    kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f http://localhost:8000/health
    
    echo "Checking frontend health..."
    kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q http://localhost:3000/api/health
    
    echo -e "${GREEN}✅ Health checks passed${NC}"
}

# Display deployment status
show_status() {
    print_section "📊 Deployment Status"
    
    echo "Pods:"
    kubectl get pods -n $NAMESPACE
    
    echo -e "\nServices:"
    kubectl get services -n $NAMESPACE
    
    echo -e "\nIngress:"
    kubectl get ingress -n $NAMESPACE
    
    echo -e "\nDeployment URLs:"
    if [ "$ENVIRONMENT" = "production" ]; then
        echo "Frontend: https://onecrm.example.com"
        echo "API: https://api.onecrm.example.com"
        echo "Auth: https://auth.onecrm.example.com"
    else
        echo "Frontend: https://staging.onecrm.example.com"
        echo "API: https://api-staging.onecrm.example.com"
        echo "Auth: https://auth-staging.onecrm.example.com"
    fi
}

# Rollback function
rollback() {
    print_section "🔄 Rolling Back Deployment"
    
    echo "Rolling back backend..."
    kubectl rollout undo deployment/onecrm-backend -n $NAMESPACE
    
    echo "Rolling back frontend..."
    kubectl rollout undo deployment/onecrm-frontend -n $NAMESPACE
    
    echo -e "${GREEN}✅ Rollback completed${NC}"
}

# Main deployment function
deploy() {
    echo -e "${BLUE}Starting deployment to $ENVIRONMENT environment...${NC}"
    
    check_prerequisites
    
    if [ "$SKIP_BUILD" != "true" ]; then
        build_images
        
        if [ -n "$DOCKER_REGISTRY" ]; then
            push_images
        fi
    fi
    
    create_namespace
    deploy_secrets
    deploy_config
    deploy_database
    deploy_redis
    deploy_keycloak
    run_migrations
    deploy_backend
    deploy_frontend
    deploy_ingress
    run_health_checks
    show_status
    
    echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "rollback")
        rollback
        ;;
    "status")
        show_status
        ;;
    "health")
        run_health_checks
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|status|health} [environment] [tag]"
        echo "  deploy    - Deploy the application"
        echo "  rollback  - Rollback to previous version"
        echo "  status    - Show deployment status"
        echo "  health    - Run health checks"
        echo ""
        echo "Environment: staging (default) | production"
        echo "Tag: Docker image tag (default: latest)"
        exit 1
        ;;
esac
