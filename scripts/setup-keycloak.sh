#!/bin/bash

# OneCRM Keycloak Setup Script
# This script configures Keycloak realm and clients for OneCRM

set -e

KEYCLOAK_URL="${KEYCLOAK_URL:-http://localhost:8080}"
KEYCLOAK_ADMIN_USER="${KEYCLOAK_ADMIN_USER:-admin}"
KEYCLOAK_ADMIN_PASSWORD="${KEYCLOAK_ADMIN_PASSWORD:-admin}"
REALM_NAME="onecrm"

echo "🔐 Setting up Keycloak realm for OneCRM..."

# Function to wait for Keycloak to be ready
wait_for_keycloak() {
    echo "⏳ Waiting for Keycloak to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$KEYCLOAK_URL/realms/master" > /dev/null 2>&1; then
            echo "✅ Keycloak is ready"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: Keycloak not ready yet..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ Keycloak failed to start within expected time"
    return 1
}

# Function to get admin access token
get_admin_token() {
    echo "🔑 Getting admin access token..."
    
    local response=$(curl -s -X POST "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$KEYCLOAK_ADMIN_USER" \
        -d "password=$KEYCLOAK_ADMIN_PASSWORD" \
        -d "grant_type=password" \
        -d "client_id=admin-cli")
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to get admin token"
        return 1
    fi
    
    echo "$response" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4
}

# Function to check if realm exists
realm_exists() {
    local token=$1
    local response=$(curl -s -H "Authorization: Bearer $token" \
        "$KEYCLOAK_URL/admin/realms/$REALM_NAME")
    
    if echo "$response" | grep -q '"realm"'; then
        return 0
    else
        return 1
    fi
}

# Function to create realm
create_realm() {
    local token=$1
    echo "🏗️  Creating OneCRM realm..."
    
    local response=$(curl -s -X POST "$KEYCLOAK_URL/admin/realms" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d @keycloak/realm-config/onecrm-realm.json)
    
    if [ $? -eq 0 ]; then
        echo "✅ OneCRM realm created successfully"
        return 0
    else
        echo "❌ Failed to create realm: $response"
        return 1
    fi
}

# Function to update realm
update_realm() {
    local token=$1
    echo "🔄 Updating OneCRM realm..."
    
    local response=$(curl -s -X PUT "$KEYCLOAK_URL/admin/realms/$REALM_NAME" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d @keycloak/realm-config/onecrm-realm.json)
    
    if [ $? -eq 0 ]; then
        echo "✅ OneCRM realm updated successfully"
        return 0
    else
        echo "❌ Failed to update realm: $response"
        return 1
    fi
}

# Function to create test user
create_test_user() {
    local token=$1
    echo "👤 Creating test user..."
    
    local user_data='{
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "firstName": "Test",
        "lastName": "User",
        "enabled": true,
        "emailVerified": true,
        "credentials": [
            {
                "type": "password",
                "value": "test123",
                "temporary": false
            }
        ],
        "realmRoles": ["user"],
        "groups": ["/default-org"],
        "attributes": {
            "org_id": ["default-org-id"],
            "role": ["user"]
        }
    }'
    
    local response=$(curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d "$user_data")
    
    if [ $? -eq 0 ]; then
        echo "✅ Test user created successfully"
        return 0
    else
        echo "ℹ️  Test user may already exist or creation failed: $response"
        return 0
    fi
}

# Function to display configuration info
display_config_info() {
    echo ""
    echo "🎉 Keycloak setup complete!"
    echo ""
    echo "📋 Configuration Details:"
    echo "- Keycloak URL: $KEYCLOAK_URL"
    echo "- Realm: $REALM_NAME"
    echo "- Admin Console: $KEYCLOAK_URL/admin"
    echo "- Realm Console: $KEYCLOAK_URL/admin/master/console/#/onecrm"
    echo ""
    echo "🔑 Client Configurations:"
    echo "- Frontend Client ID: onecrm-frontend"
    echo "- Backend Client ID: onecrm-backend"
    echo ""
    echo "👥 Test Users:"
    echo "- Admin: <EMAIL> / admin123"
    echo "- User: <EMAIL> / test123"
    echo ""
    echo "🔗 OIDC Endpoints:"
    echo "- Discovery: $KEYCLOAK_URL/realms/$REALM_NAME/.well-known/openid_configuration"
    echo "- Auth: $KEYCLOAK_URL/realms/$REALM_NAME/protocol/openid-connect/auth"
    echo "- Token: $KEYCLOAK_URL/realms/$REALM_NAME/protocol/openid-connect/token"
    echo "- UserInfo: $KEYCLOAK_URL/realms/$REALM_NAME/protocol/openid-connect/userinfo"
    echo ""
}

# Main execution
main() {
    # Check if realm config file exists
    if [ ! -f "keycloak/realm-config/onecrm-realm.json" ]; then
        echo "❌ Realm configuration file not found: keycloak/realm-config/onecrm-realm.json"
        exit 1
    fi
    
    # Wait for Keycloak to be ready
    if ! wait_for_keycloak; then
        exit 1
    fi
    
    # Get admin token
    local admin_token=$(get_admin_token)
    if [ -z "$admin_token" ]; then
        echo "❌ Failed to get admin token"
        exit 1
    fi
    
    # Check if realm exists and create/update accordingly
    if realm_exists "$admin_token"; then
        echo "ℹ️  Realm '$REALM_NAME' already exists"
        update_realm "$admin_token"
    else
        create_realm "$admin_token"
    fi
    
    # Create test user
    create_test_user "$admin_token"
    
    # Display configuration info
    display_config_info
}

# Run main function
main "$@"
