#!/bin/bash

# OneCRM API Gateway Test Script
# This script tests the complete API flow through Kong Gateway

set -e

KONG_PROXY_URL="${KONG_PROXY_URL:-http://localhost:8000}"
KONG_ADMIN_URL="${KONG_ADMIN_URL:-http://localhost:8001}"
API_BASE_URL="$KONG_PROXY_URL/api"

echo "🧪 Testing OneCRM API Gateway Integration..."

# Function to test Kong Gateway health
test_kong_health() {
    echo "🦍 Testing Kong Gateway health..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_ADMIN_URL/status")
    
    if [ "$response" = "200" ]; then
        echo "✅ Kong Gateway is healthy"
        
        # Get Kong status details
        local status=$(curl -s "$KONG_ADMIN_URL/status")
        echo "📊 Kong Status: $(echo "$status" | jq -r '.database.reachable' 2>/dev/null || echo 'Unknown')"
        
        return 0
    else
        echo "❌ Kong Gateway is not healthy (HTTP $response)"
        return 1
    fi
}

# Function to test API routing through Kong
test_api_routing() {
    echo "🔀 Testing API routing through Kong..."
    
    # Test health endpoint through Kong
    local health_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/health")
    
    if [ "$health_response" = "200" ]; then
        echo "✅ API routing through Kong is working"
        
        # Get health data
        local health_data=$(curl -s "$API_BASE_URL/health")
        echo "📊 API Health: $(echo "$health_data" | jq -r '.status' 2>/dev/null || echo 'Unknown')"
        
        return 0
    else
        echo "❌ API routing through Kong failed (HTTP $health_response)"
        return 1
    fi
}

# Function to test CORS headers
test_cors_headers() {
    echo "🌐 Testing CORS headers..."
    
    local cors_response=$(curl -s -I -X OPTIONS \
        -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Authorization" \
        "$API_BASE_URL/health")
    
    if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
        echo "✅ CORS headers are present"
        
        # Check specific CORS headers
        if echo "$cors_response" | grep -q "Access-Control-Allow-Credentials: true"; then
            echo "✅ CORS credentials allowed"
        else
            echo "⚠️  CORS credentials may not be configured"
        fi
        
        return 0
    else
        echo "❌ CORS headers are missing"
        return 1
    fi
}

# Function to test rate limiting
test_rate_limiting() {
    echo "⏱️  Testing rate limiting..."
    
    local rate_limit_response=$(curl -s -I "$API_BASE_URL/health")
    
    if echo "$rate_limit_response" | grep -q "X-RateLimit"; then
        echo "✅ Rate limiting headers are present"
        
        # Extract rate limit info
        local remaining=$(echo "$rate_limit_response" | grep -i "x-ratelimit-remaining" | cut -d' ' -f2 | tr -d '\r')
        local limit=$(echo "$rate_limit_response" | grep -i "x-ratelimit-limit" | cut -d' ' -f2 | tr -d '\r')
        
        if [ -n "$remaining" ] && [ -n "$limit" ]; then
            echo "📊 Rate Limit: $remaining/$limit remaining"
        fi
        
        return 0
    else
        echo "⚠️  Rate limiting headers not found (may not be configured)"
        return 0
    fi
}

# Function to test authentication endpoints
test_auth_endpoints() {
    echo "🔐 Testing authentication endpoints..."
    
    # Test protected endpoint without token
    local unauth_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/users/profile")
    
    if [ "$unauth_response" = "401" ]; then
        echo "✅ Protected endpoints require authentication"
    elif [ "$unauth_response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ Authentication bypassed in development mode"
    else
        echo "⚠️  Unexpected response from protected endpoint (HTTP $unauth_response)"
    fi
    
    # Test with invalid token
    local invalid_token_response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer invalid-token" \
        "$API_BASE_URL/users/profile")
    
    if [ "$invalid_token_response" = "401" ]; then
        echo "✅ Invalid tokens are rejected"
    elif [ "$invalid_token_response" = "200" ] && [ "$NODE_ENV" = "development" ]; then
        echo "✅ Token validation bypassed in development mode"
    else
        echo "⚠️  Unexpected response for invalid token (HTTP $invalid_token_response)"
    fi
}

# Function to test request/response transformation
test_transformations() {
    echo "🔄 Testing request/response transformations..."
    
    local response=$(curl -s -I "$API_BASE_URL/health")
    
    # Check for Kong request ID
    if echo "$response" | grep -q "Kong-Request-ID"; then
        echo "✅ Request ID transformation is working"
    else
        echo "⚠️  Request ID transformation not found"
    fi
    
    # Check for security headers
    if echo "$response" | grep -q "X-Content-Type-Options"; then
        echo "✅ Security headers are added"
    else
        echo "⚠️  Security headers not found"
    fi
    
    # Check for upstream latency
    if echo "$response" | grep -q "X-Kong-Upstream-Latency"; then
        echo "✅ Upstream latency header is present"
    else
        echo "⚠️  Upstream latency header not found"
    fi
}

# Function to test service discovery
test_service_discovery() {
    echo "🔍 Testing service discovery..."
    
    # List Kong services
    local services=$(curl -s "$KONG_ADMIN_URL/services")
    local service_count=$(echo "$services" | jq -r '.data | length' 2>/dev/null || echo "0")
    
    echo "📊 Configured services: $service_count"
    
    if [ "$service_count" -gt "0" ]; then
        echo "✅ Services are configured"
        
        # List service names
        echo "📋 Services:"
        echo "$services" | jq -r '.data[].name' 2>/dev/null || echo "Unable to parse service names"
    else
        echo "❌ No services configured"
        return 1
    fi
    
    # List Kong routes
    local routes=$(curl -s "$KONG_ADMIN_URL/routes")
    local route_count=$(echo "$routes" | jq -r '.data | length' 2>/dev/null || echo "0")
    
    echo "📊 Configured routes: $route_count"
    
    if [ "$route_count" -gt "0" ]; then
        echo "✅ Routes are configured"
        
        # List route names
        echo "📋 Routes:"
        echo "$routes" | jq -r '.data[].name' 2>/dev/null || echo "Unable to parse route names"
    else
        echo "❌ No routes configured"
        return 1
    fi
}

# Function to test plugin configuration
test_plugins() {
    echo "🔌 Testing plugin configuration..."
    
    local plugins=$(curl -s "$KONG_ADMIN_URL/plugins")
    local plugin_count=$(echo "$plugins" | jq -r '.data | length' 2>/dev/null || echo "0")
    
    echo "📊 Configured plugins: $plugin_count"
    
    if [ "$plugin_count" -gt "0" ]; then
        echo "✅ Plugins are configured"
        
        # List plugin names
        echo "📋 Plugins:"
        echo "$plugins" | jq -r '.data[].name' 2>/dev/null || echo "Unable to parse plugin names"
        
        # Check for specific plugins
        if echo "$plugins" | grep -q "cors"; then
            echo "✅ CORS plugin is configured"
        fi
        
        if echo "$plugins" | grep -q "rate-limiting"; then
            echo "✅ Rate limiting plugin is configured"
        fi
        
        if echo "$plugins" | grep -q "prometheus"; then
            echo "✅ Prometheus plugin is configured"
        fi
        
    else
        echo "⚠️  No plugins configured"
    fi
}

# Function to test end-to-end API flow
test_e2e_flow() {
    echo "🔄 Testing end-to-end API flow..."
    
    # Test complete request flow
    local start_time=$(date +%s%N)
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -H "Accept: application/json" \
        -H "User-Agent: OneCRM-Test/1.0" \
        "$API_BASE_URL/health")
    local end_time=$(date +%s%N)
    
    local body=$(echo "$response" | head -n -2)
    local status_code=$(echo "$response" | tail -n 2 | head -n 1)
    local curl_time=$(echo "$response" | tail -n 1)
    
    local total_time=$(( (end_time - start_time) / 1000000 ))
    
    if [ "$status_code" = "200" ]; then
        echo "✅ End-to-end flow successful"
        echo "📊 Response time: ${total_time}ms (curl: ${curl_time}s)"
        
        # Parse response
        if echo "$body" | jq -r '.status' 2>/dev/null | grep -q "ok"; then
            echo "✅ API response is valid"
        else
            echo "⚠️  API response format may be unexpected"
        fi
        
    else
        echo "❌ End-to-end flow failed (HTTP $status_code)"
        return 1
    fi
}

# Function to display test results
display_results() {
    echo ""
    echo "🎉 API Gateway Test Complete!"
    echo ""
    echo "📋 Test Summary:"
    echo "- Kong Health: $kong_health_status"
    echo "- API Routing: $api_routing_status"
    echo "- CORS Headers: $cors_status"
    echo "- Rate Limiting: $rate_limit_status"
    echo "- Authentication: $auth_status"
    echo "- Transformations: $transform_status"
    echo "- Service Discovery: $service_status"
    echo "- Plugin Config: $plugin_status"
    echo "- E2E Flow: $e2e_status"
    echo ""
    
    if [ "$overall_status" = "PASS" ]; then
        echo "✅ All critical tests passed!"
        echo ""
        echo "🔗 Access URLs:"
        echo "- Kong Proxy: $KONG_PROXY_URL"
        echo "- Kong Admin: $KONG_ADMIN_URL"
        echo "- API Base: $API_BASE_URL"
        echo "- Health Check: $API_BASE_URL/health"
    else
        echo "❌ Some tests failed. Please check the logs above."
        exit 1
    fi
}

# Main execution
main() {
    local overall_status="PASS"
    
    # Run tests
    if test_kong_health; then
        kong_health_status="✅ PASS"
    else
        kong_health_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_api_routing; then
        api_routing_status="✅ PASS"
    else
        api_routing_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_cors_headers; then
        cors_status="✅ PASS"
    else
        cors_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_rate_limiting; then
        rate_limit_status="✅ PASS"
    else
        rate_limit_status="⚠️  WARN"
    fi
    
    if test_auth_endpoints; then
        auth_status="✅ PASS"
    else
        auth_status="⚠️  WARN"
    fi
    
    if test_transformations; then
        transform_status="✅ PASS"
    else
        transform_status="⚠️  WARN"
    fi
    
    if test_service_discovery; then
        service_status="✅ PASS"
    else
        service_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    if test_plugins; then
        plugin_status="✅ PASS"
    else
        plugin_status="⚠️  WARN"
    fi
    
    if test_e2e_flow; then
        e2e_status="✅ PASS"
    else
        e2e_status="❌ FAIL"
        overall_status="FAIL"
    fi
    
    # Display results
    display_results
}

# Run main function
main "$@"
