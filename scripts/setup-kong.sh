#!/bin/bash

# OneCRM Kong Gateway Setup Script
# This script configures Kong Gateway for OneCRM

set -e

KONG_ADMIN_URL="${KONG_ADMIN_URL:-http://localhost:8001}"
ENVIRONMENT="${ENVIRONMENT:-development}"
KONG_CONFIG_FILE="kong/config/kong.yml"

if [ "$ENVIRONMENT" = "production" ]; then
    KONG_CONFIG_FILE="kong/config/kong-production.yml"
fi

echo "🦍 Setting up Kong Gateway for OneCRM ($ENVIRONMENT)..."

# Function to wait for Kong to be ready
wait_for_kong() {
    echo "⏳ Waiting for Kong to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$KONG_ADMIN_URL" > /dev/null 2>&1; then
            echo "✅ Kong is ready"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: Kong not ready yet..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ Kong failed to start within expected time"
    return 1
}

# Function to install decK if not present
install_deck() {
    if command -v deck &> /dev/null; then
        echo "✅ decK is already installed"
        deck version
        return 0
    fi
    
    echo "📦 Installing decK..."
    
    # Detect OS and architecture
    local os=$(uname -s | tr '[:upper:]' '[:lower:]')
    local arch=$(uname -m)
    
    case $arch in
        x86_64) arch="amd64" ;;
        arm64|aarch64) arch="arm64" ;;
        *) echo "❌ Unsupported architecture: $arch"; return 1 ;;
    esac
    
    local deck_version="v1.28.2"
    local download_url="https://github.com/Kong/deck/releases/download/${deck_version}/deck_${deck_version}_${os}_${arch}.tar.gz"
    
    echo "Downloading decK from: $download_url"
    
    # Download and install decK
    curl -sL "$download_url" -o deck.tar.gz
    tar -xzf deck.tar.gz
    sudo mv deck /usr/local/bin/
    rm deck.tar.gz
    
    echo "✅ decK installed successfully"
    deck version
}

# Function to validate Kong configuration
validate_config() {
    echo "🔍 Validating Kong configuration..."
    
    if [ ! -f "$KONG_CONFIG_FILE" ]; then
        echo "❌ Kong configuration file not found: $KONG_CONFIG_FILE"
        return 1
    fi
    
    # Validate with decK
    if deck validate --state "$KONG_CONFIG_FILE"; then
        echo "✅ Kong configuration is valid"
        return 0
    else
        echo "❌ Kong configuration validation failed"
        return 1
    fi
}

# Function to sync Kong configuration
sync_config() {
    echo "🔄 Syncing Kong configuration..."
    
    # Replace environment variables in production config
    if [ "$ENVIRONMENT" = "production" ]; then
        echo "🔧 Processing production configuration..."
        
        # Create temporary config file with environment variables replaced
        local temp_config="/tmp/kong-config-processed.yml"
        envsubst < "$KONG_CONFIG_FILE" > "$temp_config"
        KONG_CONFIG_FILE="$temp_config"
    fi
    
    # Sync configuration to Kong
    if deck sync --kong-addr "$KONG_ADMIN_URL" --state "$KONG_CONFIG_FILE"; then
        echo "✅ Kong configuration synced successfully"
        
        # Clean up temporary file
        if [ "$ENVIRONMENT" = "production" ] && [ -f "/tmp/kong-config-processed.yml" ]; then
            rm "/tmp/kong-config-processed.yml"
        fi
        
        return 0
    else
        echo "❌ Kong configuration sync failed"
        return 1
    fi
}

# Function to test Kong configuration
test_kong_config() {
    echo "🧪 Testing Kong configuration..."
    
    # Test health endpoint through Kong
    local health_response=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_ADMIN_URL/../api/health" || echo "000")
    
    if [ "$health_response" = "200" ]; then
        echo "✅ Kong is routing requests correctly"
    else
        echo "⚠️  Kong routing test failed (HTTP $health_response)"
    fi
    
    # Test Kong admin API
    local admin_response=$(curl -s "$KONG_ADMIN_URL/status")
    
    if echo "$admin_response" | grep -q "database"; then
        echo "✅ Kong admin API is working"
    else
        echo "❌ Kong admin API test failed"
        return 1
    fi
    
    # List configured services
    echo "📋 Configured services:"
    curl -s "$KONG_ADMIN_URL/services" | jq -r '.data[].name' 2>/dev/null || echo "Unable to list services (jq not available)"
    
    # List configured routes
    echo "📋 Configured routes:"
    curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data[].name' 2>/dev/null || echo "Unable to list routes (jq not available)"
}

# Function to export current Kong configuration
export_config() {
    echo "📤 Exporting current Kong configuration..."
    
    local export_file="kong/config/kong-exported-$(date +%Y%m%d-%H%M%S).yml"
    
    if deck dump --kong-addr "$KONG_ADMIN_URL" --output-file "$export_file"; then
        echo "✅ Configuration exported to: $export_file"
        return 0
    else
        echo "❌ Configuration export failed"
        return 1
    fi
}

# Function to display Kong information
display_kong_info() {
    echo ""
    echo "🎉 Kong Gateway setup complete!"
    echo ""
    echo "📋 Kong Information:"
    echo "- Admin URL: $KONG_ADMIN_URL"
    echo "- Proxy URL: ${KONG_ADMIN_URL%:*}:8000"
    echo "- Manager URL: ${KONG_ADMIN_URL%:*}:8002"
    echo "- Environment: $ENVIRONMENT"
    echo "- Config File: $KONG_CONFIG_FILE"
    echo ""
    echo "🔗 Useful Commands:"
    echo "- Check status: curl $KONG_ADMIN_URL/status"
    echo "- List services: curl $KONG_ADMIN_URL/services"
    echo "- List routes: curl $KONG_ADMIN_URL/routes"
    echo "- Validate config: deck validate --state $KONG_CONFIG_FILE"
    echo "- Sync config: deck sync --kong-addr $KONG_ADMIN_URL --state $KONG_CONFIG_FILE"
    echo ""
}

# Main execution
main() {
    # Check if Kong configuration file exists
    if [ ! -f "$KONG_CONFIG_FILE" ]; then
        echo "❌ Kong configuration file not found: $KONG_CONFIG_FILE"
        exit 1
    fi
    
    # Wait for Kong to be ready
    if ! wait_for_kong; then
        exit 1
    fi
    
    # Install decK
    if ! install_deck; then
        exit 1
    fi
    
    # Validate configuration
    if ! validate_config; then
        exit 1
    fi
    
    # Export current configuration (backup)
    export_config
    
    # Sync configuration
    if ! sync_config; then
        exit 1
    fi
    
    # Test configuration
    test_kong_config
    
    # Display information
    display_kong_info
}

# Handle command line arguments
case "${1:-}" in
    "validate")
        validate_config
        ;;
    "sync")
        sync_config
        ;;
    "test")
        test_kong_config
        ;;
    "export")
        export_config
        ;;
    *)
        main "$@"
        ;;
esac
