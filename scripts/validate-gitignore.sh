#!/bin/bash

# OneCRM Git Ignore Validation Script
# This script validates the .gitignore configuration and checks for potential issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}OneCRM Git Ignore Validation${NC}"
echo "==============================="

# Function to check if file exists and is ignored
check_ignored() {
    local file="$1"
    local description="$2"
    
    if [ -f "$PROJECT_ROOT/$file" ]; then
        if git check-ignore "$PROJECT_ROOT/$file" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $description: $file (ignored)"
        else
            echo -e "${RED}✗${NC} $description: $file (NOT ignored - should be)"
            return 1
        fi
    else
        echo -e "${YELLOW}?${NC} $description: $file (file not found)"
    fi
    return 0
}

# Function to check if file exists and is NOT ignored
check_not_ignored() {
    local file="$1"
    local description="$2"
    
    if [ -f "$PROJECT_ROOT/$file" ]; then
        if git check-ignore "$PROJECT_ROOT/$file" >/dev/null 2>&1; then
            echo -e "${RED}✗${NC} $description: $file (ignored - should NOT be)"
            return 1
        else
            echo -e "${GREEN}✓${NC} $description: $file (tracked)"
        fi
    else
        echo -e "${YELLOW}?${NC} $description: $file (file not found)"
    fi
    return 0
}

# Function to check directory patterns
check_directory_ignored() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$PROJECT_ROOT/$dir" ]; then
        # Create a test file in the directory
        local test_file="$PROJECT_ROOT/$dir/test_gitignore_validation.tmp"
        touch "$test_file"
        
        if git check-ignore "$test_file" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $description: $dir/ (ignored)"
            rm -f "$test_file"
        else
            echo -e "${RED}✗${NC} $description: $dir/ (NOT ignored - should be)"
            rm -f "$test_file"
            return 1
        fi
    else
        echo -e "${YELLOW}?${NC} $description: $dir/ (directory not found)"
    fi
    return 0
}

echo -e "\n${BLUE}Checking Build Artifacts...${NC}"
issues=0

# Build outputs
check_ignored "frontend/.next" "Next.js build output" || ((issues++))
check_ignored "frontend/out" "Next.js export output" || ((issues++))
check_ignored "backend/crm-api/dist" "Backend build output" || ((issues++))
check_ignored "frontend/tsconfig.tsbuildinfo" "TypeScript build info" || ((issues++))

echo -e "\n${BLUE}Checking Dependencies...${NC}"

# Dependencies
check_directory_ignored "node_modules" "Node modules" || ((issues++))
check_directory_ignored "frontend/node_modules" "Frontend node modules" || ((issues++))

echo -e "\n${BLUE}Checking Environment Files...${NC}"

# Environment files (should be ignored)
check_ignored "frontend/.env.local" "Frontend local env" || ((issues++))
check_ignored "backend/crm-api/.env.production" "Backend production env" || ((issues++))

# Environment templates (should NOT be ignored)
check_not_ignored "frontend/.env.example" "Frontend env example" || ((issues++))

echo -e "\n${BLUE}Checking Test & Coverage Files...${NC}"

# Test outputs
check_directory_ignored "frontend/coverage" "Frontend coverage" || ((issues++))
check_directory_ignored "frontend/test-results" "Frontend test results" || ((issues++))

echo -e "\n${BLUE}Checking Cache & Temporary Files...${NC}"

# Cache directories
check_directory_ignored ".nx/cache" "Nx cache" || ((issues++))
check_ignored "frontend/.eslintcache" "ESLint cache" || ((issues++))

echo -e "\n${BLUE}Checking Logs...${NC}"

# Log files
if ls "$PROJECT_ROOT"/backend/crm-api/logs/*.log >/dev/null 2>&1; then
    for log_file in "$PROJECT_ROOT"/backend/crm-api/logs/*.log; do
        relative_path="${log_file#$PROJECT_ROOT/}"
        check_ignored "$relative_path" "Backend log file" || ((issues++))
    done
fi

echo -e "\n${BLUE}Checking Service Data...${NC}"

# Service data directories
check_directory_ignored "keycloak-data" "Keycloak data" || ((issues++))
check_directory_ignored "postgres-data" "PostgreSQL data" || ((issues++))

echo -e "\n${BLUE}Checking Important Configuration Files...${NC}"

# Important files that should NOT be ignored
check_not_ignored ".gitignore" "Git ignore file" || ((issues++))
check_not_ignored "package.json" "Root package.json" || ((issues++))
check_not_ignored "frontend/package.json" "Frontend package.json" || ((issues++))
check_not_ignored "nx.json" "Nx configuration" || ((issues++))
check_not_ignored "tsconfig.base.json" "Base TypeScript config" || ((issues++))

echo -e "\n${BLUE}Checking for Accidentally Committed Files...${NC}"

# Check for files that might have been accidentally committed
accidentally_committed=()

# Check for environment files in git history
if git ls-files | grep -E "\.(env|key|pem|crt)$" | grep -v "\.example$" | grep -v "\.template$"; then
    echo -e "${RED}⚠️  Found potentially sensitive files in git:${NC}"
    git ls-files | grep -E "\.(env|key|pem|crt)$" | grep -v "\.example$" | grep -v "\.template$" | while read -r file; do
        echo -e "${RED}   - $file${NC}"
    done
    ((issues++))
fi

# Check for large files that might be build artifacts
echo -e "\n${BLUE}Checking for Large Files...${NC}"
large_files=$(git ls-files | xargs ls -la 2>/dev/null | awk '$5 > 1048576 {print $9, $5}' | head -5)
if [ -n "$large_files" ]; then
    echo -e "${YELLOW}⚠️  Found large files (>1MB) in git:${NC}"
    echo "$large_files" | while read -r file size; do
        echo -e "${YELLOW}   - $file ($(numfmt --to=iec $size))${NC}"
    done
fi

echo -e "\n${BLUE}Summary${NC}"
echo "======="

if [ $issues -eq 0 ]; then
    echo -e "${GREEN}✓ All checks passed! Git ignore configuration is properly set up.${NC}"
    exit 0
else
    echo -e "${RED}✗ Found $issues issue(s) with git ignore configuration.${NC}"
    echo -e "${YELLOW}Please review the issues above and update .gitignore accordingly.${NC}"
    exit 1
fi
