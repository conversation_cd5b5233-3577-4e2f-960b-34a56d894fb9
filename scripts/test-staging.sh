#!/bin/bash

# OneCRM Staging Testing Script
# Comprehensive testing suite for staging environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="onecrm-staging"
BACKEND_SERVICE="onecrm-backend-service"
FRONTEND_SERVICE="onecrm-frontend-service"
KEYCLOAK_SERVICE="keycloak-service"

# Test results
HEALTH_TESTS=0
API_TESTS=0
FRONTEND_TESTS=0
INTEGRATION_TESTS=0
PERFORMANCE_TESTS=0

echo -e "${BLUE}🧪 OneCRM Staging Test Suite${NC}"
echo "=================================================="
echo "Namespace: $NAMESPACE"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to print test results
print_result() {
    if [ $2 -eq 0 ]; then
        echo -e "${GREEN}✅ $1 PASSED${NC}"
    else
        echo -e "${RED}❌ $1 FAILED${NC}"
    fi
}

# Get service URLs
get_service_urls() {
    BACKEND_IP=$(kubectl get svc $BACKEND_SERVICE -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    FRONTEND_IP=$(kubectl get svc $FRONTEND_SERVICE -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    KEYCLOAK_IP=$(kubectl get svc $KEYCLOAK_SERVICE -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    
    BACKEND_URL="http://$BACKEND_IP:8000"
    FRONTEND_URL="http://$FRONTEND_IP:3000"
    KEYCLOAK_URL="http://$KEYCLOAK_IP:8080"
    
    echo "Backend URL: $BACKEND_URL"
    echo "Frontend URL: $FRONTEND_URL"
    echo "Keycloak URL: $KEYCLOAK_URL"
}

# Health check tests
run_health_tests() {
    print_section "🏥 Health Check Tests"
    
    local failed=0
    
    echo "Testing backend health..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f -s $BACKEND_URL/health >/dev/null; then
        echo -e "${GREEN}✅ Backend health endpoint${NC}"
    else
        echo -e "${RED}❌ Backend health endpoint${NC}"
        failed=1
    fi
    
    echo "Testing backend readiness..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f -s $BACKEND_URL/health/ready >/dev/null; then
        echo -e "${GREEN}✅ Backend readiness endpoint${NC}"
    else
        echo -e "${RED}❌ Backend readiness endpoint${NC}"
        failed=1
    fi
    
    echo "Testing database connectivity..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f -s $BACKEND_URL/health/db >/dev/null; then
        echo -e "${GREEN}✅ Database connectivity${NC}"
    else
        echo -e "${RED}❌ Database connectivity${NC}"
        failed=1
    fi
    
    echo "Testing Redis connectivity..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f -s $BACKEND_URL/health/redis >/dev/null; then
        echo -e "${GREEN}✅ Redis connectivity${NC}"
    else
        echo -e "${RED}❌ Redis connectivity${NC}"
        failed=1
    fi
    
    echo "Testing frontend health..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q $FRONTEND_URL/api/health; then
        echo -e "${GREEN}✅ Frontend health endpoint${NC}"
    else
        echo -e "${RED}❌ Frontend health endpoint${NC}"
        failed=1
    fi
    
    echo "Testing Keycloak health..."
    if kubectl exec -n $NAMESPACE deployment/keycloak -- curl -f -s $KEYCLOAK_URL/health/ready >/dev/null; then
        echo -e "${GREEN}✅ Keycloak health endpoint${NC}"
    else
        echo -e "${RED}❌ Keycloak health endpoint${NC}"
        failed=1
    fi
    
    HEALTH_TESTS=$failed
    print_result "Health Tests" $failed
}

# API endpoint tests
run_api_tests() {
    print_section "🔌 API Endpoint Tests"
    
    local failed=0
    
    echo "Testing API documentation endpoint..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f -s $BACKEND_URL/api-docs >/dev/null; then
        echo -e "${GREEN}✅ API documentation available${NC}"
    else
        echo -e "${RED}❌ API documentation not available${NC}"
        failed=1
    fi
    
    echo "Testing contacts endpoint (without auth)..."
    local response=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL/api/contacts)
    if [ "$response" = "401" ]; then
        echo -e "${GREEN}✅ Contacts endpoint properly secured${NC}"
    else
        echo -e "${RED}❌ Contacts endpoint security issue (got $response, expected 401)${NC}"
        failed=1
    fi
    
    echo "Testing companies endpoint (without auth)..."
    local response=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL/api/companies)
    if [ "$response" = "401" ]; then
        echo -e "${GREEN}✅ Companies endpoint properly secured${NC}"
    else
        echo -e "${RED}❌ Companies endpoint security issue (got $response, expected 401)${NC}"
        failed=1
    fi
    
    echo "Testing deals endpoint (without auth)..."
    local response=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL/api/deals)
    if [ "$response" = "401" ]; then
        echo -e "${GREEN}✅ Deals endpoint properly secured${NC}"
    else
        echo -e "${RED}❌ Deals endpoint security issue (got $response, expected 401)${NC}"
        failed=1
    fi
    
    echo "Testing activities endpoint (without auth)..."
    local response=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL/api/activities)
    if [ "$response" = "401" ]; then
        echo -e "${GREEN}✅ Activities endpoint properly secured${NC}"
    else
        echo -e "${RED}❌ Activities endpoint security issue (got $response, expected 401)${NC}"
        failed=1
    fi
    
    echo "Testing CORS headers..."
    local cors_header=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -H "Origin: https://staging.onecrm.example.com" -I $BACKEND_URL/health | grep -i "access-control-allow-origin" || echo "")
    if [ -n "$cors_header" ]; then
        echo -e "${GREEN}✅ CORS headers present${NC}"
    else
        echo -e "${RED}❌ CORS headers missing${NC}"
        failed=1
    fi
    
    API_TESTS=$failed
    print_result "API Tests" $failed
}

# Frontend tests
run_frontend_tests() {
    print_section "🌐 Frontend Tests"
    
    local failed=0
    
    echo "Testing frontend root endpoint..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q $FRONTEND_URL/; then
        echo -e "${GREEN}✅ Frontend root accessible${NC}"
    else
        echo -e "${RED}❌ Frontend root not accessible${NC}"
        failed=1
    fi
    
    echo "Testing login page..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q $FRONTEND_URL/login; then
        echo -e "${GREEN}✅ Login page accessible${NC}"
    else
        echo -e "${RED}❌ Login page not accessible${NC}"
        failed=1
    fi
    
    echo "Testing static assets..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q $FRONTEND_URL/_next/static/css/app.css 2>/dev/null || true; then
        echo -e "${GREEN}✅ Static assets loading${NC}"
    else
        echo -e "${YELLOW}⚠️  Some static assets may not be available${NC}"
    fi
    
    echo "Testing API proxy..."
    local response=$(kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- curl -s -o /dev/null -w "%{http_code}" $FRONTEND_URL/api/health)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ API proxy working${NC}"
    else
        echo -e "${RED}❌ API proxy not working (got $response)${NC}"
        failed=1
    fi
    
    FRONTEND_TESTS=$failed
    print_result "Frontend Tests" $failed
}

# Integration tests
run_integration_tests() {
    print_section "🔗 Integration Tests"
    
    local failed=0
    
    echo "Testing database schema..."
    local tables=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- psql $DATABASE_URL -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "0")
    if [ "$tables" -gt "10" ]; then
        echo -e "${GREEN}✅ Database schema populated ($tables tables)${NC}"
    else
        echo -e "${RED}❌ Database schema incomplete ($tables tables)${NC}"
        failed=1
    fi
    
    echo "Testing Redis connection..."
    if kubectl exec -n $NAMESPACE deployment/redis -- redis-cli -a "$REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
        echo -e "${GREEN}✅ Redis connection working${NC}"
    else
        echo -e "${RED}❌ Redis connection failed${NC}"
        failed=1
    fi
    
    echo "Testing Keycloak realm..."
    local realm_response=$(kubectl exec -n $NAMESPACE deployment/keycloak -- curl -s -o /dev/null -w "%{http_code}" $KEYCLOAK_URL/realms/onecrm-staging)
    if [ "$realm_response" = "200" ]; then
        echo -e "${GREEN}✅ Keycloak realm accessible${NC}"
    else
        echo -e "${RED}❌ Keycloak realm not accessible (got $realm_response)${NC}"
        failed=1
    fi
    
    echo "Testing service discovery..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- nslookup postgres-service >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Service discovery working${NC}"
    else
        echo -e "${RED}❌ Service discovery failed${NC}"
        failed=1
    fi
    
    INTEGRATION_TESTS=$failed
    print_result "Integration Tests" $failed
}

# Performance tests
run_performance_tests() {
    print_section "⚡ Performance Tests"
    
    local failed=0
    
    echo "Testing backend response time..."
    local response_time=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{time_total}" $BACKEND_URL/health)
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
    
    if [ "$response_time_ms" -lt "1000" ]; then
        echo -e "${GREEN}✅ Backend response time: ${response_time_ms}ms${NC}"
    else
        echo -e "${RED}❌ Backend response time too slow: ${response_time_ms}ms${NC}"
        failed=1
    fi
    
    echo "Testing frontend response time..."
    local frontend_response_time=$(kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- curl -s -o /dev/null -w "%{time_total}" $FRONTEND_URL/api/health)
    local frontend_response_time_ms=$(echo "$frontend_response_time * 1000" | bc -l | cut -d. -f1)
    
    if [ "$frontend_response_time_ms" -lt "2000" ]; then
        echo -e "${GREEN}✅ Frontend response time: ${frontend_response_time_ms}ms${NC}"
    else
        echo -e "${RED}❌ Frontend response time too slow: ${frontend_response_time_ms}ms${NC}"
        failed=1
    fi
    
    echo "Testing database query performance..."
    local db_response_time=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{time_total}" $BACKEND_URL/health/db)
    local db_response_time_ms=$(echo "$db_response_time * 1000" | bc -l | cut -d. -f1)
    
    if [ "$db_response_time_ms" -lt "500" ]; then
        echo -e "${GREEN}✅ Database response time: ${db_response_time_ms}ms${NC}"
    else
        echo -e "${RED}❌ Database response time too slow: ${db_response_time_ms}ms${NC}"
        failed=1
    fi
    
    PERFORMANCE_TESTS=$failed
    print_result "Performance Tests" $failed
}

# Resource usage tests
run_resource_tests() {
    print_section "📊 Resource Usage Tests"
    
    echo "Checking pod resource usage..."
    kubectl top pods -n $NAMESPACE 2>/dev/null || echo "Metrics server not available"
    
    echo -e "\nChecking pod status..."
    kubectl get pods -n $NAMESPACE -o wide
    
    echo -e "\nChecking persistent volumes..."
    kubectl get pv,pvc -n $NAMESPACE 2>/dev/null || echo "No persistent volumes"
    
    echo -e "\nChecking service endpoints..."
    kubectl get endpoints -n $NAMESPACE
}

# Security tests
run_security_tests() {
    print_section "🔒 Security Tests"
    
    local failed=0
    
    echo "Testing security headers..."
    local security_headers=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -I $BACKEND_URL/health | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection)" | wc -l)
    
    if [ "$security_headers" -ge "2" ]; then
        echo -e "${GREEN}✅ Security headers present${NC}"
    else
        echo -e "${RED}❌ Missing security headers${NC}"
        failed=1
    fi
    
    echo "Testing HTTPS redirect (if applicable)..."
    # This would be tested if ingress is configured
    echo -e "${YELLOW}⚠️  HTTPS redirect test skipped (no ingress in staging)${NC}"
    
    echo "Testing authentication requirement..."
    local auth_response=$(kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL/api/contacts)
    if [ "$auth_response" = "401" ]; then
        echo -e "${GREEN}✅ Authentication properly enforced${NC}"
    else
        echo -e "${RED}❌ Authentication bypass possible${NC}"
        failed=1
    fi
    
    print_result "Security Tests" $failed
}

# Generate test report
generate_test_report() {
    print_section "📋 Test Report Summary"
    
    local total_failures=$((HEALTH_TESTS + API_TESTS + FRONTEND_TESTS + INTEGRATION_TESTS + PERFORMANCE_TESTS))
    
    echo "Test Results:"
    echo "============="
    print_result "Health Tests" $HEALTH_TESTS
    print_result "API Tests" $API_TESTS
    print_result "Frontend Tests" $FRONTEND_TESTS
    print_result "Integration Tests" $INTEGRATION_TESTS
    print_result "Performance Tests" $PERFORMANCE_TESTS
    
    echo ""
    if [ $total_failures -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! Staging environment is ready.${NC}"
        echo -e "${GREEN}✅ Ready for production deployment${NC}"
        exit 0
    else
        echo -e "${RED}💥 $total_failures test suite(s) failed.${NC}"
        echo -e "${RED}❌ Staging environment needs attention before production deployment${NC}"
        exit 1
    fi
}

# Main function
main() {
    echo -e "${BLUE}Starting comprehensive staging tests...${NC}"
    
    get_service_urls
    run_health_tests
    run_api_tests
    run_frontend_tests
    run_integration_tests
    run_performance_tests
    run_resource_tests
    run_security_tests
    generate_test_report
}

# Run main function
main
