# OneCRM Product Overview

OneCRM is an enterprise-grade, multi-tenant Customer Relationship Management platform built as a fork of Twenty CRM. The product provides comprehensive CRM functionality with modern security architecture and SaaS capabilities.

## Core Product Features

- **Contact Management**: Complete contact lifecycle with advanced search and filtering
- **Company Management**: Hierarchical company structures with relationship mapping  
- **Deal Pipeline**: Visual sales pipeline with customizable stages and forecasting
- **Activity Tracking**: Comprehensive interaction history and task management
- **Dashboard & Analytics**: Real-time KPI monitoring and business intelligence

## Enterprise SaaS Features

- **Multi-Tenancy**: Complete tenant isolation with organization management
- **Single Sign-On**: Keycloak-based authentication with OIDC/SAML support
- **API Gateway**: Kong-powered API management with rate limiting and security
- **Role-Based Access Control**: Granular permissions and security policies
- **Audit Logging**: Complete audit trail for compliance and security

## Architecture Philosophy

OneCRM follows a microservices architecture with:
- Frontend-backend separation via API Gateway
- Keycloak as the sole authentication provider
- Multi-tenant data isolation at all layers
- Cloud-native deployment with Kubernetes support

## License & Compliance

The project maintains GPL-3.0 license compatibility with Twenty CRM while adding proprietary SaaS extensions as separate services to avoid license contamination.