# Technology Stack & Build System

OneCRM uses a modern TypeScript-based stack with Nx monorepo architecture.

## Build System

- **Monorepo**: Nx workspace with shared configurations
- **Package Manager**: npm with workspaces
- **TypeScript**: Strict mode enabled across all projects
- **Linting**: ESLint with TypeScript rules and Prettier formatting

## Frontend Stack

- **Framework**: Next.js 14 with React 18
- **Language**: TypeScript (strict mode)
- **Authentication**: @react-keycloak/web for OIDC integration
- **UI Library**: Material UI (@mui/material) with Material Tailwind
- **State Management**: SWR for server state, React Context for client state
- **Forms**: react-hook-form with @hookform/resolvers and Zod validation
- **Tables**: @tanstack/react-table
- **Charts**: Recharts
- **Animations**: GSAP
- **Schema Forms**: @rjsf/core with @rjsf/mui for dynamic UI generation

## Backend Stack

- **Framework**: NestJS with TypeScript
- **Authentication**: nest-keycloak-connect for Keycloak integration
- **Database**: PostgreSQL 15 with TypeORM
- **Cache**: Redis 7
- **Validation**: Zod schemas with class-validator
- **API Documentation**: @nestjs/swagger (OpenAPI)
- **Logging**: Winston with daily rotate file

## Infrastructure

- **API Gateway**: Kong Gateway 3.x with OIDC plugin
- **Authentication**: Keycloak 22.x
- **Database**: PostgreSQL 15 with Row Level Security (RLS)
- **Cache**: Redis 7
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Kubernetes (production)
- **Config Management**: decK for Kong GitOps

## Development Tools

- **Testing**: Jest with @testing-library/react and Playwright for E2E
- **CI/CD**: GitHub Actions
- **Security**: Snyk scanning, npm audit
- **Code Quality**: ESLint, Prettier, TypeScript strict mode
- **Hot Reload**: Enabled for all services in development

## Common Commands

```bash
# Development
npm run dev                    # Start all services in development
npm run dev:frontend          # Start frontend only
npm run dev:backend           # Start backend only

# Building
npm run build                 # Build all projects
nx run-many --target=build --all

# Testing
npm run test                  # Run all tests
nx run-many --target=test --all

# Code Quality
npm run lint                  # Lint all projects
npm run format                # Format code with Prettier
npm run typecheck             # TypeScript type checking

# Setup
npm run setup                 # Install dependencies and setup
npm run clean                 # Clean all node_modules and caches

# Infrastructure
./scripts/setup.sh            # Setup development environment
./scripts/dev-start.sh         # Start development services
docker-compose up -d           # Start infrastructure services
```

## Environment Setup

1. **Prerequisites**: Node.js 18+, Docker, PostgreSQL 15+, Redis 7+
2. **Development**: Use `./scripts/setup.sh` for complete environment setup
3. **Services**: Frontend (3000), Backend (3002), Kong (8000), Keycloak (8080)

## Architecture Principles

- **Microservices**: Backend services are independently deployable
- **API-First**: All communication via Kong Gateway with OpenAPI specs
- **Multi-Tenant**: All services are tenant-aware with data isolation
- **Type Safety**: End-to-end TypeScript with Zod schema validation
- **Test-Driven**: TDD approach with high test coverage requirements