# Dev Dockerfile for Next.js frontend with live reload
FROM node:18-alpine

WORKDIR /app

# Install OS deps if needed
RUN apk add --no-cache libc6-compat

# Copy only package manifests first for better caching
COPY ./frontend/package*.json ./

# Install deps
RUN npm install

# Copy app source
COPY ./frontend ./

# Ensure Next listens on all interfaces
ENV HOST=0.0.0.0
ENV PORT=3000
ENV NEXT_TELEMETRY_DISABLED=1

# Expose port
EXPOSE 3000

# Default command in dev: if package.json has dev, use it
CMD ["npm", "run", "dev"]
