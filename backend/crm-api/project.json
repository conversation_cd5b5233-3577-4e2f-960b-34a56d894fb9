{"name": "@onecrm/crm-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "backend/crm-api/src", "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "nest start --watch", "cwd": "backend/crm-api"}}, "build": {"executor": "nx:run-commands", "options": {"command": "nest build", "cwd": "backend/crm-api"}, "outputs": ["{projectRoot}/dist"]}, "start": {"executor": "nx:run-commands", "options": {"command": "nest start", "cwd": "backend/crm-api"}, "dependsOn": ["build"]}, "start:prod": {"executor": "nx:run-commands", "options": {"command": "node dist/main", "cwd": "backend/crm-api"}, "dependsOn": ["build"]}, "lint": {"executor": "nx:run-commands", "options": {"command": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "cwd": "backend/crm-api"}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "backend/crm-api"}}, "test": {"executor": "nx:run-commands", "options": {"command": "jest", "cwd": "backend/crm-api"}}, "test:watch": {"executor": "nx:run-commands", "options": {"command": "jest --watch", "cwd": "backend/crm-api"}}, "test:cov": {"executor": "nx:run-commands", "options": {"command": "jest --coverage", "cwd": "backend/crm-api"}}, "test:e2e": {"executor": "nx:run-commands", "options": {"command": "jest --config ./test/jest-e2e.json", "cwd": "backend/crm-api"}}, "setup": {"executor": "nx:run-commands", "options": {"command": "npm install", "cwd": "backend/crm-api"}}}, "tags": ["scope:backend", "type:app"]}