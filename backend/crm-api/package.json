{"name": "@onecrm/crm-api", "version": "1.0.0", "description": "OneCRM Main API Service", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typecheck": "tsc --noEmit", "setup": "npm install", "start:dev": "nest start --watch"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.0", "@nestjs/typeorm": "^10.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.8.1", "helmet": "^8.1.0", "keycloak-connect": "^22.0.0", "nest-keycloak-connect": "^1.9.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/compression": "^1.8.1", "@types/express": "^4.17.17", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/pg": "^8.10.0", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}