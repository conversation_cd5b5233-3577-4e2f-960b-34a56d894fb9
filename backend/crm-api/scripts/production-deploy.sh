#!/bin/bash

# OneCRM API Production Deployment Script
# This script prepares and deploys the OneCRM API for production

set -e

echo "🚀 OneCRM API Production Deployment"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the API root directory."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "Created .env file from .env.example"
        print_warning "Please update .env file with production values before continuing."
        exit 1
    else
        print_error ".env.example not found. Please create .env file manually."
        exit 1
    fi
fi

# Validate environment variables
print_header "Validating Environment Configuration"

required_vars=(
    "NODE_ENV"
    "PORT"
    "DB_HOST"
    "DB_PORT"
    "DB_USERNAME"
    "DB_PASSWORD"
    "DB_NAME"
    "JWT_SECRET"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
fi

print_status "Environment validation passed"

# Check Node.js version
print_header "Checking Node.js Version"
node_version=$(node --version)
print_status "Node.js version: $node_version"

# Install dependencies
print_header "Installing Dependencies"
npm ci --only=production
print_status "Production dependencies installed"

# Build the application
print_header "Building Application"
npm run build
print_status "Application built successfully"

# Run database migrations (if any)
print_header "Database Setup"
if [ -f "dist/migrations" ]; then
    print_status "Running database migrations..."
    npm run migration:run
else
    print_status "No migrations found, skipping..."
fi

# Create logs directory
print_header "Setting up Logging"
mkdir -p logs
chmod 755 logs
print_status "Logs directory created"

# Set up PM2 ecosystem file
print_header "Setting up Process Manager"
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'onecrm-api',
    script: 'dist/main.js',
    instances: process.env.PM2_INSTANCES || 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: process.env.PORT || 3002
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

print_status "PM2 ecosystem file created"

# Health check function
health_check() {
    local url="http://localhost:${PORT:-3002}/api/health"
    local max_attempts=30
    local attempt=1
    
    print_status "Performing health check..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_status "Health check passed!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_error "Health check failed after $max_attempts attempts"
    return 1
}

# Start the application
print_header "Starting Application"

if command -v pm2 &> /dev/null; then
    print_status "Starting with PM2..."
    pm2 start ecosystem.config.js
    pm2 save
    
    # Health check
    sleep 5
    if health_check; then
        print_status "Application started successfully with PM2"
        pm2 status
    else
        print_error "Application failed to start properly"
        pm2 logs onecrm-api --lines 50
        exit 1
    fi
else
    print_warning "PM2 not found. Starting with npm..."
    npm run start:prod &
    APP_PID=$!
    
    # Health check
    sleep 10
    if health_check; then
        print_status "Application started successfully (PID: $APP_PID)"
    else
        print_error "Application failed to start properly"
        kill $APP_PID 2>/dev/null || true
        exit 1
    fi
fi

# Final status
print_header "Deployment Summary"
print_status "✅ OneCRM API deployed successfully!"
print_status "🌐 API URL: http://localhost:${PORT:-3002}"
print_status "📚 Documentation: http://localhost:${PORT:-3002}/api/docs"
print_status "📊 Health Check: http://localhost:${PORT:-3002}/api/health"
print_status "📋 Modules Status: http://localhost:${PORT:-3002}/api/health/modules"

echo ""
echo "🎉 Deployment completed successfully!"
echo "Monitor the application with: pm2 monit (if using PM2)"
echo "View logs with: pm2 logs onecrm-api (if using PM2)"
echo ""
