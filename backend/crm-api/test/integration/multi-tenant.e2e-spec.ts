import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { DataSource } from 'typeorm';

describe('Multi-Tenant API (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  // Test organization IDs
  const ACME_ORG_ID = '550e8400-e29b-41d4-a716-446655440001';
  const TECHSTART_ORG_ID = '550e8400-e29b-41d4-a716-446655440002';
  const SMALLBIZ_ORG_ID = '550e8400-e29b-41d4-a716-446655440003';

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('Tenant Context', () => {
    it('should require organization context for protected endpoints', async () => {
      const response = await request(app.getHttpServer())
        .get('/organizations/current')
        .expect(401); // Should fail without auth in production

      // In development mode, it might return 200
      if (process.env.NODE_ENV === 'development') {
        expect([200, 401]).toContain(response.status);
      }
    });

    it('should accept X-Org-Id header', async () => {
      if (process.env.NODE_ENV === 'development') {
        await request(app.getHttpServer())
          .get('/organizations/current')
          .set('X-Org-Id', ACME_ORG_ID)
          .expect(200);
      }
    });

    it('should reject mismatched organization context', async () => {
      // This test would require proper authentication setup
      // For now, we'll test the basic structure
      expect(ACME_ORG_ID).not.toBe(TECHSTART_ORG_ID);
    });
  });

  describe('Data Isolation', () => {
    it('should isolate user data by organization', async () => {
      if (process.env.NODE_ENV === 'development') {
        // Test Acme Corp users
        const acmeResponse = await request(app.getHttpServer())
          .get('/users/organization')
          .set('X-Org-Id', ACME_ORG_ID)
          .expect(200);

        // Test TechStart users
        const techstartResponse = await request(app.getHttpServer())
          .get('/users/organization')
          .set('X-Org-Id', TECHSTART_ORG_ID)
          .expect(200);

        // Verify that different organizations have different users
        expect(acmeResponse.body).not.toEqual(techstartResponse.body);
      }
    });

    it('should prevent cross-tenant data access', async () => {
      if (process.env.NODE_ENV === 'development') {
        // Try to access Acme data with TechStart context
        const response = await request(app.getHttpServer())
          .get('/users/organization')
          .set('X-Org-Id', TECHSTART_ORG_ID);

        // Should only return TechStart users, not Acme users
        if (response.status === 200) {
          const users = response.body;
          const acmeUsers = users.filter((user: any) => user.orgId === ACME_ORG_ID);
          expect(acmeUsers).toHaveLength(0);
        }
      }
    });
  });

  describe('Database RLS', () => {
    it('should have RLS enabled on all tenant tables', async () => {
      const tables = ['organizations', 'users', 'contacts', 'companies', 'deals'];
      
      for (const table of tables) {
        const result = await dataSource.query(
          'SELECT relrowsecurity FROM pg_class WHERE relname = $1',
          [table]
        );
        
        expect(result).toHaveLength(1);
        expect(result[0].relrowsecurity).toBe(true);
      }
    });

    it('should enforce tenant context in database queries', async () => {
      // Set user context for Acme Corp
      await dataSource.query(
        'SELECT set_user_context($1, $2, $3, $4)',
        ['660e8400-e29b-41d4-a716-446655440001', ACME_ORG_ID, 'admin', 'development']
      );

      // Query users - should only return Acme users
      const users = await dataSource.query('SELECT * FROM users');
      
      // All returned users should belong to Acme Corp
      users.forEach((user: any) => {
        expect(user.org_id).toBe(ACME_ORG_ID);
      });

      // Clear context
      await dataSource.query('SELECT clear_user_context()');
    });

    it('should prevent unauthorized data access', async () => {
      // Set user context for TechStart
      await dataSource.query(
        'SELECT set_user_context($1, $2, $3, $4)',
        ['660e8400-e29b-41d4-a716-446655440004', TECHSTART_ORG_ID, 'user', 'development']
      );

      // Try to query all organizations - should only see TechStart
      const orgs = await dataSource.query('SELECT * FROM organizations');
      
      // Should only see TechStart organization
      expect(orgs).toHaveLength(1);
      expect(orgs[0].id).toBe(TECHSTART_ORG_ID);

      // Clear context
      await dataSource.query('SELECT clear_user_context()');
    });
  });

  describe('Permission Enforcement', () => {
    it('should enforce admin-only operations', async () => {
      if (process.env.NODE_ENV === 'development') {
        // Test organization settings update (admin only)
        const response = await request(app.getHttpServer())
          .put('/organizations/settings')
          .set('X-Org-Id', ACME_ORG_ID)
          .send({ settings: { test: 'value' } });

        // Should succeed in development mode
        expect([200, 401, 403]).toContain(response.status);
      }
    });

    it('should allow user operations within tenant', async () => {
      if (process.env.NODE_ENV === 'development') {
        // Test user profile access (allowed for all users)
        const response = await request(app.getHttpServer())
          .get('/users/profile')
          .set('X-Org-Id', ACME_ORG_ID);

        expect([200, 401]).toContain(response.status);
      }
    });
  });

  describe('Organization Management', () => {
    it('should return current organization stats', async () => {
      if (process.env.NODE_ENV === 'development') {
        const response = await request(app.getHttpServer())
          .get('/organizations/stats')
          .set('X-Org-Id', ACME_ORG_ID)
          .expect(200);

        expect(response.body).toHaveProperty('totalUsers');
        expect(response.body).toHaveProperty('activeUsers');
        expect(response.body).toHaveProperty('totalContacts');
        expect(response.body).toHaveProperty('totalCompanies');
        expect(response.body).toHaveProperty('totalDeals');
        expect(response.body).toHaveProperty('storageUsed');
      }
    });

    it('should handle organization updates', async () => {
      if (process.env.NODE_ENV === 'development') {
        const updateData = {
          name: 'Updated Acme Corporation',
        };

        const response = await request(app.getHttpServer())
          .put('/organizations')
          .set('X-Org-Id', ACME_ORG_ID)
          .send(updateData);

        expect([200, 401, 403]).toContain(response.status);
      }
    });
  });

  describe('Data Consistency', () => {
    it('should maintain referential integrity across tenants', async () => {
      // Check that all users belong to valid organizations
      const orphanedUsers = await dataSource.query(`
        SELECT COUNT(*) as count FROM users u 
        LEFT JOIN organizations o ON u.org_id = o.id 
        WHERE o.id IS NULL
      `);

      expect(orphanedUsers[0].count).toBe('0');
    });

    it('should ensure all records have org_id', async () => {
      const tables = ['users', 'contacts', 'companies', 'deals'];
      
      for (const table of tables) {
        const result = await dataSource.query(
          `SELECT COUNT(*) as count FROM ${table} WHERE org_id IS NULL`
        );
        
        expect(result[0].count).toBe('0');
      }
    });
  });

  describe('Performance', () => {
    it('should perform tenant-filtered queries efficiently', async () => {
      const startTime = Date.now();

      // Set user context
      await dataSource.query(
        'SELECT set_user_context($1, $2, $3, $4)',
        ['660e8400-e29b-41d4-a716-446655440001', ACME_ORG_ID, 'admin', 'development']
      );

      // Perform multiple queries
      await Promise.all([
        dataSource.query('SELECT COUNT(*) FROM users'),
        dataSource.query('SELECT COUNT(*) FROM contacts'),
        dataSource.query('SELECT COUNT(*) FROM companies'),
        dataSource.query('SELECT COUNT(*) FROM deals'),
      ]);

      // Clear context
      await dataSource.query('SELECT clear_user_context()');

      const duration = Date.now() - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(1000); // 1 second
    });
  });
});
