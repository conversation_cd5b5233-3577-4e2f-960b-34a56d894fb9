import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Authentication (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/health (GET)', () => {
    it('should return health status without authentication', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'ok');
          expect(res.body).toHaveProperty('service', 'onecrm-api');
        });
    });
  });

  describe('/users/profile (GET)', () => {
    it('should return 401 without authentication token', () => {
      return request(app.getHttpServer())
        .get('/users/profile')
        .expect(401);
    });

    it('should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/users/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    // Note: In development mode, auth is bypassed as per guidelines
    it('should bypass auth in development mode', () => {
      if (process.env.NODE_ENV === 'development') {
        return request(app.getHttpServer())
          .get('/users/profile')
          .expect(200);
      }
    });
  });

  describe('Tenant isolation', () => {
    it('should require org context for protected endpoints', () => {
      return request(app.getHttpServer())
        .get('/organizations/test-org-id')
        .expect(401); // Should fail without auth
    });

    it('should accept X-Org-Id header', () => {
      if (process.env.NODE_ENV === 'development') {
        return request(app.getHttpServer())
          .get('/organizations/test-org-id')
          .set('X-Org-Id', 'test-org-id')
          .expect(200);
      }
    });
  });

  describe('CORS configuration', () => {
    it('should include CORS headers', () => {
      return request(app.getHttpServer())
        .options('/health')
        .expect(200)
        .expect((res) => {
          expect(res.headers).toHaveProperty('access-control-allow-origin');
        });
    });
  });
});
