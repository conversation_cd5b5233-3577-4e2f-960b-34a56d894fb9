# 🚀 OneCRM API - Enterprise-grade Multi-tenant CRM Backend

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![NestJS](https://img.shields.io/badge/NestJS-10+-red.svg)](https://nestjs.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7+-red.svg)](https://redis.io/)

A robust, scalable, and production-ready CRM API built with NestJS, TypeScript, and PostgreSQL. Features comprehensive logging, multi-tenancy, and enterprise-grade security.

## ✨ Features

### 🏗️ **Core Architecture**
- **Multi-tenant Architecture** - Complete tenant isolation with RLS
- **Microservices Ready** - Modular design for easy scaling
- **Hot Reload Development** - Instant feedback during development
- **Production Optimized** - Compression, security headers, clustering support

### 📊 **CRM Modules**
- **Organizations** - Multi-tenant organization management
- **Contacts** - Comprehensive contact management with custom fields
- **Companies** - Company profiles and relationship management
- **Deals** - Sales pipeline and opportunity tracking
- **Users** - User management with role-based access control
- **Activities** - Task and activity tracking with notifications

### 🔧 **Technical Features**
- **Robust Logging** - Structured JSON logging with Winston
- **Health Monitoring** - Comprehensive health checks and metrics
- **API Documentation** - Auto-generated Swagger/OpenAPI docs
- **Validation** - Request/response validation with class-validator
- **Error Handling** - Centralized error handling with detailed logging
- **Security** - Helmet, CORS, rate limiting, input sanitization

### 🗄️ **Database & Caching**
- **PostgreSQL** - Primary database with connection pooling
- **Redis** - Caching and session management
- **TypeORM** - Database ORM with migrations support
- **Row Level Security** - Database-level tenant isolation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 15+
- Redis 7+
- npm or yarn

### Development Setup

1. **Clone and Install**
```bash
git clone <repository-url>
cd backend/crm-api
npm install
```

2. **Environment Configuration**
```bash
cp .env.example .env
# Update .env with your configuration
```

3. **Database Setup**
```bash
# Start PostgreSQL and Redis (using Docker)
docker-compose up -d postgres redis

# Run migrations (when available)
npm run migration:run
```

4. **Start Development Server**
```bash
npm run dev
```

The API will be available at:
- **API**: http://localhost:3002
- **Documentation**: http://localhost:3002/api/docs
- **Health Check**: http://localhost:3002/api/health

## 📚 API Documentation

### Health Endpoints
- `GET /api/health` - Basic health check
- `GET /api/health/ready` - Readiness probe
- `GET /api/health/live` - Liveness probe  
- `GET /api/health/modules` - Detailed modules status

### Test Endpoints (Development)
- `GET /api/organizations/test` - Organizations module test
- `GET /api/contacts/test` - Contacts module test
- `GET /api/companies/test` - Companies module test

### Core Modules
- **Organizations**: `/api/organizations/*`
- **Contacts**: `/api/contacts/*`
- **Companies**: `/api/companies/*`
- **Deals**: `/api/deals/*`
- **Users**: `/api/users/*`
- **Activities**: `/api/activities/*`

## 🔧 Development

### Available Scripts
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start:prod   # Start production server
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

### Project Structure
```
src/
├── common/           # Shared utilities and services
│   ├── decorators/   # Custom decorators
│   ├── guards/       # Authentication & authorization guards
│   ├── interceptors/ # Request/response interceptors
│   ├── logger/       # Custom logging service
│   └── services/     # Shared services
├── config/           # Configuration files
├── modules/          # Feature modules
│   ├── health/       # Health check module
│   ├── organizations/# Organization management
│   ├── contacts/     # Contact management
│   ├── companies/    # Company management
│   ├── deals/        # Deal management
│   ├── users/        # User management
│   └── activities/   # Activity management
├── app.module.ts     # Root application module
└── main.ts          # Application entry point
```

## 🔒 Security Features

### Current Implementation
- **CORS Protection** - Configurable cross-origin resource sharing
- **Input Validation** - Comprehensive request validation
- **Error Sanitization** - Secure error responses
- **Request Logging** - Detailed request/response logging
- **Health Monitoring** - Application health endpoints

### Production Ready (Configurable)
- **Helmet Security Headers** - Security headers for production
- **Rate Limiting** - API rate limiting protection
- **Authentication** - JWT-based authentication (Keycloak ready)
- **Authorization** - Role-based access control
- **Audit Logging** - Comprehensive audit trail

## 📊 Logging & Monitoring

### Logging Features
- **Structured JSON Logs** - Machine-readable log format
- **Multiple Log Levels** - Debug, info, warn, error
- **Request Tracing** - Request ID tracking
- **Performance Monitoring** - Response time tracking
- **Error Tracking** - Detailed error logging with stack traces
- **Security Logging** - Authentication and authorization events

### Log Outputs
- **Development**: Colorized console output with detailed debugging
- **Production**: JSON logs with file rotation and compression

### Health Monitoring
- **Database Connectivity** - PostgreSQL connection status
- **Redis Connectivity** - Cache layer status
- **Module Status** - Individual module health
- **Performance Metrics** - Response times and throughput

## 🚀 Production Deployment

### Using the Deployment Script
```bash
# Make sure environment is configured
cp .env.example .env
# Update .env with production values

# Run deployment script
./scripts/production-deploy.sh
```

### Manual Deployment
```bash
# Install production dependencies
npm ci --only=production

# Build application
npm run build

# Start with PM2 (recommended)
pm2 start ecosystem.config.js

# Or start directly
npm run start:prod
```

### Environment Variables
Key production environment variables:
```bash
NODE_ENV=production
PORT=3002
DB_HOST=your-db-host
DB_PASSWORD=secure-password
JWT_SECRET=your-jwt-secret
REDIS_HOST=your-redis-host
```

## 🧪 Testing

### Running Tests
```bash
npm test              # Run all tests
npm run test:watch    # Watch mode
npm run test:cov      # With coverage
npm run test:e2e      # End-to-end tests
```

### Test Structure
- **Unit Tests** - Individual service and controller tests
- **Integration Tests** - Module integration tests
- **E2E Tests** - Full application flow tests

## 🔄 Hot Reload Development

The application supports hot reload in development mode:
- **File Changes** - Automatic restart on TypeScript changes
- **Error Recovery** - Graceful error handling and recovery
- **Fast Compilation** - Incremental TypeScript compilation
- **Live Debugging** - Real-time debugging with detailed logs

## 📈 Performance

### Optimizations
- **Connection Pooling** - Database connection optimization
- **Caching** - Redis-based caching layer
- **Compression** - Response compression in production
- **Clustering** - Multi-process support with PM2

### Monitoring
- **Response Times** - Request duration tracking
- **Memory Usage** - Memory consumption monitoring
- **Database Performance** - Query performance tracking
- **Error Rates** - Error frequency monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/api/docs`
- Review the health status at `/api/health/modules`
- Check application logs for detailed error information
- Refer to the troubleshooting section in the documentation

---

**OneCRM API** - Built with ❤️ using NestJS, TypeScript, and PostgreSQL
