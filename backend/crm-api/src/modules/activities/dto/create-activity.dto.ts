import { IsString, IsOptional, IsUUID, <PERSON><PERSON>ength, IsDateString, IsEnum, IsObject, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ActivityType {
  CALL = 'call',
  EMAIL = 'email',
  MEETING = 'meeting',
  TASK = 'task',
  NOTE = 'note',
  SMS = 'sms',
  DEMO = 'demo',
  PROPOSAL = 'proposal',
  FOLLOW_UP = 'follow-up',
  OTHER = 'other',
}

export enum ActivityStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue',
}

export enum ActivityPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export class CreateActivityDto {
  @ApiProperty({ description: 'Activity type', enum: ActivityType })
  @IsEnum(ActivityType)
  type: ActivityType;

  @ApiProperty({ description: 'Activity subject', example: 'Follow up call with prospect' })
  @IsString()
  @MaxLength(255)
  subject: string;

  @ApiProperty({ description: 'Activity description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Activity status', enum: ActivityStatus, default: ActivityStatus.PENDING, required: false })
  @IsOptional()
  @IsEnum(ActivityStatus)
  status?: ActivityStatus;

  @ApiProperty({ description: 'Activity priority', enum: ActivityPriority, default: ActivityPriority.MEDIUM, required: false })
  @IsOptional()
  @IsEnum(ActivityPriority)
  priority?: ActivityPriority;

  @ApiProperty({ description: 'Activity due date', example: '2024-12-31T10:00:00Z', required: false })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({ description: 'Activity start date', example: '2024-12-31T09:00:00Z', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: 'Activity end date', example: '2024-12-31T11:00:00Z', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: 'Activity duration in minutes', example: 60, required: false })
  @IsOptional()
  duration?: number;

  @ApiProperty({ description: 'Activity location', example: 'Conference Room A', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  location?: string;

  @ApiProperty({ description: 'Contact ID associated with this activity', required: false })
  @IsOptional()
  @IsUUID()
  contactId?: string;

  @ApiProperty({ description: 'Company ID associated with this activity', required: false })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ description: 'Deal ID associated with this activity', required: false })
  @IsOptional()
  @IsUUID()
  dealId?: string;

  @ApiProperty({ description: 'User ID assigned to this activity', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Whether this is an all-day activity', default: false, required: false })
  @IsOptional()
  @IsBoolean()
  isAllDay?: boolean;

  @ApiProperty({ description: 'Whether this activity is completed', default: false, required: false })
  @IsOptional()
  @IsBoolean()
  isCompleted?: boolean;

  @ApiProperty({ description: 'Custom fields as key-value pairs', required: false })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Activity outcome/result', required: false })
  @IsOptional()
  @IsString()
  outcome?: string;

  @ApiProperty({ description: 'Activity notes', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}
