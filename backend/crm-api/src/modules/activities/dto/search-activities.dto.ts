import { IsOptional, IsString, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsArray, IsEnum, IsUUID, IsDateString, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { ActivityType, ActivityStatus, ActivityPriority } from './create-activity.dto';

export enum ActivitySortField {
  SUBJECT = 'subject',
  TYPE = 'type',
  STATUS = 'status',
  PRIORITY = 'priority',
  DUE_DATE = 'dueDate',
  START_DATE = 'startDate',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class SearchActivitiesDto {
  @ApiProperty({ description: 'Search query for subject or description', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter by activity type', enum: ActivityType, required: false })
  @IsOptional()
  @IsEnum(ActivityType)
  type?: ActivityType;

  @ApiProperty({ description: 'Filter by activity status', enum: ActivityStatus, required: false })
  @IsOptional()
  @IsEnum(ActivityStatus)
  status?: ActivityStatus;

  @ApiProperty({ description: 'Filter by activity priority', enum: ActivityPriority, required: false })
  @IsOptional()
  @IsEnum(ActivityPriority)
  priority?: ActivityPriority;

  @ApiProperty({ description: 'Filter by assigned user ID', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Filter by contact ID', required: false })
  @IsOptional()
  @IsUUID()
  contactId?: string;

  @ApiProperty({ description: 'Filter by company ID', required: false })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ description: 'Filter by deal ID', required: false })
  @IsOptional()
  @IsUUID()
  dealId?: string;

  @ApiProperty({ description: 'Filter by completion status', required: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isCompleted?: boolean;

  @ApiProperty({ description: 'Filter by overdue activities', required: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isOverdue?: boolean;

  @ApiProperty({ description: 'Due date from', required: false })
  @IsOptional()
  @IsDateString()
  dueDateFrom?: string;

  @ApiProperty({ description: 'Due date to', required: false })
  @IsOptional()
  @IsDateString()
  dueDateTo?: string;

  @ApiProperty({ description: 'Start date from', required: false })
  @IsOptional()
  @IsDateString()
  startDateFrom?: string;

  @ApiProperty({ description: 'Start date to', required: false })
  @IsOptional()
  @IsDateString()
  startDateTo?: string;

  @ApiProperty({ description: 'Page number', minimum: 1, default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', minimum: 1, maximum: 100, default: 20, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({ description: 'Sort field', enum: ActivitySortField, default: ActivitySortField.CREATED_AT, required: false })
  @IsOptional()
  @IsEnum(ActivitySortField)
  sortBy?: ActivitySortField = ActivitySortField.CREATED_AT;

  @ApiProperty({ description: 'Sort order', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({ description: 'Include soft-deleted activities', default: false, required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}

export class ActivitiesResponseDto {
  @ApiProperty({ description: 'List of activities' })
  activities: any[];

  @ApiProperty({ description: 'Total number of activities' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there are more pages' })
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there are previous pages' })
  hasPrev: boolean;
}

export class ActivityStatsDto {
  @ApiProperty({ description: 'Total activities' })
  total: number;

  @ApiProperty({ description: 'Activities by type' })
  byType: Record<string, number>;

  @ApiProperty({ description: 'Activities by status' })
  byStatus: Record<string, number>;

  @ApiProperty({ description: 'Activities by priority' })
  byPriority: Record<string, number>;

  @ApiProperty({ description: 'Overdue activities count' })
  overdue: number;

  @ApiProperty({ description: 'Completed activities count' })
  completed: number;

  @ApiProperty({ description: 'Pending activities count' })
  pending: number;

  @ApiProperty({ description: 'Activities due today' })
  dueToday: number;

  @ApiProperty({ description: 'Activities due this week' })
  dueThisWeek: number;
}
