import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Organization } from '../organizations/organization.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { Deal } from '../deals/deal.entity';

@Entity('activities')
export class Activity {
  @ApiProperty({ description: 'Unique identifier for the activity' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Organization ID' })
  @Column({ name: 'org_id' })
  orgId: string;

  @ApiProperty({ description: 'Activity type' })
  @Column({ length: 50 })
  type: string;

  @ApiProperty({ description: 'Activity subject', required: false })
  @Column({ length: 255, nullable: true })
  subject?: string;

  @ApiProperty({ description: 'Activity description', required: false })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Activity status', required: false })
  @Column({ length: 50, default: 'pending' })
  status: string;

  @ApiProperty({ description: 'Activity priority', required: false })
  @Column({ length: 50, default: 'medium' })
  priority: string;

  @ApiProperty({ description: 'Activity due date', required: false })
  @Column({ type: 'timestamp', nullable: true, name: 'due_date' })
  dueDate?: Date;

  @ApiProperty({ description: 'Activity start date', required: false })
  @Column({ type: 'timestamp', nullable: true, name: 'start_date' })
  startDate?: Date;

  @ApiProperty({ description: 'Activity end date', required: false })
  @Column({ type: 'timestamp', nullable: true, name: 'end_date' })
  endDate?: Date;

  @ApiProperty({ description: 'Activity duration in minutes', required: false })
  @Column({ type: 'int', nullable: true })
  duration?: number;

  @ApiProperty({ description: 'Activity location', required: false })
  @Column({ length: 255, nullable: true })
  location?: string;

  @ApiProperty({ description: 'Whether this is an all-day activity', required: false })
  @Column({ type: 'boolean', default: false, name: 'is_all_day' })
  isAllDay: boolean;

  @ApiProperty({ description: 'Whether this activity is completed', required: false })
  @Column({ type: 'boolean', default: false, name: 'is_completed' })
  isCompleted: boolean;

  @ApiProperty({ description: 'Custom fields', required: false })
  @Column({ type: 'jsonb', default: '{}', name: 'custom_fields' })
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Activity outcome/result', required: false })
  @Column({ type: 'text', nullable: true })
  outcome?: string;

  @ApiProperty({ description: 'Activity notes', required: false })
  @Column({ type: 'text', nullable: true })
  notes?: string;

  @ApiProperty({ description: 'Contact ID', required: false })
  @Column({ name: 'contact_id', nullable: true })
  contactId?: string;

  @ApiProperty({ description: 'Company ID', required: false })
  @Column({ name: 'company_id', nullable: true })
  companyId?: string;

  @ApiProperty({ description: 'Deal ID', required: false })
  @Column({ name: 'deal_id', nullable: true })
  dealId?: string;

  @ApiProperty({ description: 'User ID', required: false })
  @Column({ name: 'user_id', nullable: true })
  userId?: string;

  @ApiProperty({ description: 'Assigned user ID', required: false })
  @Column({ name: 'assigned_to', nullable: true })
  assignedToId?: string;

  @ApiProperty({ description: 'Scheduled date/time', required: false })
  @Column({ type: 'timestamp', nullable: true, name: 'scheduled_at' })
  scheduledAt?: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Organization, (organization) => organization.activities)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo?: User;

  @ManyToOne(() => Contact)
  @JoinColumn({ name: 'contact_id' })
  contact?: Contact;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'company_id' })
  company?: Company;

  @ManyToOne(() => Deal)
  @JoinColumn({ name: 'deal_id' })
  deal?: Deal;
}
