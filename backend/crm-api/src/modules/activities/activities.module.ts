import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivitiesController } from './activities.controller';
import { ActivitiesService } from './activities.service';
import { Activity } from './activity.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { Deal } from '../deals/deal.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';

@Module({
  imports: [TypeOrmModule.forFeature([Activity, User, Contact, Company, Deal])],
  controllers: [ActivitiesController],
  providers: [ActivitiesService, TenantContextService],
  exports: [ActivitiesService],
})
export class ActivitiesModule {}
