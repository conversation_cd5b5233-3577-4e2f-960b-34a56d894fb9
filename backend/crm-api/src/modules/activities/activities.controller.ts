import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  ParseUUIDPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { ActivitiesService } from './activities.service';
import { Activity } from './activity.entity';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { SearchActivitiesDto, ActivitiesResponseDto, ActivityStatsDto } from './dto/search-activities.dto';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('activities')
@ApiBearerAuth()
@Controller('activities')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class ActivitiesController {
  constructor(private readonly activitiesService: ActivitiesService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint for activities module' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Activities module is working',
      timestamp: new Date().toISOString(),
    };
  }

  @Public()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get activities data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.activitiesService.getTestData();
  }

  @Post()
  // @RequirePermissions('activities:write')
  @ApiOperation({ summary: 'Create a new activity' })
  @ApiResponse({ status: 201, description: 'Activity created successfully', type: Activity })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(@Body() createActivityDto: CreateActivityDto): Promise<Activity> {
    return this.activitiesService.create(createActivityDto);
  }

  @Get()
  // @RequirePermissions('activities:read')
  @ApiOperation({ summary: 'Get all activities with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Activities retrieved successfully', type: ActivitiesResponseDto })
  @ApiQuery({ name: 'search', required: false, description: 'Search query for subject or description' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by activity type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by activity status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by activity priority' })
  @ApiQuery({ name: 'assignedToId', required: false, description: 'Filter by assigned user ID' })
  @ApiQuery({ name: 'contactId', required: false, description: 'Filter by contact ID' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filter by company ID' })
  @ApiQuery({ name: 'dealId', required: false, description: 'Filter by deal ID' })
  @ApiQuery({ name: 'isCompleted', required: false, description: 'Filter by completion status', type: Boolean })
  @ApiQuery({ name: 'isOverdue', required: false, description: 'Filter by overdue status', type: Boolean })
  @ApiQuery({ name: 'dueDateFrom', required: false, description: 'Due date from' })
  @ApiQuery({ name: 'dueDateTo', required: false, description: 'Due date to' })
  @ApiQuery({ name: 'startDateFrom', required: false, description: 'Start date from' })
  @ApiQuery({ name: 'startDateTo', required: false, description: 'Start date to' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  async findAll(@Query() searchDto: SearchActivitiesDto): Promise<ActivitiesResponseDto> {
    return this.activitiesService.findAll(searchDto);
  }

  @Get('stats')
  // @RequirePermissions('activities:read')
  @ApiOperation({ summary: 'Get activity statistics' })
  @ApiResponse({ status: 200, description: 'Activity statistics retrieved successfully', type: ActivityStatsDto })
  async getStats(): Promise<ActivityStatsDto> {
    return this.activitiesService.getActivityStats();
  }

  @Get('upcoming')
  // @RequirePermissions('activities:read')
  @ApiOperation({ summary: 'Get upcoming activities for current user' })
  @ApiResponse({ status: 200, description: 'Upcoming activities retrieved successfully', type: [Activity] })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days to look ahead (default: 7)', type: Number })
  async getUpcoming(@Query('days', new ParseIntPipe({ optional: true })) days?: number): Promise<Activity[]> {
    return this.activitiesService.getUpcomingActivities(days);
  }

  @Get('overdue')
  // @RequirePermissions('activities:read')
  @ApiOperation({ summary: 'Get overdue activities for current user' })
  @ApiResponse({ status: 200, description: 'Overdue activities retrieved successfully', type: [Activity] })
  async getOverdue(): Promise<Activity[]> {
    return this.activitiesService.getOverdueActivities();
  }

  @Get(':id')
  // @RequirePermissions('activities:read')
  @ApiOperation({ summary: 'Get activity by ID' })
  @ApiResponse({ status: 200, description: 'Activity retrieved successfully', type: Activity })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Activity> {
    return this.activitiesService.findById(id);
  }

  @Put(':id')
  // @RequirePermissions('activities:write')
  @ApiOperation({ summary: 'Update activity by ID' })
  @ApiResponse({ status: 200, description: 'Activity updated successfully', type: Activity })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateActivityDto: UpdateActivityDto
  ): Promise<Activity> {
    return this.activitiesService.update(id, updateActivityDto);
  }

  @Post(':id/complete')
  // @RequirePermissions('activities:write')
  @ApiOperation({ summary: 'Mark activity as completed' })
  @ApiResponse({ status: 200, description: 'Activity marked as completed successfully', type: Activity })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async markAsCompleted(@Param('id', ParseUUIDPipe) id: string): Promise<Activity> {
    return this.activitiesService.markAsCompleted(id);
  }

  @Delete(':id')
  // @RequirePermissions('activities:delete')
  @ApiOperation({ summary: 'Delete activity by ID (soft delete)' })
  @ApiResponse({ status: 200, description: 'Activity deleted successfully' })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.activitiesService.remove(id);
    return { message: 'Activity deleted successfully' };
  }
}
