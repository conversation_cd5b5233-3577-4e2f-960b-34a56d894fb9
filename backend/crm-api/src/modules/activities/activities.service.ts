import { Injectable, NotFoundException, ForbiddenException, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, Between, LessThan } from 'typeorm';
import { Activity } from './activity.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { Deal } from '../deals/deal.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';
import { CreateActivityDto, ActivityStatus } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { SearchActivitiesDto, ActivitiesResponseDto, ActivityStatsDto } from './dto/search-activities.dto';

@Injectable()
export class ActivitiesService {
  private readonly logger = new Logger(ActivitiesService.name);

  constructor(
    @InjectRepository(Activity)
    private activitiesRepository: Repository<Activity>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Contact)
    private contactsRepository: Repository<Contact>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    @InjectRepository(Deal)
    private dealsRepository: Repository<Deal>,
    private tenantContextService: TenantContextService,
  ) {}

  async create(createActivityDto: CreateActivityDto): Promise<Activity> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Validate assigned user belongs to the same organization
    if (createActivityDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: createActivityDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate contact belongs to the same organization
    if (createActivityDto.contactId) {
      const contact = await this.contactsRepository.findOne({
        where: { id: createActivityDto.contactId, orgId },
      });
      if (!contact) {
        throw new BadRequestException('Contact not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (createActivityDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: createActivityDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Validate deal belongs to the same organization
    if (createActivityDto.dealId) {
      const deal = await this.dealsRepository.findOne({
        where: { id: createActivityDto.dealId, orgId },
      });
      if (!deal) {
        throw new BadRequestException('Deal not found in your organization');
      }
    }

    // Set default values
    const status = createActivityDto.status || ActivityStatus.PENDING;
    const isCompleted = createActivityDto.isCompleted || status === ActivityStatus.COMPLETED;

    const activity = this.activitiesRepository.create({
      ...createActivityDto,
      status,
      isCompleted,
      orgId,
      userId: currentUserId,
      assignedToId: createActivityDto.assignedToId || currentUserId,
    });

    const savedActivity = await this.activitiesRepository.save(activity);
    
    this.logger.log(`Activity created: ${savedActivity.id} by user ${currentUserId}`);
    
    return this.findById(savedActivity.id);
  }

  async findAll(searchDto: SearchActivitiesDto): Promise<ActivitiesResponseDto> {
    const orgId = this.tenantContextService.getOrgId();
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = searchDto;

    const queryBuilder = this.activitiesRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.user', 'user')
      .leftJoinAndSelect('activity.assignedTo', 'assignedTo')
      .leftJoinAndSelect('activity.contact', 'contact')
      .leftJoinAndSelect('activity.company', 'company')
      .leftJoinAndSelect('activity.deal', 'deal')
      .where('activity.orgId = :orgId', { orgId });

    // Apply search filters
    this.applySearchFilters(queryBuilder, searchDto);

    // Apply sorting
    queryBuilder.orderBy(`activity.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [activities, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      activities,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findById(id: string): Promise<Activity> {
    const orgId = this.tenantContextService.getOrgId();
    
    const activity = await this.activitiesRepository.findOne({
      where: { id, orgId },
      relations: ['user', 'assignedTo', 'contact', 'company', 'deal'],
    });

    if (!activity) {
      throw new NotFoundException(`Activity with ID ${id} not found`);
    }

    return activity;
  }

  async update(id: string, updateActivityDto: UpdateActivityDto): Promise<Activity> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if activity exists and belongs to the organization
    const existingActivity = await this.findById(id);

    // Check permissions - users can update activities they created or are assigned to, admins can update all
    const canUpdate = this.tenantContextService.isAdmin() || 
                     existingActivity.userId === currentUserId ||
                     existingActivity.assignedToId === currentUserId;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this activity');
    }

    // Validate assigned user belongs to the same organization
    if (updateActivityDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: updateActivityDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate contact belongs to the same organization
    if (updateActivityDto.contactId) {
      const contact = await this.contactsRepository.findOne({
        where: { id: updateActivityDto.contactId, orgId },
      });
      if (!contact) {
        throw new BadRequestException('Contact not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (updateActivityDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: updateActivityDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Validate deal belongs to the same organization
    if (updateActivityDto.dealId) {
      const deal = await this.dealsRepository.findOne({
        where: { id: updateActivityDto.dealId, orgId },
      });
      if (!deal) {
        throw new BadRequestException('Deal not found in your organization');
      }
    }

    // Update completion status based on status
    if (updateActivityDto.status) {
      updateActivityDto.isCompleted = updateActivityDto.status === ActivityStatus.COMPLETED;
    }

    await this.activitiesRepository.update({ id, orgId }, updateActivityDto);

    this.logger.log(`Activity updated: ${id} by user ${currentUserId}`);

    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if activity exists and belongs to the organization
    const existingActivity = await this.findById(id);

    // Check permissions - users can delete activities they created, admins can delete all
    const canDelete = this.tenantContextService.isAdmin() || 
                     existingActivity.userId === currentUserId;

    if (!canDelete) {
      throw new ForbiddenException('You do not have permission to delete this activity');
    }

    // Soft delete
    await this.activitiesRepository.softDelete({ id, orgId });

    this.logger.log(`Activity deleted: ${id} by user ${currentUserId}`);
  }

  async markAsCompleted(id: string): Promise<Activity> {
    const updateDto: UpdateActivityDto = {
      status: ActivityStatus.COMPLETED,
      isCompleted: true,
    };

    return this.update(id, updateDto);
  }

  async getUpcomingActivities(days: number = 7): Promise<Activity[]> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();
    
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + days);

    return this.activitiesRepository.find({
      where: {
        orgId,
        assignedToId: currentUserId,
        dueDate: Between(startDate, endDate),
        isCompleted: false,
      },
      relations: ['contact', 'company', 'deal'],
      order: { dueDate: 'ASC' },
    });
  }

  async getOverdueActivities(): Promise<Activity[]> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();
    const now = new Date();

    return this.activitiesRepository.find({
      where: {
        orgId,
        assignedToId: currentUserId,
        dueDate: LessThan(now),
        isCompleted: false,
      },
      relations: ['contact', 'company', 'deal'],
      order: { dueDate: 'ASC' },
    });
  }

  async getActivityStats(): Promise<ActivityStatsDto> {
    const orgId = this.tenantContextService.getOrgId();

    // Get total count
    const total = await this.activitiesRepository.count({ where: { orgId } });

    // Get counts by type
    const typeCounts = await this.activitiesRepository
      .createQueryBuilder('activity')
      .select('activity.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('activity.orgId = :orgId', { orgId })
      .groupBy('activity.type')
      .getRawMany();

    const byType = typeCounts.reduce((acc, item) => {
      acc[item.type] = parseInt(item.count);
      return acc;
    }, {});

    // Get counts by status
    const statusCounts = await this.activitiesRepository
      .createQueryBuilder('activity')
      .select('activity.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('activity.orgId = :orgId', { orgId })
      .groupBy('activity.status')
      .getRawMany();

    const byStatus = statusCounts.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {});

    // Get counts by priority
    const priorityCounts = await this.activitiesRepository
      .createQueryBuilder('activity')
      .select('activity.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .where('activity.orgId = :orgId', { orgId })
      .groupBy('activity.priority')
      .getRawMany();

    const byPriority = priorityCounts.reduce((acc, item) => {
      acc[item.priority] = parseInt(item.count);
      return acc;
    }, {});

    // Get overdue count
    const now = new Date();
    const overdue = await this.activitiesRepository.count({
      where: {
        orgId,
        dueDate: LessThan(now),
        isCompleted: false,
      },
    });

    // Get completed count
    const completed = await this.activitiesRepository.count({
      where: { orgId, isCompleted: true },
    });

    // Get pending count
    const pending = await this.activitiesRepository.count({
      where: { orgId, isCompleted: false },
    });

    // Get due today count
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const dueToday = await this.activitiesRepository.count({
      where: {
        orgId,
        dueDate: Between(todayStart, todayEnd),
        isCompleted: false,
      },
    });

    // Get due this week count
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weekEnd = new Date();
    weekEnd.setDate(weekEnd.getDate() + (6 - weekEnd.getDay()));
    weekEnd.setHours(23, 59, 59, 999);

    const dueThisWeek = await this.activitiesRepository.count({
      where: {
        orgId,
        dueDate: Between(weekStart, weekEnd),
        isCompleted: false,
      },
    });

    return {
      total,
      byType,
      byStatus,
      byPriority,
      overdue,
      completed,
      pending,
      dueToday,
      dueThisWeek,
    };
  }

  private applySearchFilters(
    queryBuilder: SelectQueryBuilder<Activity>,
    searchDto: SearchActivitiesDto
  ): void {
    const {
      search,
      type,
      status,
      priority,
      assignedToId,
      contactId,
      companyId,
      dealId,
      isCompleted,
      isOverdue,
      dueDateFrom,
      dueDateTo,
      startDateFrom,
      startDateTo,
      includeDeleted,
    } = searchDto;

    // Include deleted activities if requested
    if (!includeDeleted) {
      queryBuilder.andWhere('activity.deletedAt IS NULL');
    }

    // General search across subject and description
    if (search) {
      queryBuilder.andWhere(
        '(activity.subject ILIKE :search OR activity.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Filter by type
    if (type) {
      queryBuilder.andWhere('activity.type = :type', { type });
    }

    // Filter by status
    if (status) {
      queryBuilder.andWhere('activity.status = :status', { status });
    }

    // Filter by priority
    if (priority) {
      queryBuilder.andWhere('activity.priority = :priority', { priority });
    }

    // Filter by assigned user
    if (assignedToId) {
      queryBuilder.andWhere('activity.assignedToId = :assignedToId', { assignedToId });
    }

    // Filter by contact
    if (contactId) {
      queryBuilder.andWhere('activity.contactId = :contactId', { contactId });
    }

    // Filter by company
    if (companyId) {
      queryBuilder.andWhere('activity.companyId = :companyId', { companyId });
    }

    // Filter by deal
    if (dealId) {
      queryBuilder.andWhere('activity.dealId = :dealId', { dealId });
    }

    // Filter by completion status
    if (isCompleted !== undefined) {
      queryBuilder.andWhere('activity.isCompleted = :isCompleted', { isCompleted });
    }

    // Filter by overdue status
    if (isOverdue) {
      const now = new Date();
      queryBuilder.andWhere('activity.dueDate < :now AND activity.isCompleted = false', { now });
    }

    // Filter by due date range
    if (dueDateFrom || dueDateTo) {
      if (dueDateFrom && dueDateTo) {
        queryBuilder.andWhere('activity.dueDate BETWEEN :dueDateFrom AND :dueDateTo', {
          dueDateFrom,
          dueDateTo,
        });
      } else if (dueDateFrom) {
        queryBuilder.andWhere('activity.dueDate >= :dueDateFrom', { dueDateFrom });
      } else if (dueDateTo) {
        queryBuilder.andWhere('activity.dueDate <= :dueDateTo', { dueDateTo });
      }
    }

    // Filter by start date range
    if (startDateFrom || startDateTo) {
      if (startDateFrom && startDateTo) {
        queryBuilder.andWhere('activity.startDate BETWEEN :startDateFrom AND :startDateTo', {
          startDateFrom,
          startDateTo,
        });
      } else if (startDateFrom) {
        queryBuilder.andWhere('activity.startDate >= :startDateFrom', { startDateFrom });
      } else if (startDateTo) {
        queryBuilder.andWhere('activity.startDate <= :startDateTo', { startDateTo });
      }
    }
  }

  /**
   * Test method to get activities data directly without tenant context
   */
  async getTestData() {
    try {
      const activities = await this.activitiesRepository
        .createQueryBuilder('activity')
        .leftJoinAndSelect('activity.user', 'user')
        .leftJoinAndSelect('activity.contact', 'contact')
        .leftJoinAndSelect('activity.deal', 'deal')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: activities.length,
        data: activities,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
