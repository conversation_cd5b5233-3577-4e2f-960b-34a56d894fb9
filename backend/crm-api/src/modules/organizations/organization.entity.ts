import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { Deal } from '../deals/deal.entity';
import { Activity } from '../activities/activity.entity';

@Entity('organizations')
export class Organization {
  @ApiProperty({ description: 'Unique identifier for the organization' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Organization name' })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({ description: 'Organization slug for URLs' })
  @Column({ length: 100, unique: true })
  slug: string;

  @ApiProperty({ description: 'Subscription plan', enum: ['free', 'pro', 'enterprise'] })
  @Column({ length: 50, default: 'free' })
  plan: string;

  @ApiProperty({ description: 'Keycloak group identifier', required: false })
  @Column({ name: 'keycloak_group', length: 255, nullable: true })
  keycloakGroup?: string;

  @ApiProperty({ description: 'Organization settings as JSON', required: false })
  @Column({ type: 'jsonb', default: {} })
  settings: Record<string, any>;

  @ApiProperty({ description: 'Maximum number of users allowed' })
  @Column({ name: 'max_users', default: 5 })
  maxUsers: number;

  @ApiProperty({ description: 'Maximum storage in GB' })
  @Column({ name: 'max_storage_gb', default: 1 })
  maxStorageGb: number;

  @ApiProperty({ description: 'Organization features as JSON', required: false })
  @Column({ type: 'jsonb', default: {} })
  features: Record<string, any>;

  @ApiProperty({ description: 'Organization status' })
  @Column({ default: 'active' })
  status: string;

  @ApiProperty({ description: 'Whether organization is suspended' })
  @Column({ default: false })
  suspended: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @OneToMany(() => User, (user) => user.organization)
  users: User[];

  @OneToMany(() => Contact, (contact) => contact.organization)
  contacts: Contact[];

  @OneToMany(() => Company, (company) => company.organization)
  companies: Company[];

  @OneToMany(() => Deal, (deal) => deal.organization)
  deals: Deal[];

  @OneToMany(() => Activity, (activity) => activity.organization)
  activities: Activity[];
}
