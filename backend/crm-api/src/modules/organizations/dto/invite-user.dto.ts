import { Is<PERSON><PERSON>, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  VIEWER = 'viewer',
}

export class InviteUserDto {
  @ApiProperty({ description: 'Email address of the user to invite', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Role to assign to the user', enum: UserRole, default: UserRole.USER })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}

export class AcceptInvitationDto {
  @ApiProperty({ description: 'Invitation token' })
  token: string;

  @ApiProperty({ description: 'User first name', required: false })
  @IsOptional()
  firstName?: string;

  @ApiProperty({ description: 'User last name', required: false })
  @IsOptional()
  lastName?: string;
}
