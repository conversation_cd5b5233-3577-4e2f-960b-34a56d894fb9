import { Is<PERSON>tring, IsE<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>num, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum OrganizationPlan {
  FREE = 'free',
  PRO = 'pro',
  ENTERPRISE = 'enterprise',
}

export class CreateOrganizationDto {
  @ApiProperty({ description: 'Organization name', example: 'Acme Corporation' })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: 'Organization slug for URLs', example: 'acme-corp' })
  @IsString()
  @MaxLength(100)
  @Matches(/^[a-z0-9-]+$/, { message: 'Slug must contain only lowercase letters, numbers, and hyphens' })
  slug: string;

  @ApiProperty({ description: 'Subscription plan', enum: OrganizationPlan, default: OrganizationPlan.FREE })
  @IsOptional()
  @IsEnum(OrganizationPlan)
  plan?: OrganizationPlan;

  @ApiProperty({ description: 'Maximum number of users', example: 10, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxUsers?: number;

  @ApiProperty({ description: 'Maximum storage in GB', example: 5, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxStorageGb?: number;

  @ApiProperty({ description: 'Billing email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  billingEmail?: string;

  @ApiProperty({ description: 'Organization features', required: false })
  @IsOptional()
  features?: Record<string, any>;

  @ApiProperty({ description: 'Organization settings', required: false })
  @IsOptional()
  settings?: Record<string, any>;
}
