import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { OrganizationsService } from './organizations.service';
import { Organization } from './organization.entity';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { Permission, UserRole } from '../../common/enums/roles.enum';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';
import { Unprotected } from 'nest-keycloak-connect';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto, UpdateOrganizationSettingsDto } from './dto/update-organization.dto';
import { InviteUserDto } from './dto/invite-user.dto';

@ApiTags('organizations')
@ApiBearerAuth()
@Controller('organizations')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  // @RequirePermissions(Permission.ORG_READ)
  // @Roles(UserRole.ORG_VIEWER, UserRole.ORG_USER, UserRole.ORG_MANAGER, UserRole.ORG_ADMIN)
  @Get('current')
  @ApiOperation({ summary: 'Get current organization' })
  @ApiResponse({ status: 200, description: 'Organization retrieved successfully', type: Organization })
  async getCurrentOrganization(): Promise<Organization> {
    return this.organizationsService.getCurrentOrganization();
  }

  @Public()
  @Unprotected()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint without authentication' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Organizations module is working',
      timestamp: new Date().toISOString(),
      endpoints: [
        'GET /api/organizations/test',
        'GET /api/organizations/test-data',
        'GET /api/health',
      ],
    };
  }

  @Public()
  @Unprotected()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get organizations data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.organizationsService.getTestData();
  }

  @Get('stats')
  // @RequirePermissions('organizations:read')
  @ApiOperation({ summary: 'Get organization statistics' })
  @ApiResponse({ status: 200, description: 'Organization statistics retrieved successfully' })
  async getOrganizationStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    totalContacts: number;
    totalCompanies: number;
    totalDeals: number;
    storageUsed: number;
  }> {
    return this.organizationsService.getOrganizationStats();
  }

  @Post()
  // @Roles('admin')
  // @RequirePermissions('organizations:write')
  @ApiOperation({ summary: 'Create new organization' })
  @ApiResponse({ status: 201, description: 'Organization created successfully', type: Organization })
  async createOrganization(@Body() createDto: CreateOrganizationDto): Promise<Organization> {
    return this.organizationsService.create(createDto);
  }

  @Put()
  // @Roles('admin')
  // @RequirePermissions('organizations:write')
  @ApiOperation({ summary: 'Update current organization' })
  @ApiResponse({ status: 200, description: 'Organization updated successfully', type: Organization })
  async updateOrganization(@Body() updateData: UpdateOrganizationDto): Promise<Organization> {
    return this.organizationsService.update(updateData);
  }

  @Put('settings')
  // @Roles('admin')
  // @RequirePermissions('organizations:write')
  @ApiOperation({ summary: 'Update organization settings' })
  @ApiResponse({ status: 200, description: 'Settings updated successfully', type: Organization })
  async updateSettings(@Body() settingsDto: UpdateOrganizationSettingsDto): Promise<Organization> {
    return this.organizationsService.updateSettings(settingsDto.settings);
  }

  @Post('invite')
  // @Roles('admin')
  // @RequirePermissions('users:write')
  @ApiOperation({ summary: 'Invite user to organization' })
  @ApiResponse({ status: 200, description: 'User invitation sent successfully' })
  async inviteUser(@Body() inviteDto: InviteUserDto): Promise<{ message: string; invitationId: string }> {
    return this.organizationsService.inviteUser(inviteDto);
  }
}
