import { Injectable, NotFoundException, ForbiddenException, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, ILike, In } from 'typeorm';
import { Contact } from './contact.entity';
import { User } from '../users/user.entity';
import { Company } from '../companies/company.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { SearchContactsDto, ContactsResponseDto } from './dto/search-contacts.dto';

@Injectable()
export class ContactsService {
  private readonly logger = new Logger(ContactsService.name);

  constructor(
    @InjectRepository(Contact)
    private contactsRepository: Repository<Contact>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    private tenantContextService: TenantContextService,
  ) {}

  async create(createContactDto: CreateContactDto): Promise<Contact> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Validate assigned user belongs to the same organization
    if (createContactDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: createContactDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (createContactDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: createContactDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Remove company field as it's handled by companyId relation
    const { company, ...contactData } = createContactDto;

    const contact = this.contactsRepository.create({
      ...contactData,
      orgId,
      createdById: currentUserId,
      updatedById: currentUserId,
    });

    const savedContact = await this.contactsRepository.save(contact) as Contact;

    this.logger.log(`Contact created: ${savedContact.id} by user ${currentUserId}`);

    return this.findById(savedContact.id);
  }

  async findAll(searchDto: SearchContactsDto): Promise<ContactsResponseDto> {
    const orgId = this.tenantContextService.getOrgId();
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = searchDto;

    const queryBuilder = this.contactsRepository
      .createQueryBuilder('contact')
      .leftJoinAndSelect('contact.assignedTo', 'assignedTo')
      .leftJoinAndSelect('contact.createdBy', 'createdBy')
      .leftJoinAndSelect('contact.company', 'company')
      .where('contact.orgId = :orgId', { orgId });

    // Apply search filters
    this.applySearchFilters(queryBuilder, searchDto);

    // Apply sorting
    queryBuilder.orderBy(`contact.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [contacts, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      contacts,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findById(id: string): Promise<Contact> {
    const orgId = this.tenantContextService.getOrgId();
    
    const contact = await this.contactsRepository.findOne({
      where: { id, orgId },
      relations: ['assignedTo', 'createdBy', 'updatedBy', 'company'],
    });

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    return contact;
  }

  async update(id: string, updateContactDto: UpdateContactDto): Promise<Contact> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if contact exists and belongs to the organization
    const existingContact = await this.findById(id);

    // Check permissions - users can update contacts they created or are assigned to, admins can update all
    const canUpdate = this.tenantContextService.isAdmin() || 
                     existingContact.createdById === currentUserId ||
                     existingContact.assignedToId === currentUserId;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this contact');
    }

    // Validate assigned user belongs to the same organization
    if (updateContactDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: updateContactDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (updateContactDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: updateContactDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Remove company field as it's handled by companyId relation
    const { company, ...updateData } = updateContactDto;

    await this.contactsRepository.update(
      { id, orgId },
      { ...updateData, updatedById: currentUserId }
    );

    this.logger.log(`Contact updated: ${id} by user ${currentUserId}`);

    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if contact exists and belongs to the organization
    const existingContact = await this.findById(id);

    // Check permissions - users can delete contacts they created, admins can delete all
    const canDelete = this.tenantContextService.isAdmin() || 
                     existingContact.createdById === currentUserId;

    if (!canDelete) {
      throw new ForbiddenException('You do not have permission to delete this contact');
    }

    // Soft delete
    await this.contactsRepository.softDelete({ id, orgId });

    this.logger.log(`Contact deleted: ${id} by user ${currentUserId}`);
  }

  async restore(id: string): Promise<Contact> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Only admins can restore contacts
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can restore contacts');
    }

    await this.contactsRepository.restore({ id, orgId });

    this.logger.log(`Contact restored: ${id} by user ${currentUserId}`);

    return this.findById(id);
  }

  async bulkDelete(ids: string[]): Promise<{ deleted: number }> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Only admins can bulk delete
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can bulk delete contacts');
    }

    const result = await this.contactsRepository.softDelete({
      id: In(ids),
      orgId,
    });

    this.logger.log(`Bulk delete: ${result.affected} contacts by user ${currentUserId}`);

    return { deleted: result.affected || 0 };
  }

  async getContactStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    bySource: Record<string, number>;
    recentlyCreated: number;
  }> {
    const orgId = this.tenantContextService.getOrgId();

    // Get total count
    const total = await this.contactsRepository.count({ where: { orgId } });

    // Get counts by lead status
    const statusCounts = await this.contactsRepository
      .createQueryBuilder('contact')
      .select('contact.leadStatus', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('contact.orgId = :orgId', { orgId })
      .groupBy('contact.leadStatus')
      .getRawMany();

    const byStatus = statusCounts.reduce((acc, item) => {
      acc[item.status || 'unknown'] = parseInt(item.count);
      return acc;
    }, {});

    // Get counts by lead source
    const sourceCounts = await this.contactsRepository
      .createQueryBuilder('contact')
      .select('contact.leadSource', 'source')
      .addSelect('COUNT(*)', 'count')
      .where('contact.orgId = :orgId', { orgId })
      .groupBy('contact.leadSource')
      .getRawMany();

    const bySource = sourceCounts.reduce((acc, item) => {
      acc[item.source || 'unknown'] = parseInt(item.count);
      return acc;
    }, {});

    // Get recently created count (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentlyCreated = await this.contactsRepository.count({
      where: {
        orgId,
        createdAt: { $gte: thirtyDaysAgo } as any,
      },
    });

    return {
      total,
      byStatus,
      bySource,
      recentlyCreated,
    };
  }

  private applySearchFilters(
    queryBuilder: SelectQueryBuilder<Contact>,
    searchDto: SearchContactsDto
  ): void {
    const { search, company, leadStatus, leadSource, assignedToId, tags, includeDeleted } = searchDto;

    // Include deleted contacts if requested
    if (!includeDeleted) {
      queryBuilder.andWhere('contact.deletedAt IS NULL');
    }

    // General search across name, email, and company
    if (search) {
      queryBuilder.andWhere(
        '(contact.firstName ILIKE :search OR contact.lastName ILIKE :search OR contact.email ILIKE :search OR contact.company ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Filter by company
    if (company) {
      queryBuilder.andWhere('contact.company ILIKE :company', { company: `%${company}%` });
    }

    // Filter by lead status
    if (leadStatus) {
      queryBuilder.andWhere('contact.leadStatus = :leadStatus', { leadStatus });
    }

    // Filter by lead source
    if (leadSource) {
      queryBuilder.andWhere('contact.leadSource = :leadSource', { leadSource });
    }

    // Filter by assigned user
    if (assignedToId) {
      queryBuilder.andWhere('contact.assignedToId = :assignedToId', { assignedToId });
    }

    // Filter by tags
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('contact.tags && :tags', { tags });
    }
  }

  /**
   * Test method to get contacts data directly without tenant context
   */
  async getTestData() {
    try {
      const contacts = await this.contactsRepository
        .createQueryBuilder('contact')
        .leftJoinAndSelect('contact.company', 'company')
        .leftJoinAndSelect('contact.assignedTo', 'assignedTo')
        .leftJoinAndSelect('contact.createdBy', 'createdBy')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: contacts.length,
        data: contacts,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
