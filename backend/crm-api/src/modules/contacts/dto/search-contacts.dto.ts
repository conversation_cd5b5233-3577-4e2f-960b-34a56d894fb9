import { IsOptional, IsS<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsA<PERSON>y, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export enum ContactSortField {
  FIRST_NAME = 'firstName',
  LAST_NAME = 'lastName',
  EMAIL = 'email',
  COMPANY = 'company',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class SearchContactsDto {
  @ApiProperty({ description: 'Search query for name, email, or company', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter by company name', required: false })
  @IsOptional()
  @IsString()
  company?: string;

  @ApiProperty({ description: 'Filter by lead status', required: false })
  @IsOptional()
  @IsString()
  leadStatus?: string;

  @ApiProperty({ description: 'Filter by lead source', required: false })
  @IsOptional()
  @IsString()
  leadSource?: string;

  @ApiProperty({ description: 'Filter by assigned user ID', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Filter by tags', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  tags?: string[];

  @ApiProperty({ description: 'Page number', minimum: 1, default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', minimum: 1, maximum: 100, default: 20, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({ description: 'Sort field', enum: ContactSortField, default: ContactSortField.CREATED_AT, required: false })
  @IsOptional()
  @IsEnum(ContactSortField)
  sortBy?: ContactSortField = ContactSortField.CREATED_AT;

  @ApiProperty({ description: 'Sort order', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({ description: 'Include soft-deleted contacts', default: false, required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}

export class ContactsResponseDto {
  @ApiProperty({ description: 'List of contacts' })
  contacts: any[];

  @ApiProperty({ description: 'Total number of contacts' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there are more pages' })
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there are previous pages' })
  hasPrev: boolean;
}
