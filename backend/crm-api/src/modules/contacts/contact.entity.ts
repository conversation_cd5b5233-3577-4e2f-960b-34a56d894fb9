import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Organization } from '../organizations/organization.entity';
import { User } from '../users/user.entity';
import { Company } from '../companies/company.entity';

@Entity('contacts')
export class Contact {
  @ApiProperty({ description: 'Unique identifier for the contact' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Organization ID' })
  @Column({ name: 'org_id' })
  orgId: string;

  @ApiProperty({ description: 'Contact first name', required: false })
  @Column({ name: 'first_name', length: 100, nullable: true })
  firstName?: string;

  @ApiProperty({ description: 'Contact last name', required: false })
  @Column({ name: 'last_name', length: 100, nullable: true })
  lastName?: string;

  @ApiProperty({ description: 'Contact email address', required: false })
  @Column({ length: 255, nullable: true })
  email?: string;

  @ApiProperty({ description: 'Contact phone number', required: false })
  @Column({ length: 50, nullable: true })
  phone?: string;

  @ApiProperty({ description: 'Company ID', required: false })
  @Column({ name: 'company_id', nullable: true })
  companyId?: string;

  @ApiProperty({ description: 'Contact title', required: false })
  @Column({ length: 255, nullable: true })
  title?: string;

  @ApiProperty({ description: 'Contact notes', required: false })
  @Column({ type: 'text', nullable: true })
  notes?: string;

  @ApiProperty({ description: 'Contact tags', required: false })
  @Column({ type: 'jsonb', default: '[]' })
  tags?: string[];

  @ApiProperty({ description: 'Custom fields', required: false })
  @Column({ type: 'jsonb', default: '{}', name: 'custom_fields' })
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Lead source', required: false })
  @Column({ length: 100, nullable: true, name: 'lead_source' })
  leadSource?: string;

  @ApiProperty({ description: 'Lead status', required: false })
  @Column({ length: 100, nullable: true, name: 'lead_status' })
  leadStatus?: string;

  @ApiProperty({ description: 'User who created the contact', required: false })
  @Column({ name: 'created_by', nullable: true })
  createdById?: string;

  @ApiProperty({ description: 'User who last updated the contact', required: false })
  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @ApiProperty({ description: 'User assigned to this contact', required: false })
  @Column({ name: 'assigned_to', nullable: true })
  assignedToId?: string;



  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Organization, (organization) => organization.contacts)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @ManyToOne(() => User, (user) => user.createdContacts)
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: User;

  @ManyToOne(() => User, (user) => user.assignedContacts)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo?: User;

  @ManyToOne(() => Company, (company) => company.contacts)
  @JoinColumn({ name: 'company_id' })
  company?: Company;
}
