import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from './user.entity';
import { Organization } from '../organizations/organization.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';

@Module({
  imports: [TypeOrmModule.forFeature([User, Organization])],
  controllers: [UsersController],
  providers: [UsersService, TenantContextService],
  exports: [UsersService],
})
export class UsersModule {}
