import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { UsersService } from './users.service';
import { User } from './user.entity';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { CurrentOrg } from '../../common/decorators/current-org.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('users')
@ApiBearerAuth()
@Controller('users')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint for users module' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Users module is working',
      timestamp: new Date().toISOString(),
    };
  }

  @Public()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get users data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.usersService.getTestData();
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully', type: User })
  async getCurrentProfile(@CurrentUser() user: CurrentUserData): Promise<User> {
    return this.usersService.findById(user.id);
  }

  @Get('profile/:keycloakSub')
  @ApiOperation({ summary: 'Get user profile by Keycloak subject' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully', type: User })
  async getProfileByKeycloakSub(@Param('keycloakSub') keycloakSub: string): Promise<User | null> {
    return this.usersService.findByKeycloakSub(keycloakSub);
  }

  @Get('organization')
  // @Roles('admin')
  // @RequirePermissions('users:read')
  @ApiOperation({ summary: 'Get all users in current organization' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully', type: [User] })
  async getUsersByOrganization(): Promise<User[]> {
    return this.usersService.findByOrganization();
  }

  @Get(':id')
  // @RequirePermissions('users:read')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully', type: User })
  async getUserById(@Param('id') id: string): Promise<User> {
    return this.usersService.findById(id);
  }

  @Put('profile')
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully', type: User })
  async updateProfile(
    @CurrentUser() user: CurrentUserData,
    @Body() updateData: { firstName?: string; lastName?: string }
  ): Promise<User> {
    return this.usersService.updateProfile(user.id, updateData);
  }

  @Post(':id/deactivate')
  // @Roles('admin')
  // @RequirePermissions('users:write')
  @ApiOperation({ summary: 'Deactivate user' })
  @ApiResponse({ status: 200, description: 'User deactivated successfully' })
  async deactivateUser(@Param('id') id: string): Promise<{ message: string }> {
    await this.usersService.deactivateUser(id);
    return { message: 'User deactivated successfully' };
  }

  @Post(':id/activate')
  // @Roles('admin')
  // @RequirePermissions('users:write')
  @ApiOperation({ summary: 'Activate user' })
  @ApiResponse({ status: 200, description: 'User activated successfully' })
  async activateUser(@Param('id') id: string): Promise<{ message: string }> {
    await this.usersService.activateUser(id);
    return { message: 'User activated successfully' };
  }
}
