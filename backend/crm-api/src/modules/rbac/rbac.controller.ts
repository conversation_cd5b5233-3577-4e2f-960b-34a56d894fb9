import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RbacService, UserContext } from '../../common/services/rbac.service';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { Public } from '../../common/decorators/public.decorator';
import { Permission, UserRole } from '../../common/enums/roles.enum';

@ApiTags('rbac')
@ApiBearerAuth()
@Controller('rbac')
export class RbacController {
  constructor(private readonly rbacService: RbacService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test RBAC system functionality' })
  @ApiResponse({ status: 200, description: 'RBAC test results' })
  async testRbac() {
    return {
      status: 'ok',
      message: 'RBAC system is working',
      timestamp: new Date().toISOString(),
      availableRoles: Object.values(UserRole),
      availablePermissions: Object.values(Permission),
    };
  }

  @Public()
  @Get('roles')
  @ApiOperation({ summary: 'Get all available roles with descriptions' })
  @ApiResponse({ status: 200, description: 'List of all roles' })
  async getAllRoles() {
    const roles = Object.values(UserRole).map(role => ({
      role,
      ...this.rbacService.getRoleInfo(role),
      permissions: this.rbacService.getUserPermissions(role),
    }));

    return {
      count: roles.length,
      data: roles,
    };
  }

  @Public()
  @Get('permissions')
  @ApiOperation({ summary: 'Get all available permissions' })
  @ApiResponse({ status: 200, description: 'List of all permissions' })
  async getAllPermissions() {
    const permissions = Object.values(Permission).map(permission => ({
      permission,
      description: this.getPermissionDescription(permission),
    }));

    return {
      count: permissions.length,
      data: permissions,
    };
  }

  @Public()
  @Post('test-user-permissions')
  @ApiOperation({ summary: 'Test permissions for a mock user' })
  @ApiResponse({ status: 200, description: 'Permission test results' })
  async testUserPermissions(@Body() testData: {
    role: UserRole;
    orgId: string;
    permissionsToTest: Permission[];
  }) {
    const mockUser: UserContext = {
      id: 'test-user-123',
      email: '<EMAIL>',
      role: testData.role,
      orgId: testData.orgId,
      isActive: true,
    };

    const results = testData.permissionsToTest.map(permission => ({
      permission,
      granted: this.rbacService.hasPermission(mockUser, permission),
    }));

    return {
      user: mockUser,
      roleInfo: this.rbacService.getRoleInfo(testData.role),
      userPermissions: this.rbacService.getUserPermissions(testData.role),
      testResults: results,
      summary: {
        total: results.length,
        granted: results.filter(r => r.granted).length,
        denied: results.filter(r => !r.granted).length,
      },
    };
  }

  @Public()
  @Get('role-hierarchy/:role')
  @ApiOperation({ summary: 'Get role hierarchy for a specific role' })
  @ApiResponse({ status: 200, description: 'Role hierarchy information' })
  async getRoleHierarchy(@Param('role') role: UserRole) {
    const roleInfo = this.rbacService.getRoleInfo(role);
    const permissions = this.rbacService.getUserPermissions(role);
    const assignableRoles = this.rbacService.getAssignableRoles(role);

    return {
      role,
      roleInfo,
      permissions: permissions.length,
      permissionsList: permissions,
      canAssignRoles: assignableRoles,
      canAssignCount: assignableRoles.length,
    };
  }

  // @RequirePermissions(Permission.USER_READ)
  // @Roles(UserRole.ORG_MANAGER, UserRole.ORG_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SUPER_ADMIN)
  @Get('protected/user-management')
  @ApiOperation({ summary: 'Protected endpoint - requires user management permissions' })
  @ApiResponse({ status: 200, description: 'User management data' })
  async protectedUserManagement(@Request() req) {
    return {
      message: 'Access granted to user management',
      user: req.user,
      timestamp: new Date().toISOString(),
      requiredPermissions: [Permission.USER_READ],
      requiredRoles: [UserRole.ORG_MANAGER, UserRole.ORG_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SUPER_ADMIN],
    };
  }

  // @RequirePermissions(Permission.DEAL_CREATE, Permission.CONTACT_CREATE)
  // @Roles(UserRole.SALES_REP, UserRole.SALES_MANAGER)
  @Get('protected/sales-operations')
  @ApiOperation({ summary: 'Protected endpoint - requires sales permissions' })
  @ApiResponse({ status: 200, description: 'Sales operations data' })
  async protectedSalesOperations(@Request() req) {
    return {
      message: 'Access granted to sales operations',
      user: req.user,
      timestamp: new Date().toISOString(),
      requiredPermissions: [Permission.DEAL_CREATE, Permission.CONTACT_CREATE],
      requiredRoles: [UserRole.SALES_REP, UserRole.SALES_MANAGER],
    };
  }

  // @RequirePermissions(Permission.SYSTEM_ADMIN)
  // @Roles(UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN)
  @Get('protected/system-admin')
  @ApiOperation({ summary: 'Protected endpoint - requires system admin permissions' })
  @ApiResponse({ status: 200, description: 'System admin data' })
  async protectedSystemAdmin(@Request() req) {
    return {
      message: 'Access granted to system administration',
      user: req.user,
      timestamp: new Date().toISOString(),
      requiredPermissions: [Permission.SYSTEM_ADMIN],
      requiredRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
    };
  }

  @Public()
  @Post('simulate-role-assignment')
  @ApiOperation({ summary: 'Simulate role assignment validation' })
  @ApiResponse({ status: 200, description: 'Role assignment simulation results' })
  async simulateRoleAssignment(@Body() assignmentData: {
    assignerRole: UserRole;
    targetRole: UserRole;
    userId: string;
    orgId: string;
  }) {
    const canAssign = this.rbacService.canAssignRole(
      assignmentData.assignerRole,
      assignmentData.targetRole
    );

    const assignableRoles = this.rbacService.getAssignableRoles(assignmentData.assignerRole);

    return {
      assignment: assignmentData,
      canAssign,
      reason: canAssign 
        ? 'Assignment allowed' 
        : `${assignmentData.assignerRole} cannot assign ${assignmentData.targetRole}`,
      assignerInfo: this.rbacService.getRoleInfo(assignmentData.assignerRole),
      targetInfo: this.rbacService.getRoleInfo(assignmentData.targetRole),
      allAssignableRoles: assignableRoles,
    };
  }

  private getPermissionDescription(permission: Permission): string {
    const descriptions: Record<Permission, string> = {
      [Permission.ORG_CREATE]: 'Create new organizations',
      [Permission.ORG_READ]: 'View organization information',
      [Permission.ORG_UPDATE]: 'Modify organization settings',
      [Permission.ORG_DELETE]: 'Delete organizations',
      [Permission.ORG_MANAGE_USERS]: 'Manage organization users',
      [Permission.ORG_MANAGE_SETTINGS]: 'Configure organization settings',
      [Permission.USER_CREATE]: 'Create new users',
      [Permission.USER_READ]: 'View user information',
      [Permission.USER_UPDATE]: 'Modify user details',
      [Permission.USER_DELETE]: 'Delete users',
      [Permission.USER_MANAGE_ROLES]: 'Assign and modify user roles',
      [Permission.CONTACT_CREATE]: 'Create new contacts',
      [Permission.CONTACT_READ]: 'View contact information',
      [Permission.CONTACT_UPDATE]: 'Modify contact details',
      [Permission.CONTACT_DELETE]: 'Delete contacts',
      [Permission.CONTACT_EXPORT]: 'Export contact data',
      [Permission.CONTACT_IMPORT]: 'Import contact data',
      [Permission.COMPANY_CREATE]: 'Create new companies',
      [Permission.COMPANY_READ]: 'View company information',
      [Permission.COMPANY_UPDATE]: 'Modify company details',
      [Permission.COMPANY_DELETE]: 'Delete companies',
      [Permission.COMPANY_EXPORT]: 'Export company data',
      [Permission.COMPANY_IMPORT]: 'Import company data',
      [Permission.DEAL_CREATE]: 'Create new deals',
      [Permission.DEAL_READ]: 'View deal information',
      [Permission.DEAL_UPDATE]: 'Modify deal details',
      [Permission.DEAL_DELETE]: 'Delete deals',
      [Permission.DEAL_MANAGE_PIPELINE]: 'Configure sales pipeline',
      [Permission.DEAL_VIEW_REPORTS]: 'Access deal reports',
      [Permission.ACTIVITY_CREATE]: 'Create new activities',
      [Permission.ACTIVITY_READ]: 'View activity information',
      [Permission.ACTIVITY_UPDATE]: 'Modify activity details',
      [Permission.ACTIVITY_DELETE]: 'Delete activities',
      [Permission.ACTIVITY_ASSIGN]: 'Assign activities to users',
      [Permission.REPORT_VIEW_BASIC]: 'View basic reports',
      [Permission.REPORT_VIEW_ADVANCED]: 'View advanced analytics',
      [Permission.REPORT_CREATE]: 'Create custom reports',
      [Permission.REPORT_EXPORT]: 'Export report data',
      [Permission.SYSTEM_ADMIN]: 'System administration',
      [Permission.SYSTEM_SETTINGS]: 'System configuration',
      [Permission.SYSTEM_LOGS]: 'Access system logs',
      [Permission.SYSTEM_BACKUP]: 'Backup and restore',
      [Permission.API_ACCESS]: 'Basic API access',
      [Permission.API_ADMIN]: 'Administrative API access',
    };

    return descriptions[permission] || 'Unknown permission';
  }
}
