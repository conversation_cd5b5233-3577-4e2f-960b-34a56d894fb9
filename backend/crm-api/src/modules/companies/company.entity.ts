import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Organization } from '../organizations/organization.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Deal } from '../deals/deal.entity';

@Entity('companies')
export class Company {
  @ApiProperty({ description: 'Unique identifier for the company' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Organization ID' })
  @Column({ name: 'org_id' })
  orgId: string;

  @ApiProperty({ description: 'Company name' })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({ description: 'Company domain', required: false })
  @Column({ length: 255, nullable: true })
  domain?: string;

  @ApiProperty({ description: 'Company industry', required: false })
  @Column({ length: 100, nullable: true })
  industry?: string;

  @ApiProperty({ description: 'Company size', required: false })
  @Column({ length: 50, nullable: true })
  size?: string;

  @ApiProperty({ description: 'Company description', required: false })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Company website', required: false })
  @Column({ length: 255, nullable: true })
  website?: string;

  @ApiProperty({ description: 'Company phone number', required: false })
  @Column({ length: 50, nullable: true })
  phone?: string;

  @ApiProperty({ description: 'Company address', required: false })
  @Column({ type: 'jsonb', default: '{}' })
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };

  @ApiProperty({ description: 'Company tags', required: false })
  @Column({ type: 'jsonb', default: '[]' })
  tags?: string[];

  @ApiProperty({ description: 'Custom fields', required: false })
  @Column({ type: 'jsonb', default: '{}', name: 'custom_fields' })
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Annual revenue in USD', required: false })
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true, name: 'annual_revenue' })
  annualRevenue?: number;

  @ApiProperty({ description: 'Number of employees', required: false })
  @Column({ type: 'int', nullable: true, name: 'employee_count' })
  employeeCount?: number;

  @ApiProperty({ description: 'User who created the company', required: false })
  @Column({ name: 'created_by', nullable: true })
  createdById?: string;

  @ApiProperty({ description: 'User who last updated the company', required: false })
  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @ApiProperty({ description: 'User assigned to this company', required: false })
  @Column({ name: 'assigned_to', nullable: true })
  assignedToId?: string;

  @ApiProperty({ description: 'Parent company for hierarchy', required: false })
  @Column({ name: 'parent_company_id', nullable: true })
  parentCompanyId?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Organization, (organization) => organization.companies)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @ManyToOne(() => User, (user) => user.createdCompanies)
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: User;

  @ManyToOne(() => User, (user) => user.assignedCompanies)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo?: User;

  @ManyToOne(() => Company, (company) => company.subsidiaries)
  @JoinColumn({ name: 'parent_company_id' })
  parentCompany?: Company;

  @OneToMany(() => Company, (company) => company.parentCompany)
  subsidiaries: Company[];

  @OneToMany(() => Contact, (contact) => contact.company)
  contacts: Contact[];

  @OneToMany(() => Deal, (deal) => deal.company)
  deals: Deal[];
}
