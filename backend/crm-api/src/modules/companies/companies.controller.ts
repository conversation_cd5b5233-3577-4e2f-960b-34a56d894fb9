import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { CompaniesService } from './companies.service';
import { Company } from './company.entity';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { SearchCompaniesDto, CompaniesResponseDto } from './dto/search-companies.dto';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('companies')
@ApiBearerAuth()
@Controller('companies')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint for companies module' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Companies module is working',
      timestamp: new Date().toISOString(),
    };
  }

  @Public()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get companies data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.companiesService.getTestData();
  }

  @Post()
  // @RequirePermissions('companies:write')
  @ApiOperation({ summary: 'Create a new company' })
  @ApiResponse({ status: 201, description: 'Company created successfully', type: Company })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(@Body() createCompanyDto: CreateCompanyDto): Promise<Company> {
    return this.companiesService.create(createCompanyDto);
  }

  @Get()
  // @RequirePermissions('companies:read')
  @ApiOperation({ summary: 'Get all companies with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Companies retrieved successfully', type: CompaniesResponseDto })
  @ApiQuery({ name: 'search', required: false, description: 'Search query for name, domain, or industry' })
  @ApiQuery({ name: 'industry', required: false, description: 'Filter by industry' })
  @ApiQuery({ name: 'size', required: false, description: 'Filter by company size' })
  @ApiQuery({ name: 'assignedToId', required: false, description: 'Filter by assigned user ID' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'minRevenue', required: false, description: 'Minimum annual revenue', type: Number })
  @ApiQuery({ name: 'maxRevenue', required: false, description: 'Maximum annual revenue', type: Number })
  @ApiQuery({ name: 'minEmployees', required: false, description: 'Minimum employee count', type: Number })
  @ApiQuery({ name: 'maxEmployees', required: false, description: 'Maximum employee count', type: Number })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  async findAll(@Query() searchDto: SearchCompaniesDto): Promise<CompaniesResponseDto> {
    return this.companiesService.findAll(searchDto);
  }

  @Get('stats')
  // @RequirePermissions('companies:read')
  @ApiOperation({ summary: 'Get company statistics' })
  @ApiResponse({ status: 200, description: 'Company statistics retrieved successfully' })
  async getStats(): Promise<{
    total: number;
    byIndustry: Record<string, number>;
    bySize: Record<string, number>;
    totalRevenue: number;
    averageRevenue: number;
    recentlyCreated: number;
  }> {
    return this.companiesService.getCompanyStats();
  }

  @Get(':id')
  // @RequirePermissions('companies:read')
  @ApiOperation({ summary: 'Get company by ID' })
  @ApiResponse({ status: 200, description: 'Company retrieved successfully', type: Company })
  @ApiResponse({ status: 404, description: 'Company not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Company> {
    return this.companiesService.findById(id);
  }

  @Put(':id')
  // @RequirePermissions('companies:write')
  @ApiOperation({ summary: 'Update company by ID' })
  @ApiResponse({ status: 200, description: 'Company updated successfully', type: Company })
  @ApiResponse({ status: 404, description: 'Company not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCompanyDto: UpdateCompanyDto
  ): Promise<Company> {
    return this.companiesService.update(id, updateCompanyDto);
  }

  @Delete(':id')
  // @RequirePermissions('companies:delete')
  @ApiOperation({ summary: 'Delete company by ID (soft delete)' })
  @ApiResponse({ status: 200, description: 'Company deleted successfully' })
  @ApiResponse({ status: 404, description: 'Company not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 400, description: 'Cannot delete company with subsidiaries' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.companiesService.remove(id);
    return { message: 'Company deleted successfully' };
  }
}
