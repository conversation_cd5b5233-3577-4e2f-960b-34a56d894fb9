import { IsOptional, IsString, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsArray, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export enum CompanySortField {
  NAME = 'name',
  DOMAIN = 'domain',
  INDUSTRY = 'industry',
  SIZE = 'size',
  ANNUAL_REVENUE = 'annualRevenue',
  EMPLOYEE_COUNT = 'employeeCount',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class SearchCompaniesDto {
  @ApiProperty({ description: 'Search query for name, domain, or industry', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter by industry', required: false })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ description: 'Filter by company size', required: false })
  @IsOptional()
  @IsString()
  size?: string;

  @ApiProperty({ description: 'Filter by assigned user ID', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Filter by tags', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  tags?: string[];

  @ApiProperty({ description: 'Minimum annual revenue', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  minRevenue?: number;

  @ApiProperty({ description: 'Maximum annual revenue', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  maxRevenue?: number;

  @ApiProperty({ description: 'Minimum employee count', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  minEmployees?: number;

  @ApiProperty({ description: 'Maximum employee count', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  maxEmployees?: number;

  @ApiProperty({ description: 'Page number', minimum: 1, default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', minimum: 1, maximum: 100, default: 20, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({ description: 'Sort field', enum: CompanySortField, default: CompanySortField.CREATED_AT, required: false })
  @IsOptional()
  @IsEnum(CompanySortField)
  sortBy?: CompanySortField = CompanySortField.CREATED_AT;

  @ApiProperty({ description: 'Sort order', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({ description: 'Include soft-deleted companies', default: false, required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}

export class CompaniesResponseDto {
  @ApiProperty({ description: 'List of companies' })
  companies: any[];

  @ApiProperty({ description: 'Total number of companies' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there are more pages' })
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there are previous pages' })
  hasPrev: boolean;
}
