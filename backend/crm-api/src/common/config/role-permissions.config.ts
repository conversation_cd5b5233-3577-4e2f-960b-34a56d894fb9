import { UserRole, Permission } from '../enums/roles.enum';

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: [
    // All permissions
    Permission.ORG_CREATE,
    Permission.ORG_READ,
    Permission.ORG_UPDATE,
    Permission.ORG_DELETE,
    Permission.ORG_MANAGE_USERS,
    Permission.ORG_MANAGE_SETTINGS,
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.USER_MANAGE_ROLES,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_DELETE,
    Permission.CONTACT_EXPORT,
    Permission.CONTACT_IMPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_DELETE,
    Permission.COMPANY_EXPORT,
    Permission.COMPANY_IMPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.DEAL_DELETE,
    Permission.DEAL_MANAGE_PIPELINE,
    Permission.DEAL_VIEW_REPORTS,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED,
    Permission.REPORT_CREATE,
    Permission.REPORT_EXPORT,
    Permission.SYSTEM_ADMIN,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_LOGS,
    Permission.SYSTEM_BACKUP,
    Permission.API_ACCESS,
    Permission.API_ADMIN
  ],

  [UserRole.SYSTEM_ADMIN]: [
    Permission.ORG_CREATE,
    Permission.ORG_READ,
    Permission.ORG_UPDATE,
    Permission.ORG_DELETE,
    Permission.ORG_MANAGE_USERS,
    Permission.ORG_MANAGE_SETTINGS,
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.USER_MANAGE_ROLES,
    Permission.SYSTEM_ADMIN,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_LOGS,
    Permission.SYSTEM_BACKUP,
    Permission.API_ACCESS,
    Permission.API_ADMIN
  ],

  [UserRole.ORG_ADMIN]: [
    Permission.ORG_READ,
    Permission.ORG_UPDATE,
    Permission.ORG_MANAGE_USERS,
    Permission.ORG_MANAGE_SETTINGS,
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    Permission.USER_MANAGE_ROLES,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_DELETE,
    Permission.CONTACT_EXPORT,
    Permission.CONTACT_IMPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_DELETE,
    Permission.COMPANY_EXPORT,
    Permission.COMPANY_IMPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.DEAL_DELETE,
    Permission.DEAL_MANAGE_PIPELINE,
    Permission.DEAL_VIEW_REPORTS,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED,
    Permission.REPORT_CREATE,
    Permission.REPORT_EXPORT,
    Permission.API_ACCESS
  ],

  [UserRole.ORG_MANAGER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_DELETE,
    Permission.CONTACT_EXPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_DELETE,
    Permission.COMPANY_EXPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.DEAL_DELETE,
    Permission.DEAL_MANAGE_PIPELINE,
    Permission.DEAL_VIEW_REPORTS,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED,
    Permission.REPORT_CREATE,
    Permission.REPORT_EXPORT
  ],

  [UserRole.ORG_USER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_EXPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_EXPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.REPORT_VIEW_BASIC
  ],

  [UserRole.ORG_VIEWER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_READ,
    Permission.COMPANY_READ,
    Permission.DEAL_READ,
    Permission.ACTIVITY_READ,
    Permission.REPORT_VIEW_BASIC
  ],

  [UserRole.SALES_MANAGER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_DELETE,
    Permission.CONTACT_EXPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_DELETE,
    Permission.COMPANY_EXPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.DEAL_DELETE,
    Permission.DEAL_MANAGE_PIPELINE,
    Permission.DEAL_VIEW_REPORTS,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED,
    Permission.REPORT_CREATE,
    Permission.REPORT_EXPORT
  ],

  [UserRole.SALES_REP]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_EXPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_EXPORT,
    Permission.DEAL_CREATE,
    Permission.DEAL_READ,
    Permission.DEAL_UPDATE,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.REPORT_VIEW_BASIC
  ],

  [UserRole.MARKETING_MANAGER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_DELETE,
    Permission.CONTACT_EXPORT,
    Permission.CONTACT_IMPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_DELETE,
    Permission.COMPANY_EXPORT,
    Permission.COMPANY_IMPORT,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED,
    Permission.REPORT_CREATE,
    Permission.REPORT_EXPORT
  ],

  [UserRole.MARKETING_USER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_CREATE,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.CONTACT_EXPORT,
    Permission.COMPANY_CREATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.COMPANY_EXPORT,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.REPORT_VIEW_BASIC
  ],

  [UserRole.SUPPORT_MANAGER]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.ACTIVITY_DELETE,
    Permission.ACTIVITY_ASSIGN,
    Permission.REPORT_VIEW_BASIC,
    Permission.REPORT_VIEW_ADVANCED
  ],

  [UserRole.SUPPORT_AGENT]: [
    Permission.ORG_READ,
    Permission.USER_READ,
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE,
    Permission.COMPANY_READ,
    Permission.COMPANY_UPDATE,
    Permission.ACTIVITY_CREATE,
    Permission.ACTIVITY_READ,
    Permission.ACTIVITY_UPDATE,
    Permission.REPORT_VIEW_BASIC
  ],

  [UserRole.API_USER]: [
    Permission.API_ACCESS,
    Permission.CONTACT_READ,
    Permission.COMPANY_READ,
    Permission.DEAL_READ,
    Permission.ACTIVITY_READ
  ],

  [UserRole.GUEST]: [
    Permission.ORG_READ,
    Permission.CONTACT_READ,
    Permission.COMPANY_READ
  ]
};
