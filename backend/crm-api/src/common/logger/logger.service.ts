import { Injectable, LoggerService, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

export interface LogContext {
  userId?: string;
  organizationId?: string;
  requestId?: string;
  method?: string;
  url?: string;
  userAgent?: string;
  ip?: string;
  duration?: number;
  statusCode?: number;
  [key: string]: any;
}

@Injectable()
export class CustomLoggerService implements LoggerService {
  private logger: winston.Logger;
  private isDevelopment: boolean;

  constructor(private configService: ConfigService) {
    this.isDevelopment = this.configService.get('NODE_ENV') === 'development';
    this.initializeLogger();
  }

  private initializeLogger() {
    const logLevel = this.isDevelopment ? 'debug' : 'info';
    
    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.errors({ stack: true }),
      winston.format.colorize(),
      winston.format.printf(({ timestamp, level, message, context, stack, ...meta }) => {
        let log = `${timestamp} [${level}]`;
        
        if (context) {
          log += ` [${context}]`;
        }
        
        log += ` ${message}`;
        
        // Add metadata in development
        if (this.isDevelopment && Object.keys(meta).length > 0) {
          log += `\n${JSON.stringify(meta, null, 2)}`;
        }
        
        if (stack) {
          log += `\n${stack}`;
        }
        
        return log;
      })
    );

    // JSON format for production
    const jsonFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    );

    const transports: winston.transport[] = [
      // Console transport
      new winston.transports.Console({
        format: this.isDevelopment ? consoleFormat : jsonFormat,
        level: logLevel,
      }),
    ];

    // File transports for production
    if (!this.isDevelopment) {
      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          format: jsonFormat,
          maxSize: '20m',
          maxFiles: '14d',
          zippedArchive: true,
        })
      );

      // Combined logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/combined-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          format: jsonFormat,
          maxSize: '20m',
          maxFiles: '30d',
          zippedArchive: true,
        })
      );

      // Access logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/access-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'http',
          format: jsonFormat,
          maxSize: '20m',
          maxFiles: '7d',
          zippedArchive: true,
        })
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true })
      ),
      transports,
      // Don't exit on handled exceptions
      exitOnError: false,
    });

    // Handle uncaught exceptions and unhandled rejections
    if (!this.isDevelopment) {
      this.logger.exceptions.handle(
        new DailyRotateFile({
          filename: 'logs/exceptions-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '30d',
        })
      );

      this.logger.rejections.handle(
        new DailyRotateFile({
          filename: 'logs/rejections-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '30d',
        })
      );
    }
  }

  log(message: any, context?: string, meta?: LogContext) {
    this.logger.info(message, { context, ...meta });
  }

  error(message: any, stack?: string, context?: string, meta?: LogContext) {
    this.logger.error(message, { context, stack, ...meta });
  }

  warn(message: any, context?: string, meta?: LogContext) {
    this.logger.warn(message, { context, ...meta });
  }

  debug(message: any, context?: string, meta?: LogContext) {
    if (this.isDevelopment) {
      this.logger.debug(message, { context, ...meta });
    }
  }

  verbose(message: any, context?: string, meta?: LogContext) {
    if (this.isDevelopment) {
      this.logger.verbose(message, { context, ...meta });
    }
  }

  // HTTP request logging
  logRequest(req: any, res: any, duration: number) {
    const logData: LogContext = {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      duration,
      statusCode: res.statusCode,
      userId: req.user?.id,
      organizationId: req.user?.organizationId || req.headers['x-organization-id'],
      requestId: req.headers['x-request-id'],
    };

    const message = `${req.method} ${req.url} ${res.statusCode} - ${duration}ms`;
    
    if (res.statusCode >= 400) {
      this.error(message, undefined, 'HTTP', logData);
    } else {
      this.logger.log('http', message, logData);
    }
  }

  // Database operation logging
  logDatabaseOperation(operation: string, table: string, duration: number, context?: LogContext) {
    if (this.isDevelopment) {
      this.debug(`DB ${operation} on ${table} - ${duration}ms`, 'Database', context);
    }
  }

  // Authentication logging
  logAuth(event: string, userId?: string, organizationId?: string, details?: any) {
    this.log(`Auth: ${event}`, 'Authentication', {
      userId,
      organizationId,
      ...details,
    });
  }

  // Business logic logging
  logBusiness(event: string, context: string, meta?: LogContext) {
    this.log(`Business: ${event}`, context, meta);
  }

  // Performance logging
  logPerformance(operation: string, duration: number, context?: string, meta?: LogContext) {
    const level = duration > 1000 ? 'warn' : 'debug';
    const message = `Performance: ${operation} took ${duration}ms`;
    
    if (level === 'warn') {
      this.warn(message, context, meta);
    } else {
      this.debug(message, context, meta);
    }
  }

  // Security logging
  logSecurity(event: string, severity: 'low' | 'medium' | 'high' | 'critical', meta?: LogContext) {
    const message = `Security: ${event}`;
    
    switch (severity) {
      case 'critical':
      case 'high':
        this.error(message, undefined, 'Security', { severity, ...meta });
        break;
      case 'medium':
        this.warn(message, 'Security', { severity, ...meta });
        break;
      default:
        this.log(message, 'Security', { severity, ...meta });
    }
  }

  // Error with context
  logError(error: Error, context?: string, meta?: LogContext) {
    this.error(
      error.message,
      error.stack,
      context,
      {
        name: error.name,
        ...meta,
      }
    );
  }

  // Structured logging for debugging
  logDebug(message: string, data: any, context?: string) {
    if (this.isDevelopment) {
      this.debug(message, context, { debugData: data });
    }
  }
}
