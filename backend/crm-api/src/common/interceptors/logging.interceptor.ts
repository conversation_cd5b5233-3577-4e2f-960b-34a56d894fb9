import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { CustomLoggerService } from '../logger/logger.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: CustomLoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    // Generate request ID if not present
    if (!request.headers['x-request-id']) {
      request.headers['x-request-id'] = uuidv4();
    }

    const requestId = request.headers['x-request-id'];
    const method = request.method;
    const url = request.url;
    const userAgent = request.get('User-Agent');
    const ip = request.ip || request.connection.remoteAddress;

    // Log request start in development
    this.logger.debug(
      `Incoming request: ${method} ${url}`,
      'HTTP',
      {
        requestId,
        method,
        url,
        userAgent,
        ip,
        body: this.sanitizeBody(request.body),
        query: request.query,
        params: request.params,
      }
    );

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        
        // Log successful response
        this.logger.logRequest(request, response, duration);
        
        // Log response data in development
        this.logger.debug(
          `Response: ${method} ${url} ${response.statusCode}`,
          'HTTP',
          {
            requestId,
            duration,
            responseSize: JSON.stringify(data).length,
            responseData: this.sanitizeResponse(data),
          }
        );
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        
        // Log error response
        this.logger.error(
          `Request failed: ${method} ${url}`,
          error.stack,
          'HTTP',
          {
            requestId,
            method,
            url,
            duration,
            errorName: error.name,
            errorMessage: error.message,
            statusCode: error.status || 500,
          }
        );
        
        throw error;
      })
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  private sanitizeResponse(data: any): any {
    if (!data || typeof data !== 'object') return data;
    
    // Limit response data size in logs
    const stringified = JSON.stringify(data);
    if (stringified.length > 1000) {
      return '[LARGE_RESPONSE]';
    }
    
    return data;
  }
}
