import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ForbiddenException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, finalize } from 'rxjs/operators';
import { TenantContextService } from '../services/tenant-context.service';

@Injectable()
export class TenantInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TenantInterceptor.name);

  constructor(private readonly tenantContextService: TenantContextService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Extract tenant information from various sources
    const orgIdFromHeader = request.headers['x-org-id'];
    const orgIdFromUser = request.user?.orgId;
    const orgIdFromToken = request.user?.org_id;
    const userId = request.user?.id || request.user?.sub;
    const userRole = request.user?.role || 'user';
    const userEmail = request.user?.email;

    // Determine the organization ID
    const orgId = orgIdFromHeader || orgIdFromUser || orgIdFromToken;

    // Skip tenant context for public endpoints
    const isPublicEndpoint = this.isPublicEndpoint(request.path);

    if (!isPublicEndpoint && !orgId) {
      console.log('No organization ID found in request', {
        path: request.path,
        headers: request.headers,
        user: request.user,
      });

      // In development mode, allow requests without org context
      if (process.env.NODE_ENV !== 'development') {
        throw new ForbiddenException('Organization context required');
      }
    }

    // Validate that user belongs to the organization
    if (orgIdFromUser && orgIdFromHeader && orgIdFromUser !== orgIdFromHeader) {
      console.log('Organization ID mismatch', {
        userOrgId: orgIdFromUser,
        headerOrgId: orgIdFromHeader,
        userId,
        path: request.path,
      });
      throw new ForbiddenException('Access denied to this organization');
    }

    // Set tenant context if we have the required information
    if (orgId && userId) {
      const tenantContext = {
        userId,
        orgId,
        role: userRole,
        email: userEmail,
        isAdmin: userRole === 'admin',
      };

      await this.tenantContextService.setContext(tenantContext);

      // Add tenant context to request for other middleware
      request.tenantContext = tenantContext;

      console.log('Tenant context established', {
        userId,
        orgId,
        role: userRole,
        path: request.path,
      });
    } else if (process.env.NODE_ENV === 'development' && !isPublicEndpoint) {
      // In development mode, set a default tenant context for testing
      const defaultTenantContext = {
        userId: userId || 'dev-user-001',
        orgId: orgId || 'dev-org-001',
        role: userRole || 'admin',
        email: userEmail || '<EMAIL>',
        isAdmin: true,
      };

      await this.tenantContextService.setContext(defaultTenantContext);
      request.tenantContext = defaultTenantContext;

      console.log('Development tenant context established', {
        userId: defaultTenantContext.userId,
        orgId: defaultTenantContext.orgId,
        role: defaultTenantContext.role,
        path: request.path,
      });
    }

    // Add tenant headers to response
    if (orgId) {
      response.setHeader('X-Tenant-Id', orgId);
    }

    // Add request tracking
    const requestId = request.headers['x-request-id'] ||
                     request.headers['kong-request-id'] ||
                     this.generateRequestId();
    response.setHeader('X-Request-Id', requestId);

    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;

        this.logger.debug('Request processed successfully', {
          requestId,
          method: request.method,
          path: request.path,
          orgId,
          userId,
          duration: `${duration}ms`,
          statusCode: response.statusCode,
        });
      }),
      finalize(async () => {
        // Clear tenant context after request
        if (orgId && userId) {
          await this.tenantContextService.clearContext();
        }
      }),
    );
  }

  /**
   * Check if the endpoint is public (doesn't require tenant context)
   */
  private isPublicEndpoint(path: string): boolean {
    const publicPaths = [
      '/health',
      '/api/health',
      '/api/docs',
      '/api/auth/callback',
      '/api/auth/login',
      '/api/auth/logout',
    ];

    return publicPaths.some(publicPath => path.startsWith(publicPath));
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
