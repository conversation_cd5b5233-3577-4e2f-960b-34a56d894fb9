import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';
import { RbacService, UserContext } from '../services/rbac.service';
import { Permission } from '../enums/roles.enum';

@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(
    private reflector: Reflector,
    private rbacService: RbacService
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Skip permission check in development mode as per guidelines
    if (process.env.NODE_ENV === 'development') {
      this.logger.debug('Permissions guard bypassed in development mode');
      return true;
    }

    // Check if user has any of the required permissions
    const hasPermission = this.rbacService.hasAnyPermission(user, requiredPermissions);

    if (!hasPermission) {
      this.logger.warn('Permission denied', {
        userId: user.id,
        userRole: user.role,
        userOrgId: user.orgId,
        requiredPermissions,
        path: request.path,
      });
      throw new ForbiddenException(
        `Insufficient permissions. Required: ${requiredPermissions.join(', ')}`
      );
    }

    this.logger.debug('Permission granted', {
      userId: user.id,
      userRole: user.role,
      userOrgId: user.orgId,
      requiredPermissions,
    });

    return true;
  }
}
