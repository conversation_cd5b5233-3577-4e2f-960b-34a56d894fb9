import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class CustomAuthGuard implements CanActivate {
  private readonly logger = new Logger(CustomAuthGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      this.logger.debug('Public endpoint accessed, skipping authentication');
      return true;
    }

    // For development mode, allow all requests as per guidelines
    if (process.env.NODE_ENV === 'development') {
      this.logger.debug('Development mode: Authentication bypassed for non-public endpoints');
      return true;
    }

    // In production, this guard would be replaced by proper Keycloak AuthGuard
    // For now, we'll allow all requests in development
    this.logger.warn('Production authentication not fully configured - allowing request');
    return true;
  }
}
