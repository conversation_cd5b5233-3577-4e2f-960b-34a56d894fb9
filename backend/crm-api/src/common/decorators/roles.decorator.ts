import { SetMetadata } from '@nestjs/common';
import { UserRole } from '../enums/roles.enum';

export const ROLES_KEY = 'roles';

/**
 * Decorator to require specific roles for accessing an endpoint
 * @param roles - Array of roles required (user needs at least one)
 */
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);

/**
 * Decorator to require minimum role level
 * @param role - Minimum role required (includes higher roles in hierarchy)
 */
export const MinRole = (role: UserRole) => SetMetadata(ROLES_KEY, [role]);
