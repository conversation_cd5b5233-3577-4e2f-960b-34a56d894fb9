import { SetMetadata } from '@nestjs/common';
import { Permission } from '../enums/roles.enum';

export const PERMISSIONS_KEY = 'permissions';

/**
 * Decorator to require specific permissions for accessing an endpoint
 * @param permissions - Array of permissions required
 */
export const RequirePermissions = (...permissions: Permission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * Decorator to require any of the specified permissions (OR logic)
 * @param permissions - Array of permissions, user needs at least one
 */
export const RequireAnyPermission = (...permissions: Permission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * Decorator to require all of the specified permissions (AND logic)
 * @param permissions - Array of permissions, user needs all of them
 */
export const RequireAllPermissions = (...permissions: Permission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);
