import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantContextService } from './services/tenant-context.service';
import { TenantInterceptor } from './interceptors/tenant.interceptor';
import { TenantGuard } from './guards/tenant.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { CustomAuthGuard } from './guards/custom-auth.guard';
import { RbacService } from './services/rbac.service';
import { SeedingService } from './services/seeding.service';
import { SeedingController } from './controllers/seeding.controller';
import { Organization } from '../modules/organizations/organization.entity';
import { User } from '../modules/users/user.entity';
import { Company } from '../modules/companies/company.entity';
import { Contact } from '../modules/contacts/contact.entity';
import { Deal } from '../modules/deals/deal.entity';
import { Activity } from '../modules/activities/activity.entity';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Organization,
      User,
      Company,
      Contact,
      Deal,
      Activity,
    ]),
  ],
  controllers: [SeedingController],
  providers: [
    TenantContextService,
    TenantInterceptor,
    TenantGuard,
    PermissionsGuard,
    CustomAuthGuard,
    RbacService,
    SeedingService,
  ],
  exports: [
    TenantContextService,
    TenantInterceptor,
    TenantGuard,
    PermissionsGuard,
    CustomAuthGuard,
    RbacService,
    SeedingService,
  ],
})
export class CommonModule {}
