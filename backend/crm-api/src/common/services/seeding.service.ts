import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Organization } from '../../modules/organizations/organization.entity';
import { User } from '../../modules/users/user.entity';
import { Company } from '../../modules/companies/company.entity';
import { Contact } from '../../modules/contacts/contact.entity';
import { Deal } from '../../modules/deals/deal.entity';
import { Activity } from '../../modules/activities/activity.entity';

@Injectable()
export class SeedingService {
  private readonly logger = new Logger(SeedingService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(Contact)
    private contactRepository: Repository<Contact>,
    @InjectRepository(Deal)
    private dealRepository: Repository<Deal>,
    @InjectRepository(Activity)
    private activityRepository: Repository<Activity>,
  ) {}

  async seedDatabase(): Promise<void> {
    this.logger.log('Starting database seeding...');

    try {
      // Clear existing data in reverse order of dependencies
      await this.clearExistingData();

      // Seed data in order of dependencies
      const organizations = await this.seedOrganizations();
      const users = await this.seedUsers(organizations);
      const companies = await this.seedCompanies(organizations, users);
      const contacts = await this.seedContacts(organizations, companies, users);
      const deals = await this.seedDeals(organizations, contacts, companies, users);
      await this.seedActivities(organizations, users, contacts, deals);

      this.logger.log('Database seeding completed successfully!');
    } catch (error) {
      this.logger.error('Database seeding failed:', error);
      throw error;
    }
  }

  private async clearExistingData(): Promise<void> {
    this.logger.log('Clearing existing data...');

    // Use raw SQL to disable foreign key checks temporarily and clear all data
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();

      // Disable foreign key checks
      await queryRunner.query('SET session_replication_role = replica;');

      // Clear all tables
      await queryRunner.query('TRUNCATE TABLE activities RESTART IDENTITY CASCADE;');
      await queryRunner.query('TRUNCATE TABLE deals RESTART IDENTITY CASCADE;');
      await queryRunner.query('TRUNCATE TABLE contacts RESTART IDENTITY CASCADE;');
      await queryRunner.query('TRUNCATE TABLE companies RESTART IDENTITY CASCADE;');
      await queryRunner.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE;');
      await queryRunner.query('TRUNCATE TABLE organizations RESTART IDENTITY CASCADE;');

      // Re-enable foreign key checks
      await queryRunner.query('SET session_replication_role = DEFAULT;');

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }

    this.logger.log('Existing data cleared');
  }

  private async seedOrganizations(): Promise<Organization[]> {
    this.logger.log('Seeding organizations...');

    const organizationsData = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'TechCorp Solutions',
        slug: 'techcorp',
        plan: 'pro',
        settings: { theme: 'blue', timezone: 'UTC', currency: 'USD' },
        maxUsers: 50,
        maxStorageGb: 10,
        features: { advanced_reporting: true, api_access: true },
        status: 'active',
        suspended: false,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'StartupInc',
        slug: 'startupinc',
        plan: 'free',
        settings: { theme: 'green', timezone: 'PST', currency: 'USD' },
        maxUsers: 5,
        maxStorageGb: 1,
        features: { basic_reporting: true },
        status: 'active',
        suspended: false,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Enterprise Corp',
        slug: 'enterprise',
        plan: 'enterprise',
        settings: { theme: 'dark', timezone: 'EST', currency: 'USD' },
        maxUsers: 200,
        maxStorageGb: 100,
        features: { advanced_reporting: true, api_access: true, custom_fields: true },
        status: 'active',
        suspended: false,
      },
    ];

    const organizations = await this.organizationRepository.save(organizationsData);
    this.logger.log(`Seeded ${organizations.length} organizations`);
    return organizations;
  }

  private async seedUsers(organizations: Organization[]): Promise<User[]> {
    this.logger.log('Seeding users...');

    const usersData = [
      // TechCorp Users
      {
        id: '550e8400-e29b-41d4-a716-446655440101',
        keycloakSub: 'kc-admin-techcorp',
        orgId: organizations[0].id,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Admin',
        role: 'admin',
        isActive: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440102',
        keycloakSub: 'kc-sales-manager',
        orgId: organizations[0].id,
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Johnson',
        role: 'user',
        isActive: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440103',
        keycloakSub: 'kc-sales-rep1',
        orgId: organizations[0].id,
        email: '<EMAIL>',
        firstName: 'Mike',
        lastName: 'Wilson',
        role: 'user',
        isActive: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440104',
        keycloakSub: 'kc-sales-rep2',
        orgId: organizations[0].id,
        email: '<EMAIL>',
        firstName: 'Emily',
        lastName: 'Davis',
        role: 'user',
        isActive: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440105',
        keycloakSub: 'kc-marketing-mgr',
        orgId: organizations[0].id,
        email: '<EMAIL>',
        firstName: 'David',
        lastName: 'Brown',
        role: 'user',
        isActive: true,
      },
      // StartupInc Users
      {
        id: '550e8400-e29b-41d4-a716-446655440201',
        keycloakSub: 'kc-admin-startup',
        orgId: organizations[1].id,
        email: '<EMAIL>',
        firstName: 'Lisa',
        lastName: 'Founder',
        role: 'admin',
        isActive: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440202',
        keycloakSub: 'kc-sales-startup',
        orgId: organizations[1].id,
        email: '<EMAIL>',
        firstName: 'Tom',
        lastName: 'Seller',
        role: 'user',
        isActive: true,
      },
      // Enterprise Corp Users
      {
        id: '550e8400-e29b-41d4-a716-446655440301',
        keycloakSub: 'kc-admin-enterprise',
        orgId: organizations[2].id,
        email: '<EMAIL>',
        firstName: 'Robert',
        lastName: 'Executive',
        role: 'admin',
        isActive: true,
      },
    ];

    const users = await this.userRepository.save(usersData);
    this.logger.log(`Seeded ${users.length} users`);
    return users;
  }

  private async seedCompanies(organizations: Organization[], users: User[]): Promise<Company[]> {
    this.logger.log('Seeding companies...');

    const companiesData = [
      // TechCorp Companies
      {
        id: '550e8400-e29b-41d4-a716-446655440401',
        orgId: organizations[0].id,
        name: 'Acme Corporation',
        domain: 'acme.com',
        industry: 'Technology',
        size: 'Large',
        description: 'Leading technology solutions provider',
        website: 'https://acme.com',
        phone: '******-0101',
        address: {
          street: '123 Tech Street',
          city: 'San Francisco',
          state: 'CA',
          zip: '94105',
          country: 'USA',
        },
        tags: ['enterprise', 'technology', 'saas'],
        customFields: { industry_vertical: 'B2B SaaS', lead_source: 'website' },
        annualRevenue: 5000000.00,
        employeeCount: 250,
        createdById: users[1].id, // Sarah Johnson
        updatedById: users[1].id,
        assignedToId: users[2].id, // Mike Wilson
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440402',
        orgId: organizations[0].id,
        name: 'Global Dynamics',
        domain: 'globaldynamics.com',
        industry: 'Manufacturing',
        size: 'Enterprise',
        description: 'International manufacturing company',
        website: 'https://globaldynamics.com',
        phone: '******-0102',
        address: {
          street: '456 Industrial Ave',
          city: 'Detroit',
          state: 'MI',
          zip: '48201',
          country: 'USA',
        },
        tags: ['manufacturing', 'global', 'b2b'],
        customFields: { industry_vertical: 'Manufacturing', lead_source: 'referral' },
        annualRevenue: 15000000.00,
        employeeCount: 1200,
        createdById: users[1].id,
        updatedById: users[1].id,
        assignedToId: users[3].id, // Emily Davis
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440403',
        orgId: organizations[0].id,
        name: 'FinTech Innovations',
        domain: 'fintech-innovations.com',
        industry: 'Financial Services',
        size: 'Medium',
        description: 'Innovative financial technology solutions',
        website: 'https://fintech-innovations.com',
        phone: '******-0103',
        address: {
          street: '789 Finance Blvd',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA',
        },
        tags: ['fintech', 'innovation', 'startup'],
        customFields: { industry_vertical: 'FinTech', lead_source: 'cold_outreach' },
        annualRevenue: 2500000.00,
        employeeCount: 85,
        createdById: users[1].id,
        updatedById: users[1].id,
        assignedToId: users[2].id,
      },
      // StartupInc Companies
      {
        id: '550e8400-e29b-41d4-a716-446655440501',
        orgId: organizations[1].id,
        name: 'Local Retail Chain',
        domain: 'localretail.com',
        industry: 'Retail',
        size: 'Small',
        description: 'Regional retail chain',
        website: 'https://localretail.com',
        phone: '******-0201',
        address: {
          street: '321 Main Street',
          city: 'Austin',
          state: 'TX',
          zip: '73301',
          country: 'USA',
        },
        tags: ['retail', 'local', 'b2c'],
        customFields: { industry_vertical: 'Retail', lead_source: 'networking' },
        annualRevenue: 800000.00,
        employeeCount: 45,
        createdById: users[5].id, // Lisa Founder
        updatedById: users[5].id,
        assignedToId: users[6].id, // Tom Seller
      },
    ];

    const companies = await this.companyRepository.save(companiesData);
    this.logger.log(`Seeded ${companies.length} companies`);
    return companies;
  }

  private async seedContacts(organizations: Organization[], companies: Company[], users: User[]): Promise<Contact[]> {
    this.logger.log('Seeding contacts...');

    const contactsData = [
      // TechCorp Contacts
      {
        id: '550e8400-e29b-41d4-a716-446655440601',
        orgId: organizations[0].id,
        companyId: companies[0].id, // Acme Corporation
        firstName: 'James',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '******-1001',
        title: 'CTO',
        leadStatus: 'qualified',
        leadSource: 'website',
        customFields: {
          linkedin: 'https://linkedin.com/in/jamessmith',
          notes: 'Very interested in our enterprise solution',
        },
        assignedToId: users[2].id, // Mike Wilson
        createdById: users[1].id, // Sarah Johnson
        updatedById: users[1].id,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440602',
        orgId: organizations[0].id,
        companyId: companies[0].id, // Acme Corporation
        firstName: 'Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        phone: '******-1002',
        title: 'VP Engineering',
        leadStatus: 'lead',
        leadSource: 'referral',
        customFields: {
          linkedin: 'https://linkedin.com/in/mariagarcia',
          notes: 'Referred by James Smith',
        },
        assignedToId: users[2].id,
        createdById: users[1].id,
        updatedById: users[1].id,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440603',
        orgId: organizations[0].id,
        companyId: companies[1].id, // Global Dynamics
        firstName: 'Robert',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '******-1003',
        title: 'CEO',
        leadStatus: 'prospect',
        leadSource: 'cold_outreach',
        customFields: {
          linkedin: 'https://linkedin.com/in/robertjohnson',
          notes: 'Initial contact made, needs follow-up',
        },
        assignedToId: users[3].id, // Emily Davis
        createdById: users[1].id,
        updatedById: users[1].id,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440604',
        orgId: organizations[0].id,
        companyId: companies[2].id, // FinTech Innovations
        firstName: 'Jennifer',
        lastName: 'Lee',
        email: '<EMAIL>',
        phone: '******-1004',
        title: 'Head of Technology',
        leadStatus: 'customer',
        leadSource: 'networking',
        customFields: {
          linkedin: 'https://linkedin.com/in/jenniferlee',
          notes: 'Existing customer, looking to expand',
        },
        assignedToId: users[2].id,
        createdById: users[1].id,
        updatedById: users[1].id,
      },
      // StartupInc Contacts
      {
        id: '550e8400-e29b-41d4-a716-446655440701',
        orgId: organizations[1].id,
        companyId: companies[3].id, // Local Retail Chain
        firstName: 'Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        phone: '******-2001',
        title: 'Store Manager',
        leadStatus: 'lead',
        leadSource: 'networking',
        customFields: {
          notes: 'Met at local business meetup',
        },
        assignedToId: users[6].id, // Tom Seller
        createdById: users[5].id, // Lisa Founder
        updatedById: users[5].id,
      },
    ];

    const contacts = await this.contactRepository.save(contactsData);
    this.logger.log(`Seeded ${contacts.length} contacts`);
    return contacts;
  }

  private async seedDeals(organizations: Organization[], contacts: Contact[], companies: Company[], users: User[]): Promise<Deal[]> {
    this.logger.log('Seeding deals...');

    const dealsData = [
      // TechCorp Deals
      {
        id: '550e8400-e29b-41d4-a716-446655440801',
        orgId: organizations[0].id,
        contactId: contacts[0].id, // James Smith
        companyId: companies[0].id, // Acme Corporation
        title: 'Acme Enterprise License',
        amount: 150000.00,
        currency: 'USD',
        stage: 'proposal',
        probability: 75,
        expectedCloseDate: new Date('2025-08-15'),
        description: 'Enterprise software license for Acme Corporation including implementation and training',
        ownerId: users[2].id, // Mike Wilson
        createdById: users[1].id, // Sarah Johnson
        updatedById: users[1].id,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440802',
        orgId: organizations[0].id,
        contactId: contacts[2].id, // Robert Johnson
        companyId: companies[1].id, // Global Dynamics
        title: 'Global Dynamics Integration',
        amount: 300000.00,
        currency: 'USD',
        stage: 'negotiation',
        probability: 60,
        expectedCloseDate: new Date('2025-09-30'),
        description: 'Custom integration solution for Global Dynamics manufacturing systems',
        ownerId: users[3].id, // Emily Davis
        createdById: users[1].id,
        updatedById: users[1].id,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440803',
        orgId: organizations[0].id,
        contactId: contacts[3].id, // Jennifer Lee
        companyId: companies[2].id, // FinTech Innovations
        title: 'FinTech Expansion',
        amount: 75000.00,
        currency: 'USD',
        stage: 'closed_won',
        probability: 100,
        expectedCloseDate: new Date('2025-07-01'),
        description: 'Additional modules for existing FinTech customer',
        ownerId: users[2].id,
        createdById: users[1].id,
        updatedById: users[1].id,
      },
      // StartupInc Deals
      {
        id: '550e8400-e29b-41d4-a716-446655440901',
        orgId: organizations[1].id,
        contactId: contacts[4].id, // Michael Brown
        companyId: companies[3].id, // Local Retail Chain
        title: 'Local Retail POS System',
        amount: 25000.00,
        currency: 'USD',
        stage: 'discovery',
        probability: 25,
        expectedCloseDate: new Date('2025-10-15'),
        description: 'Point of sale system for local retail chain',
        ownerId: users[6].id, // Tom Seller
        createdById: users[5].id, // Lisa Founder
        updatedById: users[5].id,
      },
    ];

    const deals = await this.dealRepository.save(dealsData);
    this.logger.log(`Seeded ${deals.length} deals`);
    return deals;
  }

  private async seedActivities(organizations: Organization[], users: User[], contacts: Contact[], deals: Deal[]): Promise<void> {
    this.logger.log('Seeding activities...');

    const activitiesData = [
      // TechCorp Activities
      {
        id: '550e8400-e29b-41d4-a716-446655441001',
        orgId: organizations[0].id,
        userId: users[2].id, // Mike Wilson
        contactId: contacts[0].id, // James Smith
        dealId: deals[0].id, // Acme Enterprise License
        type: 'call',
        subject: 'Discovery Call with James Smith',
        description: 'Initial discovery call to understand requirements for enterprise license',
        scheduledAt: new Date('2025-07-20T14:00:00Z'),
        status: 'completed',
        priority: 'high',
        isCompleted: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655441002',
        orgId: organizations[0].id,
        userId: users[2].id,
        contactId: contacts[0].id,
        dealId: deals[0].id,
        type: 'meeting',
        subject: 'Technical Demo',
        description: 'Product demonstration for Acme technical team',
        scheduledAt: new Date('2025-07-25T10:00:00Z'),
        status: 'completed',
        priority: 'high',
        isCompleted: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655441003',
        orgId: organizations[0].id,
        userId: users[3].id, // Emily Davis
        contactId: contacts[2].id, // Robert Johnson
        dealId: deals[1].id, // Global Dynamics Integration
        type: 'email',
        subject: 'Follow-up Email',
        description: 'Sent follow-up email with proposal details',
        scheduledAt: new Date('2025-07-28T09:30:00Z'),
        status: 'completed',
        priority: 'medium',
        isCompleted: true,
      },
      {
        id: '550e8400-e29b-41d4-a716-446655441004',
        orgId: organizations[0].id,
        userId: users[2].id,
        contactId: contacts[3].id, // Jennifer Lee
        dealId: deals[2].id, // FinTech Expansion
        type: 'meeting',
        subject: 'Contract Signing',
        description: 'Final contract signing meeting',
        scheduledAt: new Date('2025-07-01T15:00:00Z'),
        status: 'completed',
        priority: 'high',
        isCompleted: true,
      },
      // StartupInc Activities
      {
        id: '550e8400-e29b-41d4-a716-446655441101',
        orgId: organizations[1].id,
        userId: users[6].id, // Tom Seller
        contactId: contacts[4].id, // Michael Brown
        dealId: deals[3].id, // Local Retail POS System
        type: 'call',
        subject: 'Initial Contact Call',
        description: 'First contact call with Michael Brown',
        scheduledAt: new Date('2025-07-15T11:00:00Z'),
        status: 'pending',
        priority: 'medium',
        isCompleted: false,
      },
    ];

    await this.activityRepository.save(activitiesData);
    this.logger.log(`Seeded ${activitiesData.length} activities`);
  }
}
