import { Injectable, Logger } from '@nestjs/common';
import { UserRole, Permission, ROLE_HIERARCHY } from '../enums/roles.enum';
import { ROLE_PERMISSIONS } from '../config/role-permissions.config';

export interface UserContext {
  id: string;
  email: string;
  role: UserRole;
  orgId: string;
  permissions?: Permission[];
  isActive: boolean;
}

export interface RoleAssignment {
  userId: string;
  role: UserRole;
  orgId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
}

@Injectable()
export class RbacService {
  private readonly logger = new Logger(RbacService.name);

  /**
   * Check if a user has a specific permission
   */
  hasPermission(user: UserContext, permission: Permission): boolean {
    if (!user.isActive) {
      this.logger.warn(`Inactive user ${user.id} attempted to access ${permission}`);
      return false;
    }

    const userPermissions = this.getUserPermissions(user.role);
    const hasPermission = userPermissions.includes(permission);

    this.logger.debug(`Permission check: ${user.email} (${user.role}) -> ${permission}: ${hasPermission}`);
    return hasPermission;
  }

  /**
   * Check if a user has any of the specified permissions
   */
  hasAnyPermission(user: UserContext, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if a user has all of the specified permissions
   */
  hasAllPermissions(user: UserContext, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if a user has a specific role or higher
   */
  hasRole(user: UserContext, role: UserRole): boolean {
    if (user.role === role) {
      return true;
    }

    // Check if user's role is higher in hierarchy
    const userRoleHierarchy = ROLE_HIERARCHY[user.role] || [];
    return userRoleHierarchy.includes(role);
  }

  /**
   * Get all permissions for a role
   */
  getUserPermissions(role: UserRole): Permission[] {
    const directPermissions = ROLE_PERMISSIONS[role] || [];
    const inheritedPermissions = this.getInheritedPermissions(role);
    
    // Combine and deduplicate permissions
    const allPermissions = [...new Set([...directPermissions, ...inheritedPermissions])];
    
    this.logger.debug(`Role ${role} has ${allPermissions.length} permissions`);
    return allPermissions;
  }

  /**
   * Get inherited permissions from role hierarchy
   */
  private getInheritedPermissions(role: UserRole): Permission[] {
    const inheritedRoles = ROLE_HIERARCHY[role] || [];
    const inheritedPermissions: Permission[] = [];

    for (const inheritedRole of inheritedRoles) {
      const rolePermissions = ROLE_PERMISSIONS[inheritedRole] || [];
      inheritedPermissions.push(...rolePermissions);
    }

    return inheritedPermissions;
  }

  /**
   * Check if a role can be assigned by another role
   */
  canAssignRole(assignerRole: UserRole, targetRole: UserRole): boolean {
    // Super admin can assign any role
    if (assignerRole === UserRole.SUPER_ADMIN) {
      return true;
    }

    // System admin can assign org-level roles
    if (assignerRole === UserRole.SYSTEM_ADMIN) {
      return [
        UserRole.ORG_ADMIN,
        UserRole.ORG_MANAGER,
        UserRole.ORG_USER,
        UserRole.ORG_VIEWER,
        UserRole.SALES_MANAGER,
        UserRole.SALES_REP,
        UserRole.MARKETING_MANAGER,
        UserRole.MARKETING_USER,
        UserRole.SUPPORT_MANAGER,
        UserRole.SUPPORT_AGENT,
        UserRole.API_USER,
        UserRole.GUEST
      ].includes(targetRole);
    }

    // Org admin can assign roles within their organization
    if (assignerRole === UserRole.ORG_ADMIN) {
      return [
        UserRole.ORG_MANAGER,
        UserRole.ORG_USER,
        UserRole.ORG_VIEWER,
        UserRole.SALES_MANAGER,
        UserRole.SALES_REP,
        UserRole.MARKETING_MANAGER,
        UserRole.MARKETING_USER,
        UserRole.SUPPORT_MANAGER,
        UserRole.SUPPORT_AGENT,
        UserRole.API_USER,
        UserRole.GUEST
      ].includes(targetRole);
    }

    // Managers can assign lower roles
    if ([UserRole.ORG_MANAGER, UserRole.SALES_MANAGER, UserRole.MARKETING_MANAGER, UserRole.SUPPORT_MANAGER].includes(assignerRole)) {
      return [
        UserRole.ORG_USER,
        UserRole.ORG_VIEWER,
        UserRole.SALES_REP,
        UserRole.MARKETING_USER,
        UserRole.SUPPORT_AGENT,
        UserRole.GUEST
      ].includes(targetRole);
    }

    return false;
  }

  /**
   * Get available roles that a user can assign
   */
  getAssignableRoles(assignerRole: UserRole): UserRole[] {
    const allRoles = Object.values(UserRole);
    return allRoles.filter(role => this.canAssignRole(assignerRole, role));
  }

  /**
   * Validate role assignment
   */
  validateRoleAssignment(assignment: Partial<RoleAssignment>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!assignment.userId) {
      errors.push('User ID is required');
    }

    if (!assignment.role) {
      errors.push('Role is required');
    }

    if (!assignment.orgId) {
      errors.push('Organization ID is required');
    }

    if (!assignment.assignedBy) {
      errors.push('Assigner ID is required');
    }

    if (assignment.expiresAt && assignment.expiresAt <= new Date()) {
      errors.push('Expiration date must be in the future');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if user can access resource within organization context
   */
  canAccessResource(user: UserContext, resourceOrgId: string, permission: Permission): boolean {
    // Super admin and system admin can access any resource
    if ([UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN].includes(user.role)) {
      return this.hasPermission(user, permission);
    }

    // Check if user belongs to the same organization
    if (user.orgId !== resourceOrgId) {
      this.logger.warn(`User ${user.id} attempted to access resource from different org: ${resourceOrgId}`);
      return false;
    }

    return this.hasPermission(user, permission);
  }

  /**
   * Get role display information
   */
  getRoleInfo(role: UserRole): { name: string; description: string; level: number } {
    const roleInfo = {
      [UserRole.SUPER_ADMIN]: { name: 'Super Administrator', description: 'Full system access', level: 10 },
      [UserRole.SYSTEM_ADMIN]: { name: 'System Administrator', description: 'System-wide administration', level: 9 },
      [UserRole.ORG_ADMIN]: { name: 'Organization Administrator', description: 'Full organization access', level: 8 },
      [UserRole.ORG_MANAGER]: { name: 'Organization Manager', description: 'Organization management', level: 7 },
      [UserRole.ORG_USER]: { name: 'Organization User', description: 'Standard user access', level: 6 },
      [UserRole.ORG_VIEWER]: { name: 'Organization Viewer', description: 'Read-only access', level: 5 },
      [UserRole.SALES_MANAGER]: { name: 'Sales Manager', description: 'Sales team management', level: 7 },
      [UserRole.SALES_REP]: { name: 'Sales Representative', description: 'Sales activities', level: 6 },
      [UserRole.MARKETING_MANAGER]: { name: 'Marketing Manager', description: 'Marketing team management', level: 7 },
      [UserRole.MARKETING_USER]: { name: 'Marketing User', description: 'Marketing activities', level: 6 },
      [UserRole.SUPPORT_MANAGER]: { name: 'Support Manager', description: 'Support team management', level: 7 },
      [UserRole.SUPPORT_AGENT]: { name: 'Support Agent', description: 'Customer support', level: 6 },
      [UserRole.API_USER]: { name: 'API User', description: 'API access only', level: 4 },
      [UserRole.GUEST]: { name: 'Guest', description: 'Limited read access', level: 1 }
    };

    return roleInfo[role] || { name: 'Unknown', description: 'Unknown role', level: 0 };
  }
}
