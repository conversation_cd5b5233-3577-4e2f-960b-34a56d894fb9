import { registerAs } from '@nestjs/config';

export interface AppConfig {
  port: number;
  environment: string;
  apiPrefix: string;
  apiVersion: string;
  baseUrl: string;
  frontendUrl: string;
  allowedOrigins: string[];
  globalPrefix: string;
  enableSwagger: boolean;
  enableCors: boolean;
  enableHelmet: boolean;
  enableCompression: boolean;
  enableRateLimit: boolean;
  rateLimitTtl: number;
  rateLimitLimit: number;
  maxFileSize: number;
  uploadPath: string;
  logLevel: string;
  logFilePath: string;
  enableFileLogging: boolean;
  enableConsoleLogging: boolean;
  enableDetailedLogging: boolean;
  enableQueryLogging: boolean;
  enableErrorTracking: boolean;
  enableMetrics: boolean;
  enableHealthChecks: boolean;
  enableGracefulShutdown: boolean;
  shutdownTimeout: number;
}

export const appConfig = registerAs('app', (): AppConfig => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';

  // Validate required environment variables for production
  if (isProduction) {
    const requiredVars = ['JWT_SECRET', 'DB_PASSWORD'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables for production: ${missingVars.join(', ')}`);
    }
  }

  return {
    // Server Configuration
    port: parseInt(process.env.PORT || '3002', 10),
    environment: process.env.NODE_ENV || 'development',
    apiPrefix: process.env.API_PREFIX || 'api',
    apiVersion: process.env.API_VERSION || 'v1',
    baseUrl: process.env.API_BASE_URL || `http://localhost:${process.env.PORT || 3002}`,
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://stgsso.cubeone.in',
    ],
    globalPrefix: process.env.GLOBAL_PREFIX || 'api',

    // Feature Flags
    enableSwagger: isDevelopment || process.env.ENABLE_SWAGGER === 'true',
    enableCors: process.env.ENABLE_CORS !== 'false',
    enableHelmet: process.env.ENABLE_HELMET !== 'false',
    enableCompression: process.env.ENABLE_COMPRESSION !== 'false',
    enableRateLimit: isProduction || process.env.ENABLE_RATE_LIMIT === 'true',
    enableMetrics: isProduction || process.env.ENABLE_METRICS === 'true',
    enableHealthChecks: process.env.ENABLE_HEALTH_CHECKS !== 'false',
    enableGracefulShutdown: isProduction || process.env.ENABLE_GRACEFUL_SHUTDOWN === 'true',

    // Rate Limiting
    rateLimitTtl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10), // seconds
    rateLimitLimit: parseInt(process.env.RATE_LIMIT_LIMIT || (isProduction ? '100' : '1000'), 10),

    // File Upload
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
    uploadPath: process.env.UPLOAD_PATH || './uploads',

    // Logging Configuration
    logLevel: process.env.LOG_LEVEL || (isProduction ? 'info' : 'debug'),
    logFilePath: process.env.LOG_FILE_PATH || './logs',
    enableFileLogging: isProduction || process.env.ENABLE_FILE_LOGGING === 'true',
    enableConsoleLogging: process.env.ENABLE_CONSOLE_LOGGING !== 'false',
    enableDetailedLogging: isDevelopment || process.env.ENABLE_DETAILED_LOGGING === 'true',
    enableQueryLogging: isDevelopment && process.env.ENABLE_QUERY_LOGGING !== 'false',
    enableErrorTracking: isProduction || process.env.ENABLE_ERROR_TRACKING === 'true',

    // Shutdown Configuration
    shutdownTimeout: parseInt(process.env.SHUTDOWN_TIMEOUT || '10000', 10), // 10 seconds
  };
});

export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  bcryptRounds: number;
  sessionSecret: string;
  cookieSecret: string;
  enableCsrf: boolean;
  csrfSecret: string;
  enableTrustedProxies: boolean;
  trustedProxies: string[];
  maxRequestSize: string;
  enableRequestValidation: boolean;
  enableResponseValidation: boolean;
  enableInputSanitization: boolean;
  enableXssProtection: boolean;
  enableContentTypeValidation: boolean;
  enableStrictTransportSecurity: boolean;
  enableContentSecurityPolicy: boolean;
  enableReferrerPolicy: boolean;
  enablePermissionsPolicy: boolean;
}

export const securityConfig = registerAs('security', (): SecurityConfig => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  return {
    // JWT Configuration
    jwtSecret: process.env.JWT_SECRET || (isDevelopment ? 'dev-jwt-secret-key' : ''),
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '15m',
    jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',

    // Password Hashing
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || (isProduction ? '12' : '10'), 10),

    // Session & Cookie Security
    sessionSecret: process.env.SESSION_SECRET || (isDevelopment ? 'dev-session-secret' : ''),
    cookieSecret: process.env.COOKIE_SECRET || (isDevelopment ? 'dev-cookie-secret' : ''),

    // CSRF Protection
    enableCsrf: isProduction || process.env.ENABLE_CSRF === 'true',
    csrfSecret: process.env.CSRF_SECRET || (isDevelopment ? 'dev-csrf-secret' : ''),

    // Proxy Configuration
    enableTrustedProxies: isProduction || process.env.ENABLE_TRUSTED_PROXIES === 'true',
    trustedProxies: process.env.TRUSTED_PROXIES?.split(',') || ['127.0.0.1', '::1'],

    // Request/Response Security
    maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
    enableRequestValidation: process.env.ENABLE_REQUEST_VALIDATION !== 'false',
    enableResponseValidation: isDevelopment || process.env.ENABLE_RESPONSE_VALIDATION === 'true',
    enableInputSanitization: process.env.ENABLE_INPUT_SANITIZATION !== 'false',

    // Security Headers
    enableXssProtection: process.env.ENABLE_XSS_PROTECTION !== 'false',
    enableContentTypeValidation: process.env.ENABLE_CONTENT_TYPE_VALIDATION !== 'false',
    enableStrictTransportSecurity: isProduction || process.env.ENABLE_HSTS === 'true',
    enableContentSecurityPolicy: isProduction || process.env.ENABLE_CSP === 'true',
    enableReferrerPolicy: process.env.ENABLE_REFERRER_POLICY !== 'false',
    enablePermissionsPolicy: isProduction || process.env.ENABLE_PERMISSIONS_POLICY === 'true',
  };
});

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  ttl: number;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  keyPrefix: string;
}

export const redisConfig = registerAs('redis', (): RedisConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || (isDevelopment ? '6380' : '6379'), 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    ttl: parseInt(process.env.REDIS_TTL || '3600', 10), // 1 hour
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100', 10),
    enableReadyCheck: process.env.REDIS_ENABLE_READY_CHECK !== 'false',
    lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
    keepAlive: parseInt(process.env.REDIS_KEEP_ALIVE || '30000', 10),
    family: parseInt(process.env.REDIS_FAMILY || '4', 10),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'onecrm:',
  };
});
