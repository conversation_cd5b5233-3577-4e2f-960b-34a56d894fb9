import { NestFactory } from '@nestjs/core';
import { Val<PERSON>tionPipe, Logger, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import helmet from 'helmet';
import compression from 'compression';
import { AppModule } from './app.module';
import { CustomLoggerService } from './common/logger/logger.service';
import { AppConfig, SecurityConfig } from './config/app.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    cors: false, // We'll configure CORS manually for better control
  });

  // Get configuration services
  const configService = app.get(ConfigService);
  const customLogger = app.get(CustomLoggerService);
  const appConfig = configService.get<AppConfig>('app');
  const securityConfig = configService.get<SecurityConfig>('security');

  // Use custom logger
  app.useLogger(customLogger);

  // Security middleware - Helmet for security headers
  if (appConfig.enableHelmet) {
    app.use(helmet({
      contentSecurityPolicy: securityConfig.enableContentSecurityPolicy ? {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      } : false,
      hsts: securityConfig.enableStrictTransportSecurity ? {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      } : false,
      referrerPolicy: securityConfig.enableReferrerPolicy ? {
        policy: "strict-origin-when-cross-origin"
      } : false,
    }));
  }

  // Compression middleware
  if (appConfig.enableCompression) {
    app.use(compression());
  }

  // Trust proxy settings for production
  if (securityConfig.enableTrustedProxies) {
    const expressApp = app.getHttpAdapter().getInstance();
    expressApp.set('trust proxy', securityConfig.trustedProxies);
  }

  // Enable CORS with production-ready configuration
  if (appConfig.enableCors) {
    app.enableCors({
      origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);

        if (appConfig.allowedOrigins.includes(origin)) {
          return callback(null, true);
        }

        // In development, allow localhost with any port
        if (appConfig.environment === 'development' && origin.includes('localhost')) {
          return callback(null, true);
        }

        return callback(new Error('Not allowed by CORS'), false);
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Org-Id',
        'X-Request-Id',
        'X-Tenant-Id',
        'Accept',
        'Origin',
        'User-Agent',
        'Cache-Control',
      ],
      exposedHeaders: ['X-Total-Count', 'X-Request-Id'],
      maxAge: 86400, // 24 hours
    });
  }

  // Global validation pipe with enhanced security
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      disableErrorMessages: appConfig.environment === 'production',
      validateCustomDecorators: true,
      forbidUnknownValues: true,
      stopAtFirstError: false,
    }),
  );

  // API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: appConfig.apiVersion,
    prefix: 'v',
  });

  // Global prefix
  app.setGlobalPrefix(appConfig.globalPrefix);

  // Swagger documentation - only in development or when explicitly enabled
  if (appConfig.enableSwagger) {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('OneCRM API')
      .setDescription('Enterprise-grade Multi-tenant CRM API with comprehensive business logic')
      .setVersion('1.0.0')
      .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      })
      .addServer(appConfig.baseUrl, 'Development server')
      .addTag('health', 'Health check and system status endpoints')
      .addTag('seed', 'Database seeding endpoints')
      .addTag('organizations', 'Organization management and multi-tenancy')
      .addTag('users', 'User management and authentication')
      .addTag('contacts', 'Contact and lead management')
      .addTag('companies', 'Company and account management')
      .addTag('deals', 'Deal and opportunity management')
      .addTag('activities', 'Activity and task management')
      .addTag('rbac', 'Role-based access control')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig, {
      operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    });

    SwaggerModule.setup(`${appConfig.globalPrefix}/docs`, app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'none',
        filter: true,
        showRequestHeaders: true,
        tryItOutEnabled: true,
      },
      customSiteTitle: 'OneCRM API Documentation',
      customfavIcon: '/favicon.ico',
      customCss: '.swagger-ui .topbar { display: none }',
    });

    customLogger.log(`📚 API Documentation: ${appConfig.baseUrl}/${appConfig.globalPrefix}/docs`, 'Bootstrap');
  }

  // Graceful shutdown handling
  if (appConfig.enableGracefulShutdown) {
    const gracefulShutdown = (signal: string) => {
      customLogger.log(`Received ${signal}, starting graceful shutdown...`, 'Bootstrap');

      setTimeout(() => {
        customLogger.error('Forcefully shutting down after timeout', 'Bootstrap');
        process.exit(1);
      }, appConfig.shutdownTimeout);

      app.close().then(() => {
        customLogger.log('Application closed successfully', 'Bootstrap');
        process.exit(0);
      }).catch((error) => {
        customLogger.error('Error during shutdown:', error, 'Bootstrap');
        process.exit(1);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  // Start the server
  await app.listen(appConfig.port, '0.0.0.0');

  // Log startup information
  customLogger.log(`🚀 OneCRM API is running on: ${appConfig.baseUrl}`, 'Bootstrap');
  customLogger.log(`🌍 Environment: ${appConfig.environment}`, 'Bootstrap');
  customLogger.log(`🔒 Security: ${securityConfig.enableCsrf ? 'CSRF Enabled' : 'CSRF Disabled'}`, 'Bootstrap');
  customLogger.log(`🛡️  Helmet: ${appConfig.enableHelmet ? 'Enabled' : 'Disabled'}`, 'Bootstrap');
  customLogger.log(`📦 Compression: ${appConfig.enableCompression ? 'Enabled' : 'Disabled'}`, 'Bootstrap');
  customLogger.log(`⚡ Rate Limiting: ${appConfig.enableRateLimit ? 'Enabled' : 'Disabled'}`, 'Bootstrap');
  customLogger.log(`📊 Metrics: ${appConfig.enableMetrics ? 'Enabled' : 'Disabled'}`, 'Bootstrap');
  customLogger.log(`🔧 Hot Reload: ${appConfig.environment === 'development' ? 'Enabled' : 'Disabled'}`, 'Bootstrap');

  if (appConfig.environment === 'development') {
    customLogger.log(`🐛 Debug Mode: Enabled`, 'Bootstrap');
    customLogger.log(`📊 Detailed Logging: Enabled`, 'Bootstrap');
    customLogger.log(`🌐 CORS Origins: ${appConfig.allowedOrigins.join(', ')}`, 'Bootstrap');
  }

  // Log performance and security settings
  if (appConfig.environment === 'production') {
    customLogger.log(`🔐 Production Security: Enabled`, 'Bootstrap');
    customLogger.log(`📈 Performance Optimizations: Enabled`, 'Bootstrap');
    customLogger.log(`🗄️  Database Connection Pool: ${configService.get('DB_MAX_CONNECTIONS', 50)} max connections`, 'Bootstrap');
  }
}

bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('Failed to start application', error.stack);
  process.exit(1);
});
