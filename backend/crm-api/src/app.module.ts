import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR, APP_FILTER, APP_GUARD } from '@nestjs/core';
import { KeycloakConnectModule, ResourceGuard, RoleGuard, TokenValidation } from 'nest-keycloak-connect';
import { CustomAuthGuard } from './common/guards/custom-auth.guard';

// Configuration
import { databaseConfig } from './config/database.config';
import { keycloakConfig } from './config/keycloak.config';
import { appConfig, securityConfig, redisConfig } from './config/app.config';

// Common modules
import { LoggerModule } from './common/logger/logger.module';
import { CommonModule } from './common/common.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TenantInterceptor } from './common/interceptors/tenant.interceptor';

// Feature modules
import { HealthModule } from './modules/health/health.module';
import { OrganizationsModule } from './modules/organizations/organizations.module';
import { ContactsModule } from './modules/contacts/contacts.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { DealsModule } from './modules/deals/deals.module';
import { UsersModule } from './modules/users/users.module';
import { ActivitiesModule } from './modules/activities/activities.module';
import { RbacModule } from './modules/rbac/rbac.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, keycloakConfig, appConfig, securityConfig, redisConfig],
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
      validationOptions: {
        allowUnknown: false,
        abortEarly: true,
      },
    }),

    // Keycloak Authentication
    KeycloakConnectModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        const keycloakOptions = configService.get('keycloak');
        return {
          authServerUrl: keycloakOptions.authServerUrl,
          realm: keycloakOptions.realm,
          clientId: keycloakOptions.clientId,
          secret: keycloakOptions.secret,
          policyEnforcement: keycloakOptions.policyEnforcement,
          tokenValidation: TokenValidation.ONLINE, // Validate tokens online with Keycloak
          bearerOnly: true, // API only accepts bearer tokens
          logLevels: ['verbose'],
          useNestLogger: true,
        };
      },
      inject: [ConfigService],
    }),

    // Logger
    LoggerModule,

    // Common services and interceptors
    CommonModule,

    // Database
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => configService.get('database'),
    }),

    // Feature modules
    HealthModule,
    OrganizationsModule,
    ContactsModule,
    CompaniesModule,
    DealsModule,
    UsersModule,
    ActivitiesModule,
    RbacModule,
  ],
  providers: [
    // Global guards for Keycloak authentication - temporarily disabled for testing
    // {
    //   provide: APP_GUARD,
    //   useClass: CustomAuthGuard,
    // },
    // Temporarily disable ResourceGuard and RoleGuard for development
    // {
    //   provide: APP_GUARD,
    //   useClass: ResourceGuard,
    // },
    // {
    //   provide: APP_GUARD,
    //   useClass: RoleGuard,
    // },
    // Global interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    // Temporarily disable tenant interceptor for testing
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: TenantInterceptor,
    // },
  ],
})
export class AppModule {}
