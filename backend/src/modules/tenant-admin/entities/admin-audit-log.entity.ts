import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum AdminAction {
  TENANT_CREATE = 'tenant_create',
  TENANT_UPDATE = 'tenant_update',
  TENANT_DELETE = 'tenant_delete',
  TENANT_SUSPEND = 'tenant_suspend',
  TENANT_REACTIVATE = 'tenant_reactivate',
  SUBSCRIPTION_CHANGE = 'subscription_change',
  FEATURE_ENABLE = 'feature_enable',
  FEATURE_DISABLE = 'feature_disable',
  USAGE_LIMIT_UPDATE = 'usage_limit_update',
  SUPPORT_TICKET_CREATE = 'support_ticket_create',
  SUPPORT_TICKET_UPDATE = 'support_ticket_update',
  SYSTEM_ALERT_CREATE = 'system_alert_create',
  SYSTEM_ALERT_RESOLVE = 'system_alert_resolve',
  USER_IMPERSONATE = 'user_impersonate',
  DATA_EXPORT = 'data_export',
  DATA_IMPORT = 'data_import',
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SECURITY_INCIDENT = 'security_incident',
}

export enum AdminActionResult {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial',
  PENDING = 'pending',
}

@Entity('admin_audit_logs')
@Index(['adminUserId', 'timestamp'])
@Index(['targetOrganizationId', 'timestamp'])
@Index(['action', 'timestamp'])
export class AdminAuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'admin_user_id' })
  adminUserId: string;

  @Column({ type: 'varchar', length: 255, name: 'admin_user_email' })
  adminUserEmail: string;

  @Column({
    type: 'enum',
    enum: AdminAction,
  })
  action: AdminAction;

  @Column({
    type: 'enum',
    enum: AdminActionResult,
    default: AdminActionResult.SUCCESS,
  })
  result: AdminActionResult;

  @Column({ name: 'target_organization_id', nullable: true })
  targetOrganizationId: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'target_user_id' })
  targetUserId: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'target_resource_type' })
  targetResourceType: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'target_resource_id' })
  targetResourceId: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json', nullable: true })
  details: {
    previousValues?: Record<string, any>;
    newValues?: Record<string, any>;
    metadata?: Record<string, any>;
    requestData?: Record<string, any>;
    responseData?: Record<string, any>;
  };

  @Column({ type: 'varchar', length: 45, nullable: true, name: 'ip_address' })
  ipAddress: string;

  @Column({ type: 'text', nullable: true, name: 'user_agent' })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'session_id' })
  sessionId: string;

  @Column({ type: 'text', nullable: true, name: 'error_message' })
  errorMessage: string;

  @Column({ type: 'int', nullable: true, name: 'duration_ms' })
  durationMs: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Computed properties
  get isSuccessful(): boolean {
    return this.result === AdminActionResult.SUCCESS;
  }

  get isCriticalAction(): boolean {
    const criticalActions = [
      AdminAction.TENANT_DELETE,
      AdminAction.TENANT_SUSPEND,
      AdminAction.DATA_EXPORT,
      AdminAction.USER_IMPERSONATE,
      AdminAction.SECURITY_INCIDENT,
    ];
    return criticalActions.includes(this.action);
  }
}
