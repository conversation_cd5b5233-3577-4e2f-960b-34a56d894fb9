import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantAdminController } from './tenant-admin.controller';
import { TenantAdminService } from './tenant-admin.service';
import { SystemMetricsService } from './system-metrics.service';
import { TenantSupportService } from './tenant-support.service';
import { AdminAuditLog } from './entities/admin-audit-log.entity';
import { SystemAlert } from './entities/system-alert.entity';
import { TenantHealthCheck } from './entities/tenant-health-check.entity';
import { SupportTicket } from './entities/support-ticket.entity';
import { OrganizationModule } from '../organization/organization.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UsageMonitoringModule } from '../usage-monitoring/usage-monitoring.module';
import { FeatureGatingModule } from '../feature-gating/feature-gating.module';
import { UserModule } from '../user/user.module';
import { EmailModule } from '../email/email.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminAuditLog,
      SystemAlert,
      TenantHealthCheck,
      SupportTicket,
    ]),
    OrganizationModule,
    SubscriptionModule,
    UsageMonitoringModule,
    FeatureGatingModule,
    UserModule,
    EmailModule,
    NotificationModule,
  ],
  controllers: [TenantAdminController],
  providers: [
    TenantAdminService,
    SystemMetricsService,
    TenantSupportService,
  ],
  exports: [
    TenantAdminService,
    SystemMetricsService,
    TenantSupportService,
  ],
})
export class TenantAdminModule {}
