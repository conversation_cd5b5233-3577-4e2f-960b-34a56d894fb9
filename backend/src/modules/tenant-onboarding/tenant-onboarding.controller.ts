import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TenantGuard } from '../tenant/guards/tenant.guard';
import { TenantOnboardingService, OnboardingInitiationDto, OnboardingProgressDto } from './tenant-onboarding.service';
import { OnboardingTemplateType } from './entities/onboarding-template.entity';
import { OnboardingStepStatus } from './entities/onboarding-step.entity';

class InitiateOnboardingDto {
  templateType?: OnboardingTemplateType;
  assignedTo?: string;
  autoStart?: boolean = true;
}

class UpdateProgressDto {
  status: OnboardingStepStatus;
  data?: Record<string, any>;
  notes?: string;
  failureReason?: string;
}

class CompleteStepDto {
  data?: Record<string, any>;
  notes?: string;
}

class SkipStepDto {
  reason?: string;
}

@ApiTags('Tenant Onboarding')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('onboarding')
export class TenantOnboardingController {
  constructor(private readonly onboardingService: TenantOnboardingService) {}

  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Initiate onboarding for current organization' })
  @ApiResponse({ status: 201, description: 'Onboarding initiated successfully' })
  @ApiResponse({ status: 400, description: 'Onboarding already exists or invalid request' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async initiateOnboarding(@Request() req, @Body() dto: InitiateOnboardingDto) {
    const initiationDto: OnboardingInitiationDto = {
      organizationId: req.user.organizationId,
      templateType: dto.templateType,
      assignedTo: dto.assignedTo,
      autoStart: dto.autoStart,
    };

    return this.onboardingService.initiateOnboarding(initiationDto);
  }

  @Get('status')
  @ApiOperation({ summary: 'Get onboarding status for current organization' })
  @ApiResponse({ status: 200, description: 'Onboarding status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'No onboarding found for organization' })
  async getOnboardingStatus(@Request() req) {
    return this.onboardingService.getOnboardingStatus(req.user.organizationId);
  }

  @Put('progress/:progressId')
  @ApiOperation({ summary: 'Update onboarding step progress' })
  @ApiResponse({ status: 200, description: 'Progress updated successfully' })
  @ApiResponse({ status: 404, description: 'Progress record not found' })
  async updateProgress(
    @Param('progressId') progressId: string,
    @Body() dto: UpdateProgressDto,
  ) {
    const progressDto: OnboardingProgressDto = {
      stepId: '', // Will be ignored in service
      status: dto.status,
      data: dto.data,
      notes: dto.notes,
      failureReason: dto.failureReason,
    };

    return this.onboardingService.updateStepProgress(progressId, progressDto);
  }

  @Post('progress/:progressId/start')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Start an onboarding step' })
  @ApiResponse({ status: 200, description: 'Step started successfully' })
  @ApiResponse({ status: 404, description: 'Progress record not found' })
  async startStep(@Param('progressId') progressId: string) {
    return this.onboardingService.startStep(progressId);
  }

  @Post('progress/:progressId/complete')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Complete an onboarding step' })
  @ApiResponse({ status: 200, description: 'Step completed successfully' })
  @ApiResponse({ status: 404, description: 'Progress record not found' })
  async completeStep(
    @Param('progressId') progressId: string,
    @Body() dto: CompleteStepDto,
  ) {
    return this.onboardingService.completeStep(progressId, dto.data, dto.notes);
  }

  @Post('progress/:progressId/skip')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Skip an onboarding step' })
  @ApiResponse({ status: 200, description: 'Step skipped successfully' })
  @ApiResponse({ status: 404, description: 'Progress record not found' })
  async skipStep(
    @Param('progressId') progressId: string,
    @Body() dto: SkipStepDto,
  ) {
    return this.onboardingService.skipStep(progressId, dto.reason);
  }

  @Post('progress/:progressId/retry')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Retry a failed onboarding step' })
  @ApiResponse({ status: 200, description: 'Step retry initiated successfully' })
  @ApiResponse({ status: 400, description: 'Can only retry failed steps' })
  @ApiResponse({ status: 404, description: 'Progress record not found' })
  async retryStep(@Param('progressId') progressId: string) {
    return this.onboardingService.retryStep(progressId);
  }
}
