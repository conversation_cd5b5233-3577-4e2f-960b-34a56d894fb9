import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { OnboardingTemplate } from './onboarding-template.entity';

export enum OnboardingStepType {
  ORGANIZATION_SETUP = 'organization_setup',
  USER_INVITATION = 'user_invitation',
  DATA_IMPORT = 'data_import',
  INTEGRATION_SETUP = 'integration_setup',
  CUSTOMIZATION = 'customization',
  TRAINING = 'training',
  VERIFICATION = 'verification',
}

export enum OnboardingStepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FAILED = 'failed',
}

@Entity('onboarding_steps')
export class OnboardingStep {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OnboardingStepType,
  })
  type: OnboardingStepType;

  @Column({ type: 'int', default: 0 })
  order: number;

  @Column({ type: 'boolean', default: false })
  required: boolean;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ type: 'json', nullable: true })
  configuration: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  validation_rules: Record<string, any>;

  @Column({ type: 'int', nullable: true })
  estimated_duration_minutes: number;

  @Column({ type: 'text', nullable: true })
  instructions: string;

  @Column({ type: 'json', nullable: true })
  resources: {
    documentation_url?: string;
    video_url?: string;
    support_contact?: string;
  };

  @ManyToOne(() => OnboardingTemplate, template => template.steps)
  @JoinColumn({ name: 'template_id' })
  template: OnboardingTemplate;

  @Column({ name: 'template_id' })
  templateId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
