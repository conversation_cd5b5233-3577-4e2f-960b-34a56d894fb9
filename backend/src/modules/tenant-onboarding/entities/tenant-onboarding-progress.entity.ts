import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';
import { OnboardingStep } from './onboarding-step.entity';
import { OnboardingTemplate } from './onboarding-template.entity';
import { OnboardingStepStatus } from './onboarding-step.entity';

@Entity('tenant_onboarding_progress')
@Index(['organizationId', 'stepId'], { unique: true })
export class TenantOnboardingProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @ManyToOne(() => OnboardingTemplate)
  @JoinColumn({ name: 'template_id' })
  template: OnboardingTemplate;

  @Column({ name: 'template_id' })
  templateId: string;

  @ManyToOne(() => OnboardingStep)
  @JoinColumn({ name: 'step_id' })
  step: OnboardingStep;

  @Column({ name: 'step_id' })
  stepId: string;

  @Column({
    type: 'enum',
    enum: OnboardingStepStatus,
    default: OnboardingStepStatus.PENDING,
  })
  status: OnboardingStepStatus;

  @Column({ type: 'timestamp', nullable: true, name: 'started_at' })
  startedAt: Date;

  @Column({ type: 'timestamp', nullable: true, name: 'completed_at' })
  completedAt: Date;

  @Column({ type: 'int', nullable: true, name: 'duration_minutes' })
  durationMinutes: number;

  @Column({ type: 'json', nullable: true })
  data: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true, name: 'failure_reason' })
  failureReason: string;

  @Column({ type: 'int', default: 0, name: 'retry_count' })
  retryCount: number;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'assigned_to' })
  assignedTo: string;

  @Column({ type: 'json', nullable: true })
  validation_results: {
    passed: boolean;
    errors?: string[];
    warnings?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
