import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionPlan, PlanType, BillingInterval } from './entities/subscription-plan.entity';
import { Subscription, SubscriptionStatus } from './entities/subscription.entity';
import { Invoice, InvoiceStatus } from './entities/invoice.entity';
import { StripeService, CreateCustomerDto, CreateSubscriptionDto } from './stripe.service';
import { OrganizationService } from '../organization/organization.service';
import { EmailService } from '../email/email.service';
import { NotificationService } from '../notification/notification.service';

export interface CreateSubscriptionRequest {
  organizationId: string;
  planId: string;
  paymentMethodId?: string;
  billingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

export interface ChangeSubscriptionRequest {
  organizationId: string;
  newPlanId: string;
  prorationBehavior?: 'create_prorations' | 'none' | 'always_invoice';
}

export interface SubscriptionWithUsage {
  subscription: Subscription;
  currentUsage: {
    users: number;
    contacts: number;
    deals: number;
    storageGB: number;
    apiCalls: number;
  };
  limits: {
    maxUsers: number;
    maxContacts: number;
    maxDeals: number;
    maxStorageGB: number;
    apiCallsPerMonth: number;
  };
  usagePercentages: {
    users: number;
    contacts: number;
    deals: number;
    storage: number;
    apiCalls: number;
  };
}

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(SubscriptionPlan)
    private readonly planRepository: Repository<SubscriptionPlan>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Invoice)
    private readonly invoiceRepository: Repository<Invoice>,
    private readonly stripeService: StripeService,
    private readonly organizationService: OrganizationService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
  ) {}

  async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    return this.planRepository.find({
      where: { active: true },
      order: { sortOrder: 'ASC', price: 'ASC' },
    });
  }

  async getPlanById(planId: string): Promise<SubscriptionPlan> {
    const plan = await this.planRepository.findOne({
      where: { id: planId, active: true },
    });

    if (!plan) {
      throw new NotFoundException('Subscription plan not found');
    }

    return plan;
  }

  async createSubscription(request: CreateSubscriptionRequest): Promise<Subscription> {
    const { organizationId, planId, paymentMethodId, billingAddress } = request;

    // Validate organization exists
    const organization = await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if organization already has a subscription
    const existingSubscription = await this.subscriptionRepository.findOne({
      where: { organizationId },
    });

    if (existingSubscription) {
      throw new BadRequestException('Organization already has an active subscription');
    }

    // Get the plan
    const plan = await this.getPlanById(planId);

    // Create Stripe customer if not exists
    let stripeCustomerId = organization.stripeCustomerId;
    if (!stripeCustomerId) {
      const customerDto: CreateCustomerDto = {
        email: organization.primaryContactEmail,
        name: organization.name,
        organizationId,
        address: billingAddress,
        metadata: {
          organizationId,
          planType: plan.type,
        },
      };

      const stripeCustomer = await this.stripeService.createCustomer(customerDto);
      stripeCustomerId = stripeCustomer.id;

      // Update organization with Stripe customer ID
      await this.organizationService.update(organizationId, {
        stripeCustomerId,
      });
    }

    // Attach payment method if provided
    if (paymentMethodId) {
      await this.stripeService.createPaymentMethod(stripeCustomerId, paymentMethodId);
      await this.stripeService.setDefaultPaymentMethod(stripeCustomerId, paymentMethodId);
    }

    // Create Stripe subscription
    const subscriptionDto: CreateSubscriptionDto = {
      customerId: stripeCustomerId,
      priceId: plan.stripePriceId,
      trialPeriodDays: plan.trialDays,
      paymentMethodId,
      metadata: {
        organizationId,
        planId,
        planType: plan.type,
      },
    };

    const stripeSubscription = await this.stripeService.createSubscription(subscriptionDto);

    // Create local subscription record
    const subscription = this.subscriptionRepository.create({
      organizationId,
      planId,
      status: this.mapStripeStatus(stripeSubscription.status),
      stripeSubscriptionId: stripeSubscription.id,
      stripeCustomerId,
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
      quantity: stripeSubscription.items.data[0]?.quantity || 1,
      metadata: {
        stripeSubscriptionId: stripeSubscription.id,
        planName: plan.name,
      },
    });

    const savedSubscription = await this.subscriptionRepository.save(subscription);

    // Send welcome email
    await this.sendSubscriptionWelcomeEmail(organization, plan, savedSubscription);

    // Create notification
    await this.notificationService.create({
      organizationId,
      type: 'subscription_created',
      title: 'Subscription Activated',
      message: `Your ${plan.name} subscription has been activated successfully.`,
      data: { subscriptionId: savedSubscription.id, planName: plan.name },
    });

    this.logger.log(`Created subscription for organization ${organizationId} with plan ${plan.name}`);

    return this.getSubscriptionWithRelations(savedSubscription.id);
  }

  async getOrganizationSubscription(organizationId: string): Promise<Subscription | null> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { organizationId },
      relations: ['plan', 'organization'],
    });

    return subscription;
  }

  async getSubscriptionWithUsage(organizationId: string): Promise<SubscriptionWithUsage | null> {
    const subscription = await this.getOrganizationSubscription(organizationId);
    if (!subscription) {
      return null;
    }

    // Get current usage (this would integrate with usage tracking service)
    const currentUsage = await this.getCurrentUsage(organizationId);
    
    const limits = subscription.plan.limits;
    
    // Calculate usage percentages
    const usagePercentages = {
      users: Math.round((currentUsage.users / limits.maxUsers) * 100),
      contacts: Math.round((currentUsage.contacts / limits.maxContacts) * 100),
      deals: Math.round((currentUsage.deals / limits.maxDeals) * 100),
      storage: Math.round((currentUsage.storageGB / limits.maxStorageGB) * 100),
      apiCalls: Math.round((currentUsage.apiCalls / limits.apiCallsPerMonth) * 100),
    };

    return {
      subscription,
      currentUsage,
      limits,
      usagePercentages,
    };
  }

  async changeSubscription(request: ChangeSubscriptionRequest): Promise<Subscription> {
    const { organizationId, newPlanId } = request;

    const subscription = await this.getOrganizationSubscription(organizationId);
    if (!subscription) {
      throw new NotFoundException('No active subscription found');
    }

    const newPlan = await this.getPlanById(newPlanId);

    // Update Stripe subscription
    await this.stripeService.updateSubscription({
      subscriptionId: subscription.stripeSubscriptionId,
      priceId: newPlan.stripePriceId,
      metadata: {
        organizationId,
        planId: newPlanId,
        planType: newPlan.type,
      },
    });

    // Update local subscription
    subscription.planId = newPlanId;
    subscription.metadata = {
      ...subscription.metadata,
      planName: newPlan.name,
      previousPlanId: subscription.planId,
      changedAt: new Date().toISOString(),
    };

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    // Send notification
    await this.notificationService.create({
      organizationId,
      type: 'subscription_changed',
      title: 'Subscription Updated',
      message: `Your subscription has been updated to ${newPlan.name}.`,
      data: { subscriptionId: subscription.id, newPlanName: newPlan.name },
    });

    this.logger.log(`Changed subscription for organization ${organizationId} to plan ${newPlan.name}`);

    return this.getSubscriptionWithRelations(updatedSubscription.id);
  }

  async cancelSubscription(organizationId: string, immediately = false): Promise<Subscription> {
    const subscription = await this.getOrganizationSubscription(organizationId);
    if (!subscription) {
      throw new NotFoundException('No active subscription found');
    }

    // Cancel Stripe subscription
    const stripeSubscription = await this.stripeService.cancelSubscription(
      subscription.stripeSubscriptionId,
      immediately,
    );

    // Update local subscription
    subscription.status = this.mapStripeStatus(stripeSubscription.status);
    subscription.canceledAt = new Date();
    subscription.cancelAtPeriodEnd = !immediately;

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    // Send notification
    await this.notificationService.create({
      organizationId,
      type: 'subscription_canceled',
      title: 'Subscription Canceled',
      message: immediately 
        ? 'Your subscription has been canceled immediately.'
        : 'Your subscription will be canceled at the end of the current billing period.',
      data: { subscriptionId: subscription.id, immediately },
    });

    this.logger.log(`Canceled subscription for organization ${organizationId} (immediate: ${immediately})`);

    return updatedSubscription;
  }

  async getOrganizationInvoices(organizationId: string): Promise<Invoice[]> {
    const subscription = await this.getOrganizationSubscription(organizationId);
    if (!subscription) {
      return [];
    }

    return this.invoiceRepository.find({
      where: { subscriptionId: subscription.id },
      order: { createdAt: 'DESC' },
    });
  }

  async createBillingPortalSession(organizationId: string, returnUrl: string): Promise<string> {
    const subscription = await this.getOrganizationSubscription(organizationId);
    if (!subscription) {
      throw new NotFoundException('No active subscription found');
    }

    const session = await this.stripeService.createBillingPortalSession(
      subscription.stripeCustomerId,
      returnUrl,
    );

    return session.url;
  }

  private async getSubscriptionWithRelations(subscriptionId: string): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id: subscriptionId },
      relations: ['plan', 'organization'],
    });

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    return subscription;
  }

  private mapStripeStatus(stripeStatus: string): SubscriptionStatus {
    const statusMap: Record<string, SubscriptionStatus> = {
      active: SubscriptionStatus.ACTIVE,
      trialing: SubscriptionStatus.TRIALING,
      past_due: SubscriptionStatus.PAST_DUE,
      canceled: SubscriptionStatus.CANCELED,
      unpaid: SubscriptionStatus.UNPAID,
      incomplete: SubscriptionStatus.INCOMPLETE,
      incomplete_expired: SubscriptionStatus.INCOMPLETE_EXPIRED,
      paused: SubscriptionStatus.PAUSED,
    };

    return statusMap[stripeStatus] || SubscriptionStatus.INCOMPLETE;
  }

  private async getCurrentUsage(organizationId: string) {
    // This would integrate with actual usage tracking
    // For now, return mock data
    return {
      users: 5,
      contacts: 150,
      deals: 25,
      storageGB: 2.5,
      apiCalls: 1250,
    };
  }

  private async sendSubscriptionWelcomeEmail(organization: any, plan: SubscriptionPlan, subscription: Subscription): Promise<void> {
    await this.emailService.sendTemplate({
      to: organization.primaryContactEmail,
      template: 'subscription-welcome',
      data: {
        organizationName: organization.name,
        planName: plan.name,
        trialDays: subscription.daysLeftInTrial,
        features: plan.features,
      },
    });
  }
}
