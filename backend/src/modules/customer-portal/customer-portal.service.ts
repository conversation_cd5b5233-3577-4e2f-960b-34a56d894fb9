import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PortalSession, PortalSessionType, PortalSessionStatus } from './entities/portal-session.entity';
import { AccountChangeRequest, ChangeRequestType, ChangeRequestStatus } from './entities/account-change-request.entity';
import { BillingNotification } from './entities/billing-notification.entity';
import { OrganizationService } from '../organization/organization.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { UsageMonitoringService } from '../usage-monitoring/usage-monitoring.service';
import { FeatureGatingService } from '../feature-gating/feature-gating.service';
import { UserService } from '../user/user.service';
import { EmailService } from '../email/email.service';
import { NotificationService } from '../notification/notification.service';
import { randomBytes } from 'crypto';

export interface CustomerPortalDashboard {
  organization: {
    id: string;
    name: string;
    status: string;
    createdAt: Date;
  };
  subscription: {
    plan: string;
    status: string;
    billingCycle: string;
    nextBillingDate: Date;
    amount: number;
    currency: string;
    daysUntilRenewal: number;
    isTrialing: boolean;
    trialDaysRemaining: number;
  };
  usage: {
    current: {
      users: number;
      contacts: number;
      deals: number;
      apiCalls: number;
      storageGB: number;
    };
    limits: {
      users: number;
      contacts: number;
      deals: number;
      apiCalls: number;
      storageGB: number;
    };
    percentages: {
      users: number;
      contacts: number;
      deals: number;
      apiCalls: number;
      storageGB: number;
    };
  };
  features: {
    enabled: string[];
    available: string[];
    trial: string[];
  };
  billing: {
    paymentMethod: {
      type: string;
      last4: string;
      expiryMonth: number;
      expiryYear: number;
    };
    billingAddress: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      postalCode: string;
      country: string;
    };
    recentInvoices: Array<{
      id: string;
      number: string;
      amount: number;
      status: string;
      dueDate: Date;
      paidAt?: Date;
    }>;
  };
  notifications: Array<{
    id: string;
    type: string;
    title: string;
    message: string;
    severity: string;
    createdAt: Date;
  }>;
}

export interface CreatePortalSessionRequest {
  organizationId: string;
  userId: string;
  userEmail: string;
  type: PortalSessionType;
  returnUrl?: string;
  expirationMinutes?: number;
  permissions?: string[];
  ipAddress?: string;
  userAgent?: string;
}

export interface CreateChangeRequestDto {
  organizationId: string;
  requestedByUserId: string;
  requestedByEmail: string;
  type: ChangeRequestType;
  title: string;
  description: string;
  requestData: Record<string, any>;
  targetImplementationDate?: Date;
  businessJustification?: string;
}

@Injectable()
export class CustomerPortalService {
  private readonly logger = new Logger(CustomerPortalService.name);

  constructor(
    @InjectRepository(PortalSession)
    private readonly portalSessionRepository: Repository<PortalSession>,
    @InjectRepository(AccountChangeRequest)
    private readonly changeRequestRepository: Repository<AccountChangeRequest>,
    @InjectRepository(BillingNotification)
    private readonly billingNotificationRepository: Repository<BillingNotification>,
    private readonly organizationService: OrganizationService,
    private readonly subscriptionService: SubscriptionService,
    private readonly usageMonitoringService: UsageMonitoringService,
    private readonly featureGatingService: FeatureGatingService,
    private readonly userService: UserService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
  ) {}

  async createPortalSession(request: CreatePortalSessionRequest): Promise<PortalSession> {
    // Validate organization and user
    const organization = await this.organizationService.findById(request.organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const user = await this.userService.findById(request.userId);
    if (!user || user.organizationId !== request.organizationId) {
      throw new ForbiddenException('User not authorized for this organization');
    }

    // Generate session token
    const sessionToken = randomBytes(32).toString('hex');

    // Calculate expiration (default 4 hours)
    const expirationMinutes = request.expirationMinutes || 240;
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);

    // Create session
    const session = this.portalSessionRepository.create({
      organizationId: request.organizationId,
      userId: request.userId,
      userEmail: request.userEmail,
      type: request.type,
      sessionToken,
      returnUrl: request.returnUrl,
      expiresAt,
      ipAddress: request.ipAddress,
      userAgent: request.userAgent,
      metadata: {
        permissions: request.permissions || [],
      },
      accessLog: [{
        timestamp: new Date(),
        action: 'session_created',
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      }],
    });

    const savedSession = await this.portalSessionRepository.save(session);

    this.logger.log(`Portal session created for user ${request.userId} in organization ${request.organizationId}`);

    return savedSession;
  }

  async validatePortalSession(sessionToken: string): Promise<PortalSession> {
    const session = await this.portalSessionRepository.findOne({
      where: { sessionToken, status: PortalSessionStatus.ACTIVE },
      relations: ['organization'],
    });

    if (!session) {
      throw new ForbiddenException('Invalid session token');
    }

    if (!session.isActive) {
      throw new ForbiddenException('Session expired');
    }

    // Update last accessed time
    session.lastAccessedAt = new Date();
    await this.portalSessionRepository.save(session);

    return session;
  }

  async getCustomerPortalDashboard(organizationId: string): Promise<CustomerPortalDashboard> {
    // Get organization
    const organization = await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Get subscription with usage
    const subscriptionWithUsage = await this.subscriptionService.getSubscriptionWithUsage(organizationId);
    
    // Get usage stats
    const usageStats = await this.usageMonitoringService.getUsageStats(organizationId);

    // Get enabled features
    const organizationFeatures = await this.featureGatingService.getOrganizationFeatures(organizationId);
    const availableFeatures = await this.featureGatingService.getAvailableFeatures(organizationId);

    // Get recent invoices
    const recentInvoices = await this.subscriptionService.getOrganizationInvoices(organizationId);

    // Get billing notifications
    const notifications = await this.billingNotificationRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    // Build dashboard data
    const dashboard: CustomerPortalDashboard = {
      organization: {
        id: organization.id,
        name: organization.name,
        status: organization.suspended ? 'suspended' : 'active',
        createdAt: organization.createdAt,
      },
      subscription: {
        plan: subscriptionWithUsage?.subscription?.plan?.name || 'Free',
        status: subscriptionWithUsage?.subscription?.status || 'none',
        billingCycle: subscriptionWithUsage?.subscription?.plan?.billingInterval || 'monthly',
        nextBillingDate: subscriptionWithUsage?.subscription?.currentPeriodEnd || new Date(),
        amount: subscriptionWithUsage?.subscription?.plan?.price || 0,
        currency: 'USD',
        daysUntilRenewal: subscriptionWithUsage?.subscription?.daysUntilRenewal || 0,
        isTrialing: subscriptionWithUsage?.subscription?.isTrialing || false,
        trialDaysRemaining: subscriptionWithUsage?.subscription?.daysLeftInTrial || 0,
      },
      usage: {
        current: subscriptionWithUsage?.currentUsage || {
          users: 0,
          contacts: 0,
          deals: 0,
          apiCalls: 0,
          storageGB: 0,
        },
        limits: subscriptionWithUsage?.limits || {
          users: 0,
          contacts: 0,
          deals: 0,
          apiCalls: 0,
          storageGB: 0,
        },
        percentages: subscriptionWithUsage?.usagePercentages || {
          users: 0,
          contacts: 0,
          deals: 0,
          apiCalls: 0,
          storageGB: 0,
        },
      },
      features: {
        enabled: organizationFeatures.filter(f => f.isEnabled).map(f => f.featureFlag.key),
        available: availableFeatures.map(f => f.key),
        trial: organizationFeatures.filter(f => f.isInTrial).map(f => f.featureFlag.key),
      },
      billing: {
        paymentMethod: {
          type: 'card',
          last4: '****',
          expiryMonth: 12,
          expiryYear: 2025,
        },
        billingAddress: {
          line1: organization.address?.line1 || '',
          line2: organization.address?.line2,
          city: organization.address?.city || '',
          state: organization.address?.state || '',
          postalCode: organization.address?.postalCode || '',
          country: organization.address?.country || '',
        },
        recentInvoices: recentInvoices.slice(0, 5).map(invoice => ({
          id: invoice.id,
          number: invoice.invoiceNumber,
          amount: invoice.total,
          status: invoice.status,
          dueDate: invoice.dueDate,
          paidAt: invoice.paidAt,
        })),
      },
      notifications: notifications.map(notification => ({
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        severity: notification.severity,
        createdAt: notification.createdAt,
      })),
    };

    return dashboard;
  }

  async createChangeRequest(dto: CreateChangeRequestDto): Promise<AccountChangeRequest> {
    const changeRequest = this.changeRequestRepository.create({
      organizationId: dto.organizationId,
      requestedByUserId: dto.requestedByUserId,
      requestedByEmail: dto.requestedByEmail,
      type: dto.type,
      title: dto.title,
      description: dto.description,
      requestData: {
        ...dto.requestData,
        businessJustification: dto.businessJustification,
      },
      targetImplementationDate: dto.targetImplementationDate,
    });

    const savedRequest = await this.changeRequestRepository.save(changeRequest);

    // Send notification to admin team
    await this.notifyAdminTeam(savedRequest);

    // Send confirmation to customer
    await this.sendChangeRequestConfirmation(savedRequest);

    this.logger.log(`Change request created: ${dto.type} for organization ${dto.organizationId}`);

    return savedRequest;
  }

  async getChangeRequests(organizationId: string): Promise<AccountChangeRequest[]> {
    return this.changeRequestRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }

  async cancelChangeRequest(requestId: string, organizationId: string): Promise<AccountChangeRequest> {
    const changeRequest = await this.changeRequestRepository.findOne({
      where: { id: requestId, organizationId },
    });

    if (!changeRequest) {
      throw new NotFoundException('Change request not found');
    }

    if (changeRequest.status !== ChangeRequestStatus.PENDING) {
      throw new ForbiddenException('Can only cancel pending requests');
    }

    changeRequest.status = ChangeRequestStatus.CANCELLED;
    
    return this.changeRequestRepository.save(changeRequest);
  }

  async revokePortalSession(sessionToken: string): Promise<void> {
    const session = await this.portalSessionRepository.findOne({
      where: { sessionToken },
    });

    if (session) {
      session.status = PortalSessionStatus.REVOKED;
      await this.portalSessionRepository.save(session);
    }
  }

  private async notifyAdminTeam(changeRequest: AccountChangeRequest): Promise<void> {
    // This would send notifications to the admin team
    await this.emailService.sendTemplate({
      to: '<EMAIL>',
      template: 'change-request-notification',
      data: {
        requestId: changeRequest.id,
        type: changeRequest.type,
        organizationId: changeRequest.organizationId,
        requestedBy: changeRequest.requestedByEmail,
        description: changeRequest.description,
      },
    });
  }

  private async sendChangeRequestConfirmation(changeRequest: AccountChangeRequest): Promise<void> {
    await this.emailService.sendTemplate({
      to: changeRequest.requestedByEmail,
      template: 'change-request-confirmation',
      data: {
        requestId: changeRequest.id,
        type: changeRequest.type,
        title: changeRequest.title,
        description: changeRequest.description,
      },
    });
  }
}
