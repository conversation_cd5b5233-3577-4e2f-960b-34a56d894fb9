import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerPortalController } from './customer-portal.controller';
import { CustomerPortalService } from './customer-portal.service';
import { BillingPortalService } from './billing-portal.service';
import { AccountManagementService } from './account-management.service';
import { PortalSession } from './entities/portal-session.entity';
import { AccountChangeRequest } from './entities/account-change-request.entity';
import { BillingNotification } from './entities/billing-notification.entity';
import { OrganizationModule } from '../organization/organization.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UsageMonitoringModule } from '../usage-monitoring/usage-monitoring.module';
import { FeatureGatingModule } from '../feature-gating/feature-gating.module';
import { UserModule } from '../user/user.module';
import { EmailModule } from '../email/email.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PortalSession,
      AccountChangeRequest,
      BillingNotification,
    ]),
    OrganizationModule,
    SubscriptionModule,
    UsageMonitoringModule,
    FeatureGatingModule,
    UserModule,
    EmailModule,
    NotificationModule,
  ],
  controllers: [CustomerPortalController],
  providers: [
    CustomerPortalService,
    BillingPortalService,
    AccountManagementService,
  ],
  exports: [
    CustomerPortalService,
    BillingPortalService,
    AccountManagementService,
  ],
})
export class CustomerPortalModule {}
