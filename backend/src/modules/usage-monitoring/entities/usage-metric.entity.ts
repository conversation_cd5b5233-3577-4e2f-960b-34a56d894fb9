import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';
import { UsageMetricType } from './usage-record.entity';

export enum AggregationPeriod {
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

@Entity('usage_metrics')
@Index(['organizationId', 'metricType', 'period', 'aggregationPeriod'], { unique: true })
export class UsageMetric {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({
    type: 'enum',
    enum: UsageMetricType,
  })
  metricType: UsageMetricType;

  @Column({
    type: 'enum',
    enum: AggregationPeriod,
  })
  aggregationPeriod: AggregationPeriod;

  @Column({ type: 'varchar', length: 50 })
  period: string;

  @Column({ type: 'bigint' })
  currentValue: number;

  @Column({ type: 'bigint', nullable: true })
  previousValue: number;

  @Column({ type: 'bigint', nullable: true })
  maxValue: number;

  @Column({ type: 'bigint', nullable: true })
  minValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  averageValue: number;

  @Column({ type: 'bigint', default: 0 })
  totalIncrement: number;

  @Column({ type: 'bigint', default: 0 })
  totalDecrement: number;

  @Column({ type: 'int', default: 0 })
  recordCount: number;

  @Column({ type: 'timestamp', name: 'first_recorded_at', nullable: true })
  firstRecordedAt: Date;

  @Column({ type: 'timestamp', name: 'last_recorded_at', nullable: true })
  lastRecordedAt: Date;

  @Column({ type: 'json', nullable: true })
  additionalStats: {
    percentileP50?: number;
    percentileP90?: number;
    percentileP95?: number;
    percentileP99?: number;
    standardDeviation?: number;
    trend?: 'increasing' | 'decreasing' | 'stable';
    changeRate?: number;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get changeFromPrevious(): number {
    if (!this.previousValue) return 0;
    return this.currentValue - this.previousValue;
  }

  get changePercentage(): number {
    if (!this.previousValue || this.previousValue === 0) return 0;
    return ((this.currentValue - this.previousValue) / this.previousValue) * 100;
  }

  get netChange(): number {
    return this.totalIncrement - this.totalDecrement;
  }
}
