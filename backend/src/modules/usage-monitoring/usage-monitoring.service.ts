import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UsageRecord, UsageMetricType, UsageRecordType } from './entities/usage-record.entity';
import { UsageMetric, AggregationPeriod } from './entities/usage-metric.entity';
import { ApiUsageLog, HttpMethod } from './entities/api-usage-log.entity';
import { UsageAlert } from './entities/usage-alert.entity';
import { OrganizationService } from '../organization/organization.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { NotificationService } from '../notification/notification.service';

export interface RecordUsageDto {
  organizationId: string;
  metricType: UsageMetricType;
  value: number;
  recordType?: UsageRecordType;
  metadata?: Record<string, any>;
  source?: string;
  notes?: string;
}

export interface ApiUsageDto {
  organizationId: string;
  userId?: string;
  method: HttpMethod;
  endpoint: string;
  route?: string;
  statusCode: number;
  responseTime?: number;
  requestSize?: number;
  responseSize?: number;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

export interface UsageStatsResponse {
  organizationId: string;
  period: string;
  metrics: {
    [key in UsageMetricType]?: {
      current: number;
      previous?: number;
      change: number;
      changePercentage: number;
      limit?: number;
      usagePercentage?: number;
    };
  };
  apiUsage: {
    totalCalls: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
    topEndpoints: Array<{
      endpoint: string;
      calls: number;
      averageResponseTime: number;
    }>;
  };
  alerts: UsageAlert[];
}

@Injectable()
export class UsageMonitoringService {
  private readonly logger = new Logger(UsageMonitoringService.name);

  constructor(
    @InjectRepository(UsageRecord)
    private readonly usageRecordRepository: Repository<UsageRecord>,
    @InjectRepository(UsageMetric)
    private readonly usageMetricRepository: Repository<UsageMetric>,
    @InjectRepository(ApiUsageLog)
    private readonly apiUsageLogRepository: Repository<ApiUsageLog>,
    @InjectRepository(UsageAlert)
    private readonly usageAlertRepository: Repository<UsageAlert>,
    private readonly organizationService: OrganizationService,
    private readonly subscriptionService: SubscriptionService,
    private readonly notificationService: NotificationService,
  ) {}

  async recordUsage(dto: RecordUsageDto): Promise<UsageRecord> {
    const now = new Date();
    const period = this.getPeriodString(now, 'daily');

    // Get previous value for increment/decrement operations
    let previousValue: number | undefined;
    if (dto.recordType === UsageRecordType.INCREMENT || dto.recordType === UsageRecordType.DECREMENT) {
      const lastRecord = await this.getLatestUsageRecord(dto.organizationId, dto.metricType);
      previousValue = lastRecord?.value || 0;
    }

    const usageRecord = this.usageRecordRepository.create({
      organizationId: dto.organizationId,
      metricType: dto.metricType,
      recordType: dto.recordType || UsageRecordType.SNAPSHOT,
      value: dto.value,
      previousValue,
      period,
      recordedAt: now,
      metadata: dto.metadata,
      source: dto.source,
      notes: dto.notes,
    });

    const savedRecord = await this.usageRecordRepository.save(usageRecord);

    // Update aggregated metrics
    await this.updateAggregatedMetrics(dto.organizationId, dto.metricType, savedRecord);

    // Check for usage alerts
    await this.checkUsageAlerts(dto.organizationId, dto.metricType, dto.value);

    return savedRecord;
  }

  async recordApiUsage(dto: ApiUsageDto): Promise<void> {
    const apiLog = this.apiUsageLogRepository.create({
      ...dto,
      timestamp: new Date(),
    });

    await this.apiUsageLogRepository.save(apiLog);

    // Record as usage metric
    await this.recordUsage({
      organizationId: dto.organizationId,
      metricType: UsageMetricType.API_CALLS,
      value: 1,
      recordType: UsageRecordType.INCREMENT,
      metadata: {
        endpoint: dto.endpoint,
        method: dto.method,
        statusCode: dto.statusCode,
        responseTime: dto.responseTime,
      },
      source: 'api_gateway',
    });
  }

  async getUsageStats(organizationId: string, period?: string): Promise<UsageStatsResponse> {
    const targetPeriod = period || this.getPeriodString(new Date(), 'daily');
    const previousPeriod = this.getPreviousPeriod(targetPeriod, 'daily');

    // Get current metrics
    const currentMetrics = await this.usageMetricRepository.find({
      where: {
        organizationId,
        period: targetPeriod,
        aggregationPeriod: AggregationPeriod.DAILY,
      },
    });

    // Get previous metrics for comparison
    const previousMetrics = await this.usageMetricRepository.find({
      where: {
        organizationId,
        period: previousPeriod,
        aggregationPeriod: AggregationPeriod.DAILY,
      },
    });

    // Get subscription limits
    const subscriptionWithUsage = await this.subscriptionService.getSubscriptionWithUsage(organizationId);
    const limits = subscriptionWithUsage?.limits;

    // Build metrics response
    const metrics: UsageStatsResponse['metrics'] = {};
    
    for (const metric of currentMetrics) {
      const previousMetric = previousMetrics.find(m => m.metricType === metric.metricType);
      const limit = limits ? limits[this.getLimitKey(metric.metricType)] : undefined;
      
      metrics[metric.metricType] = {
        current: metric.currentValue,
        previous: previousMetric?.currentValue,
        change: metric.changeFromPrevious,
        changePercentage: metric.changePercentage,
        limit,
        usagePercentage: limit ? Math.round((metric.currentValue / limit) * 100) : undefined,
      };
    }

    // Get API usage stats
    const apiUsage = await this.getApiUsageStats(organizationId, targetPeriod);

    // Get active alerts
    const alerts = await this.usageAlertRepository.find({
      where: {
        organizationId,
        resolved: false,
      },
      order: { createdAt: 'DESC' },
    });

    return {
      organizationId,
      period: targetPeriod,
      metrics,
      apiUsage,
      alerts,
    };
  }

  async getUsageTrends(
    organizationId: string,
    metricType: UsageMetricType,
    days = 30,
  ): Promise<Array<{ date: string; value: number }>> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const metrics = await this.usageMetricRepository.find({
      where: {
        organizationId,
        metricType,
        aggregationPeriod: AggregationPeriod.DAILY,
        period: Between(
          this.getPeriodString(startDate, 'daily'),
          this.getPeriodString(endDate, 'daily'),
        ),
      },
      order: { period: 'ASC' },
    });

    return metrics.map(metric => ({
      date: metric.period,
      value: metric.currentValue,
    }));
  }

  @Cron(CronExpression.EVERY_HOUR)
  async aggregateHourlyMetrics(): Promise<void> {
    this.logger.log('Starting hourly metrics aggregation');
    
    const organizations = await this.organizationService.findAll();
    
    for (const org of organizations) {
      await this.aggregateMetricsForOrganization(org.id, AggregationPeriod.HOURLY);
    }
    
    this.logger.log('Completed hourly metrics aggregation');
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async aggregateDailyMetrics(): Promise<void> {
    this.logger.log('Starting daily metrics aggregation');
    
    const organizations = await this.organizationService.findAll();
    
    for (const org of organizations) {
      await this.aggregateMetricsForOrganization(org.id, AggregationPeriod.DAILY);
    }
    
    this.logger.log('Completed daily metrics aggregation');
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupOldLogs(): Promise<void> {
    this.logger.log('Starting cleanup of old usage logs');
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep 90 days of detailed logs
    
    const deletedCount = await this.apiUsageLogRepository.delete({
      createdAt: MoreThan(cutoffDate),
    });
    
    this.logger.log(`Cleaned up ${deletedCount.affected} old API usage logs`);
  }

  private async getLatestUsageRecord(organizationId: string, metricType: UsageMetricType): Promise<UsageRecord | null> {
    return this.usageRecordRepository.findOne({
      where: { organizationId, metricType },
      order: { recordedAt: 'DESC' },
    });
  }

  private async updateAggregatedMetrics(
    organizationId: string,
    metricType: UsageMetricType,
    record: UsageRecord,
  ): Promise<void> {
    const periods = [
      { period: AggregationPeriod.HOURLY, periodString: this.getPeriodString(record.recordedAt, 'hourly') },
      { period: AggregationPeriod.DAILY, periodString: this.getPeriodString(record.recordedAt, 'daily') },
      { period: AggregationPeriod.MONTHLY, periodString: this.getPeriodString(record.recordedAt, 'monthly') },
    ];

    for (const { period, periodString } of periods) {
      await this.updateMetricForPeriod(organizationId, metricType, period, periodString, record);
    }
  }

  private async updateMetricForPeriod(
    organizationId: string,
    metricType: UsageMetricType,
    aggregationPeriod: AggregationPeriod,
    period: string,
    record: UsageRecord,
  ): Promise<void> {
    let metric = await this.usageMetricRepository.findOne({
      where: { organizationId, metricType, aggregationPeriod, period },
    });

    if (!metric) {
      metric = this.usageMetricRepository.create({
        organizationId,
        metricType,
        aggregationPeriod,
        period,
        currentValue: 0,
        recordCount: 0,
      });
    }

    // Update metric based on record type
    switch (record.recordType) {
      case UsageRecordType.SNAPSHOT:
        metric.currentValue = record.value;
        break;
      case UsageRecordType.INCREMENT:
        metric.currentValue += record.value;
        metric.totalIncrement += record.value;
        break;
      case UsageRecordType.DECREMENT:
        metric.currentValue = Math.max(0, metric.currentValue - record.value);
        metric.totalDecrement += record.value;
        break;
      case UsageRecordType.RESET:
        metric.currentValue = record.value;
        break;
    }

    // Update statistics
    metric.recordCount += 1;
    metric.lastRecordedAt = record.recordedAt;
    
    if (!metric.firstRecordedAt) {
      metric.firstRecordedAt = record.recordedAt;
    }

    if (!metric.maxValue || record.value > metric.maxValue) {
      metric.maxValue = record.value;
    }

    if (!metric.minValue || record.value < metric.minValue) {
      metric.minValue = record.value;
    }

    await this.usageMetricRepository.save(metric);
  }

  private async aggregateMetricsForOrganization(
    organizationId: string,
    aggregationPeriod: AggregationPeriod,
  ): Promise<void> {
    // This would implement more sophisticated aggregation logic
    // For now, we'll skip as the real-time updates handle most cases
  }

  private async getApiUsageStats(organizationId: string, period: string) {
    const startDate = new Date(period);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 1);

    const logs = await this.apiUsageLogRepository.find({
      where: {
        organizationId,
        timestamp: Between(startDate, endDate),
      },
    });

    const totalCalls = logs.length;
    const successfulCalls = logs.filter(log => log.isSuccess).length;
    const successRate = totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0;
    const errorRate = 100 - successRate;

    const responseTimes = logs.filter(log => log.responseTime).map(log => log.responseTime!);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Get top endpoints
    const endpointStats = logs.reduce((acc, log) => {
      if (!acc[log.endpoint]) {
        acc[log.endpoint] = { calls: 0, totalResponseTime: 0, responseTimeCount: 0 };
      }
      acc[log.endpoint].calls += 1;
      if (log.responseTime) {
        acc[log.endpoint].totalResponseTime += log.responseTime;
        acc[log.endpoint].responseTimeCount += 1;
      }
      return acc;
    }, {} as Record<string, any>);

    const topEndpoints = Object.entries(endpointStats)
      .map(([endpoint, stats]: [string, any]) => ({
        endpoint,
        calls: stats.calls,
        averageResponseTime: stats.responseTimeCount > 0 
          ? stats.totalResponseTime / stats.responseTimeCount 
          : 0,
      }))
      .sort((a, b) => b.calls - a.calls)
      .slice(0, 10);

    return {
      totalCalls,
      successRate: Math.round(successRate * 100) / 100,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      topEndpoints,
    };
  }

  private async checkUsageAlerts(organizationId: string, metricType: UsageMetricType, value: number): Promise<void> {
    // Get subscription limits
    const subscriptionWithUsage = await this.subscriptionService.getSubscriptionWithUsage(organizationId);
    if (!subscriptionWithUsage) return;

    const limit = subscriptionWithUsage.limits[this.getLimitKey(metricType)];
    if (!limit) return;

    const usagePercentage = (value / limit) * 100;

    // Check for threshold alerts (80%, 90%, 100%)
    const thresholds = [80, 90, 100];
    
    for (const threshold of thresholds) {
      if (usagePercentage >= threshold) {
        await this.createUsageAlert(organizationId, metricType, value, limit, threshold);
      }
    }
  }

  private async createUsageAlert(
    organizationId: string,
    metricType: UsageMetricType,
    currentValue: number,
    limit: number,
    threshold: number,
  ): Promise<void> {
    // Check if alert already exists for this threshold
    const existingAlert = await this.usageAlertRepository.findOne({
      where: {
        organizationId,
        metricType,
        threshold,
        resolved: false,
      },
    });

    if (existingAlert) return;

    // Create new alert
    const alert = this.usageAlertRepository.create({
      organizationId,
      metricType,
      currentValue,
      limit,
      threshold,
      usagePercentage: (currentValue / limit) * 100,
      severity: threshold >= 100 ? 'critical' : threshold >= 90 ? 'high' : 'medium',
      message: `${metricType} usage has reached ${threshold}% of your plan limit (${currentValue}/${limit})`,
    });

    await this.usageAlertRepository.save(alert);

    // Send notification
    await this.notificationService.create({
      organizationId,
      type: 'usage_alert',
      title: `Usage Alert: ${metricType}`,
      message: alert.message,
      data: { alertId: alert.id, metricType, threshold, currentValue, limit },
    });
  }

  private getPeriodString(date: Date, granularity: 'hourly' | 'daily' | 'monthly'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');

    switch (granularity) {
      case 'hourly':
        return `${year}-${month}-${day}-${hour}`;
      case 'daily':
        return `${year}-${month}-${day}`;
      case 'monthly':
        return `${year}-${month}`;
      default:
        return `${year}-${month}-${day}`;
    }
  }

  private getPreviousPeriod(period: string, granularity: 'daily' | 'monthly'): string {
    const date = new Date(period);
    
    if (granularity === 'daily') {
      date.setDate(date.getDate() - 1);
    } else {
      date.setMonth(date.getMonth() - 1);
    }
    
    return this.getPeriodString(date, granularity);
  }

  private getLimitKey(metricType: UsageMetricType): string {
    const limitMap: Record<UsageMetricType, string> = {
      [UsageMetricType.USERS]: 'maxUsers',
      [UsageMetricType.CONTACTS]: 'maxContacts',
      [UsageMetricType.COMPANIES]: 'maxContacts', // Companies count towards contacts
      [UsageMetricType.DEALS]: 'maxDeals',
      [UsageMetricType.API_CALLS]: 'apiCallsPerMonth',
      [UsageMetricType.STORAGE_BYTES]: 'maxStorageGB',
      [UsageMetricType.ACTIVITIES]: 'maxContacts', // Activities are unlimited but tied to contacts
      [UsageMetricType.EMAIL_SENDS]: 'apiCallsPerMonth', // Email sends count as API calls
      [UsageMetricType.WEBHOOKS]: 'maxWebhooks',
      [UsageMetricType.INTEGRATIONS]: 'maxIntegrations',
      [UsageMetricType.CUSTOM_FIELDS]: 'maxCustomFields',
      [UsageMetricType.REPORTS_GENERATED]: 'apiCallsPerMonth',
      [UsageMetricType.EXPORTS]: 'apiCallsPerMonth',
      [UsageMetricType.IMPORTS]: 'apiCallsPerMonth',
    };

    return limitMap[metricType] || 'maxContacts';
  }
}
