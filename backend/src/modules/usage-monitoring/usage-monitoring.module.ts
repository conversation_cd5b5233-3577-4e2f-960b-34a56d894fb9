import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { UsageMonitoringController } from './usage-monitoring.controller';
import { UsageMonitoringService } from './usage-monitoring.service';
import { UsageMetricsService } from './usage-metrics.service';
import { UsageAggregationService } from './usage-aggregation.service';
import { UsageRecord } from './entities/usage-record.entity';
import { UsageMetric } from './entities/usage-metric.entity';
import { UsageAlert } from './entities/usage-alert.entity';
import { UsageReport } from './entities/usage-report.entity';
import { ApiUsageLog } from './entities/api-usage-log.entity';
import { OrganizationModule } from '../organization/organization.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { NotificationModule } from '../notification/notification.module';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UsageRecord,
      UsageMetric,
      UsageAlert,
      UsageReport,
      ApiUsageLog,
    ]),
    ScheduleModule.forRoot(),
    OrganizationModule,
    SubscriptionModule,
    NotificationModule,
    EmailModule,
  ],
  controllers: [UsageMonitoringController],
  providers: [
    UsageMonitoringService,
    UsageMetricsService,
    UsageAggregationService,
  ],
  exports: [
    UsageMonitoringService,
    UsageMetricsService,
  ],
})
export class UsageMonitoringModule {}
