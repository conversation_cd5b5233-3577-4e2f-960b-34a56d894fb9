import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeatureGatingController } from './feature-gating.controller';
import { FeatureGatingService } from './feature-gating.service';
import { FeatureFlagService } from './feature-flag.service';
import { UsageLimitService } from './usage-limit.service';
import { FeatureFlag } from './entities/feature-flag.entity';
import { OrganizationFeature } from './entities/organization-feature.entity';
import { UsageLimit } from './entities/usage-limit.entity';
import { FeatureUsage } from './entities/feature-usage.entity';
import { FeatureGate } from './decorators/feature-gate.decorator';
import { FeatureGateGuard } from './guards/feature-gate.guard';
import { UsageLimitGuard } from './guards/usage-limit.guard';
import { OrganizationModule } from '../organization/organization.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UsageMonitoringModule } from '../usage-monitoring/usage-monitoring.module';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      FeatureFlag,
      OrganizationFeature,
      UsageLimit,
      FeatureUsage,
    ]),
    OrganizationModule,
    SubscriptionModule,
    UsageMonitoringModule,
  ],
  controllers: [FeatureGatingController],
  providers: [
    FeatureGatingService,
    FeatureFlagService,
    UsageLimitService,
    FeatureGateGuard,
    UsageLimitGuard,
  ],
  exports: [
    FeatureGatingService,
    FeatureFlagService,
    UsageLimitService,
    FeatureGateGuard,
    UsageLimitGuard,
  ],
})
export class FeatureGatingModule {}
