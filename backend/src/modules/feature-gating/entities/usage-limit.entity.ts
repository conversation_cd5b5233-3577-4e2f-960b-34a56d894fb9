import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum LimitType {
  COUNT = 'count',
  RATE = 'rate',
  QUOTA = 'quota',
  SIZE = 'size',
  DURATION = 'duration',
}

export enum LimitScope {
  GLOBAL = 'global',
  ORGANIZATION = 'organization',
  USER = 'user',
  FEATURE = 'feature',
  ENDPOINT = 'endpoint',
}

export enum LimitPeriod {
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  LIFETIME = 'lifetime',
}

export enum LimitAction {
  BLOCK = 'block',
  THROTTLE = 'throttle',
  WARN = 'warn',
  UPGRADE_PROMPT = 'upgrade_prompt',
  QUEUE = 'queue',
}

@Entity('usage_limits')
@Index(['organizationId', 'limitKey'], { unique: true })
export class UsageLimit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @Column({ type: 'varchar', length: 255 })
  limitKey: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: LimitType,
  })
  type: LimitType;

  @Column({
    type: 'enum',
    enum: LimitScope,
  })
  scope: LimitScope;

  @Column({
    type: 'enum',
    enum: LimitPeriod,
    default: LimitPeriod.MONTH,
  })
  period: LimitPeriod;

  @Column({ type: 'bigint' })
  maxValue: number;

  @Column({ type: 'bigint', default: 0 })
  currentValue: number;

  @Column({ type: 'bigint', nullable: true })
  warningThreshold: number;

  @Column({
    type: 'enum',
    enum: LimitAction,
    default: LimitAction.BLOCK,
  })
  actionOnExceed: LimitAction;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ type: 'json', nullable: true })
  configuration: {
    resetTime?: string; // cron expression
    gracePeriod?: number; // in seconds
    burstLimit?: number;
    throttleRate?: number;
    customMessage?: string;
    upgradeUrl?: string;
    exemptUsers?: string[];
    exemptRoles?: string[];
  };

  @Column({ type: 'timestamp', nullable: true })
  lastResetAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  nextResetAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastExceededAt: Date;

  @Column({ type: 'int', default: 0 })
  exceedCount: number;

  @Column({ type: 'json', nullable: true })
  metadata: {
    planType?: string;
    featureKey?: string;
    endpoint?: string;
    method?: string;
    tags?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get usagePercentage(): number {
    if (this.maxValue === 0) return 0;
    return Math.round((this.currentValue / this.maxValue) * 100);
  }

  get remainingValue(): number {
    return Math.max(0, this.maxValue - this.currentValue);
  }

  get isExceeded(): boolean {
    return this.currentValue >= this.maxValue;
  }

  get isNearLimit(): boolean {
    if (!this.warningThreshold) return false;
    return this.currentValue >= this.warningThreshold;
  }

  get timeUntilReset(): number {
    if (!this.nextResetAt) return 0;
    const now = new Date();
    const diff = this.nextResetAt.getTime() - now.getTime();
    return Math.max(0, diff);
  }

  get isInGracePeriod(): boolean {
    if (!this.configuration?.gracePeriod || !this.lastExceededAt) return false;
    const now = new Date();
    const gracePeriodEnd = new Date(this.lastExceededAt.getTime() + (this.configuration.gracePeriod * 1000));
    return now < gracePeriodEnd;
  }
}
