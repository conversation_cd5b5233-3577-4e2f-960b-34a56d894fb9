import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeatureFlag, FeatureScope, FeatureCategory } from './entities/feature-flag.entity';
import { OrganizationFeature, FeatureStatus } from './entities/organization-feature.entity';
import { UsageLimit, LimitAction } from './entities/usage-limit.entity';
import { SubscriptionService } from '../subscription/subscription.service';
import { UsageMonitoringService } from '../usage-monitoring/usage-monitoring.service';

export interface FeatureCheckRequest {
  organizationId: string;
  featureKey: string;
  userId?: string;
  context?: Record<string, any>;
}

export interface FeatureCheckResponse {
  enabled: boolean;
  value?: any;
  reason?: string;
  metadata?: {
    planRequired?: string;
    trialAvailable?: boolean;
    upgradeUrl?: string;
    daysUntilExpiry?: number;
    usageInfo?: {
      current: number;
      limit: number;
      percentage: number;
    };
  };
}

export interface BulkFeatureCheckRequest {
  organizationId: string;
  featureKeys: string[];
  userId?: string;
  context?: Record<string, any>;
}

export interface EnableFeatureRequest {
  organizationId: string;
  featureKey: string;
  value?: any;
  configuration?: Record<string, any>;
  enabledBy?: string;
  expiresAt?: Date;
  startTrial?: boolean;
  trialDays?: number;
}

@Injectable()
export class FeatureGatingService {
  private readonly logger = new Logger(FeatureGatingService.name);

  constructor(
    @InjectRepository(FeatureFlag)
    private readonly featureFlagRepository: Repository<FeatureFlag>,
    @InjectRepository(OrganizationFeature)
    private readonly organizationFeatureRepository: Repository<OrganizationFeature>,
    @InjectRepository(UsageLimit)
    private readonly usageLimitRepository: Repository<UsageLimit>,
    private readonly subscriptionService: SubscriptionService,
    private readonly usageMonitoringService: UsageMonitoringService,
  ) {}

  async checkFeature(request: FeatureCheckRequest): Promise<FeatureCheckResponse> {
    const { organizationId, featureKey, userId, context } = request;

    // Get feature flag
    const featureFlag = await this.featureFlagRepository.findOne({
      where: { key: featureKey, enabled: true },
    });

    if (!featureFlag) {
      return {
        enabled: false,
        reason: 'Feature not found or disabled',
      };
    }

    // Check global features
    if (featureFlag.scope === FeatureScope.GLOBAL) {
      return {
        enabled: featureFlag.isGloballyEnabled,
        value: featureFlag.defaultValue,
        reason: featureFlag.isGloballyEnabled ? 'Globally enabled' : 'Globally disabled',
      };
    }

    // Get organization-specific feature configuration
    const orgFeature = await this.organizationFeatureRepository.findOne({
      where: { organizationId, featureFlagId: featureFlag.id },
      relations: ['featureFlag'],
    });

    // Check subscription-based access
    const subscriptionCheck = await this.checkSubscriptionAccess(organizationId, featureFlag);
    if (!subscriptionCheck.allowed) {
      return {
        enabled: false,
        reason: subscriptionCheck.reason,
        metadata: subscriptionCheck.metadata,
      };
    }

    // Check organization-specific configuration
    if (orgFeature) {
      if (!orgFeature.isEnabled) {
        return {
          enabled: false,
          reason: orgFeature.isExpired ? 'Feature expired' : 'Feature disabled for organization',
          metadata: {
            daysUntilExpiry: orgFeature.daysUntilExpiry,
          },
        };
      }

      // Check usage limits
      const usageLimitCheck = await this.checkUsageLimits(organizationId, featureKey, context);
      if (!usageLimitCheck.allowed) {
        return {
          enabled: false,
          reason: usageLimitCheck.reason,
          metadata: usageLimitCheck.metadata,
        };
      }

      return {
        enabled: true,
        value: orgFeature.value || featureFlag.defaultValue,
        metadata: {
          daysUntilExpiry: orgFeature.daysUntilExpiry,
          usageInfo: usageLimitCheck.usageInfo,
        },
      };
    }

    // Check if feature should be inherited from plan
    if (subscriptionCheck.inheritFromPlan) {
      return {
        enabled: true,
        value: featureFlag.defaultValue,
        reason: 'Inherited from subscription plan',
        metadata: subscriptionCheck.metadata,
      };
    }

    // Default to feature flag default
    return {
      enabled: featureFlag.defaultValue === true,
      value: featureFlag.defaultValue,
      reason: 'Using default value',
    };
  }

  async checkMultipleFeatures(request: BulkFeatureCheckRequest): Promise<Record<string, FeatureCheckResponse>> {
    const { organizationId, featureKeys, userId, context } = request;
    const results: Record<string, FeatureCheckResponse> = {};

    // Process features in parallel
    const checks = featureKeys.map(async (featureKey) => {
      const result = await this.checkFeature({
        organizationId,
        featureKey,
        userId,
        context,
      });
      return { featureKey, result };
    });

    const checkResults = await Promise.all(checks);
    
    for (const { featureKey, result } of checkResults) {
      results[featureKey] = result;
    }

    return results;
  }

  async enableFeature(request: EnableFeatureRequest): Promise<OrganizationFeature> {
    const { organizationId, featureKey, value, configuration, enabledBy, expiresAt, startTrial, trialDays } = request;

    // Get feature flag
    const featureFlag = await this.featureFlagRepository.findOne({
      where: { key: featureKey, enabled: true },
    });

    if (!featureFlag) {
      throw new ForbiddenException('Feature not found or disabled');
    }

    // Check if organization can enable this feature
    const subscriptionCheck = await this.checkSubscriptionAccess(organizationId, featureFlag);
    if (!subscriptionCheck.allowed && !startTrial) {
      throw new ForbiddenException(subscriptionCheck.reason);
    }

    // Get or create organization feature
    let orgFeature = await this.organizationFeatureRepository.findOne({
      where: { organizationId, featureFlagId: featureFlag.id },
    });

    if (!orgFeature) {
      orgFeature = this.organizationFeatureRepository.create({
        organizationId,
        featureFlagId: featureFlag.id,
      });
    }

    // Configure feature
    const now = new Date();
    
    if (startTrial && trialDays) {
      orgFeature.status = FeatureStatus.TRIAL;
      orgFeature.trialStartedAt = now;
      orgFeature.trialEndsAt = new Date(now.getTime() + (trialDays * 24 * 60 * 60 * 1000));
    } else {
      orgFeature.status = FeatureStatus.ENABLED;
      orgFeature.enabledAt = now;
    }

    orgFeature.value = value;
    orgFeature.configuration = configuration;
    orgFeature.enabledBy = enabledBy;
    orgFeature.expiresAt = expiresAt;

    const savedFeature = await this.organizationFeatureRepository.save(orgFeature);

    this.logger.log(`Enabled feature ${featureKey} for organization ${organizationId}`);

    return savedFeature;
  }

  async disableFeature(organizationId: string, featureKey: string, disabledBy?: string): Promise<OrganizationFeature> {
    const featureFlag = await this.featureFlagRepository.findOne({
      where: { key: featureKey },
    });

    if (!featureFlag) {
      throw new ForbiddenException('Feature not found');
    }

    const orgFeature = await this.organizationFeatureRepository.findOne({
      where: { organizationId, featureFlagId: featureFlag.id },
    });

    if (!orgFeature) {
      throw new ForbiddenException('Feature not enabled for organization');
    }

    orgFeature.status = FeatureStatus.DISABLED;
    orgFeature.disabledAt = new Date();
    orgFeature.disabledBy = disabledBy;

    const savedFeature = await this.organizationFeatureRepository.save(orgFeature);

    this.logger.log(`Disabled feature ${featureKey} for organization ${organizationId}`);

    return savedFeature;
  }

  async getOrganizationFeatures(organizationId: string): Promise<OrganizationFeature[]> {
    return this.organizationFeatureRepository.find({
      where: { organizationId },
      relations: ['featureFlag'],
      order: { createdAt: 'DESC' },
    });
  }

  async getAvailableFeatures(organizationId: string): Promise<FeatureFlag[]> {
    // Get subscription to determine available features
    const subscription = await this.subscriptionService.getOrganizationSubscription(organizationId);
    
    const query = this.featureFlagRepository.createQueryBuilder('feature')
      .where('feature.enabled = :enabled', { enabled: true });

    // Filter by plan constraints if subscription exists
    if (subscription?.plan) {
      query.andWhere(
        '(feature.constraints IS NULL OR feature.constraints->>\'minPlanType\' IS NULL OR feature.constraints->>\'minPlanType\' <= :planType)',
        { planType: subscription.plan.type }
      );
    }

    return query.orderBy('feature.category', 'ASC')
                .addOrderBy('feature.name', 'ASC')
                .getMany();
  }

  private async checkSubscriptionAccess(organizationId: string, featureFlag: FeatureFlag): Promise<{
    allowed: boolean;
    inheritFromPlan: boolean;
    reason?: string;
    metadata?: any;
  }> {
    // Get subscription
    const subscription = await this.subscriptionService.getOrganizationSubscription(organizationId);
    
    if (!subscription) {
      // No subscription - only allow free features
      if (featureFlag.category === FeatureCategory.CORE) {
        return { allowed: true, inheritFromPlan: true };
      }
      
      return {
        allowed: false,
        inheritFromPlan: false,
        reason: 'Subscription required',
        metadata: {
          planRequired: 'starter',
          upgradeUrl: '/billing/upgrade',
        },
      };
    }

    // Check plan constraints
    if (featureFlag.constraints?.minPlanType) {
      const planHierarchy = ['free', 'starter', 'professional', 'enterprise'];
      const requiredPlanIndex = planHierarchy.indexOf(featureFlag.constraints.minPlanType);
      const currentPlanIndex = planHierarchy.indexOf(subscription.plan.type);
      
      if (currentPlanIndex < requiredPlanIndex) {
        return {
          allowed: false,
          inheritFromPlan: false,
          reason: `${featureFlag.constraints.minPlanType} plan required`,
          metadata: {
            planRequired: featureFlag.constraints.minPlanType,
            upgradeUrl: '/billing/upgrade',
          },
        };
      }
    }

    // Check if subscription is active
    if (!subscription.isActive) {
      return {
        allowed: false,
        inheritFromPlan: false,
        reason: 'Active subscription required',
        metadata: {
          upgradeUrl: '/billing/reactivate',
        },
      };
    }

    return { allowed: true, inheritFromPlan: true };
  }

  private async checkUsageLimits(organizationId: string, featureKey: string, context?: Record<string, any>): Promise<{
    allowed: boolean;
    reason?: string;
    metadata?: any;
    usageInfo?: {
      current: number;
      limit: number;
      percentage: number;
    };
  }> {
    // Get usage limits for this feature
    const usageLimits = await this.usageLimitRepository.find({
      where: [
        { organizationId, limitKey: featureKey, enabled: true },
        { organizationId: null, limitKey: featureKey, enabled: true }, // Global limits
      ],
    });

    for (const limit of usageLimits) {
      if (limit.isExceeded && !limit.isInGracePeriod) {
        const action = limit.actionOnExceed;
        
        if (action === LimitAction.BLOCK) {
          return {
            allowed: false,
            reason: `Usage limit exceeded: ${limit.name}`,
            metadata: {
              limitType: limit.type,
              maxValue: limit.maxValue,
              currentValue: limit.currentValue,
              resetTime: limit.nextResetAt,
              upgradeUrl: limit.configuration?.upgradeUrl,
            },
            usageInfo: {
              current: limit.currentValue,
              limit: limit.maxValue,
              percentage: limit.usagePercentage,
            },
          };
        }
      }

      // Return usage info even if not exceeded
      if (usageLimits.length > 0) {
        const primaryLimit = usageLimits[0];
        return {
          allowed: true,
          usageInfo: {
            current: primaryLimit.currentValue,
            limit: primaryLimit.maxValue,
            percentage: primaryLimit.usagePercentage,
          },
        };
      }
    }

    return { allowed: true };
  }
}
