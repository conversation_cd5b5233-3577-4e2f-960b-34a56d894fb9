import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactsService } from '../../contacts/contacts.service';
import { Contact } from '../../contacts/entities/contact.entity';
import { Company } from '../../companies/entities/company.entity';
import { User } from '../../users/entities/user.entity';
import { CreateContactDto } from '../../contacts/dto/create-contact.dto';
import { UpdateContactDto } from '../../contacts/dto/update-contact.dto';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('ContactsService', () => {
  let service: ContactsService;
  let contactRepository: Repository<Contact>;
  let companyRepository: Repository<Company>;
  let userRepository: Repository<User>;

  const mockContact = {
    id: '1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    title: 'Software Engineer',
    leadStatus: 'new',
    leadSource: 'website',
    tags: ['developer', 'tech'],
    notes: 'Interested in our product',
    orgId: 'org-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCompany = {
    id: 'company-1',
    name: 'Tech Corp',
    orgId: 'org-1',
  };

  const mockUser = {
    id: 'user-1',
    firstName: 'Jane',
    lastName: 'Manager',
    orgId: 'org-1',
  };

  const mockContactRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(),
      getMany: jest.fn(),
    })),
  };

  const mockCompanyRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContactsService,
        {
          provide: getRepositoryToken(Contact),
          useValue: mockContactRepository,
        },
        {
          provide: getRepositoryToken(Company),
          useValue: mockCompanyRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<ContactsService>(ContactsService);
    contactRepository = module.get<Repository<Contact>>(getRepositoryToken(Contact));
    companyRepository = module.get<Repository<Company>>(getRepositoryToken(Company));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createContactDto: CreateContactDto = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+**********',
      title: 'Software Engineer',
      leadStatus: 'new',
      leadSource: 'website',
      companyId: 'company-1',
      assignedToId: 'user-1',
      tags: ['developer'],
      notes: 'Test contact',
    };

    it('should create a contact successfully', async () => {
      mockCompanyRepository.findOne.mockResolvedValue(mockCompany);
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockContactRepository.create.mockReturnValue(mockContact);
      mockContactRepository.save.mockResolvedValue(mockContact);

      const result = await service.create(createContactDto, 'org-1');

      expect(mockCompanyRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'company-1', orgId: 'org-1' },
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user-1', orgId: 'org-1' },
      });
      expect(mockContactRepository.create).toHaveBeenCalledWith({
        ...createContactDto,
        company: mockCompany,
        assignedTo: mockUser,
        orgId: 'org-1',
      });
      expect(mockContactRepository.save).toHaveBeenCalledWith(mockContact);
      expect(result).toEqual(mockContact);
    });

    it('should throw NotFoundException when company not found', async () => {
      mockCompanyRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createContactDto, 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw NotFoundException when assigned user not found', async () => {
      mockCompanyRepository.findOne.mockResolvedValue(mockCompany);
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createContactDto, 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should create contact without company and assignedTo', async () => {
      const dtoWithoutRefs = { ...createContactDto };
      delete dtoWithoutRefs.companyId;
      delete dtoWithoutRefs.assignedToId;

      mockContactRepository.create.mockReturnValue(mockContact);
      mockContactRepository.save.mockResolvedValue(mockContact);

      const result = await service.create(dtoWithoutRefs, 'org-1');

      expect(mockCompanyRepository.findOne).not.toHaveBeenCalled();
      expect(mockUserRepository.findOne).not.toHaveBeenCalled();
      expect(result).toEqual(mockContact);
    });
  });

  describe('findAll', () => {
    const queryOptions = {
      page: 1,
      limit: 10,
      search: 'john',
      leadStatus: 'new',
      sortBy: 'firstName',
      sortOrder: 'ASC' as const,
    };

    it('should return paginated contacts', async () => {
      const mockQueryBuilder = mockContactRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockContact], 1]);

      const result = await service.findAll('org-1', queryOptions);

      expect(mockContactRepository.createQueryBuilder).toHaveBeenCalledWith('contact');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('contact.orgId = :orgId', { orgId: 'org-1' });
      expect(result).toEqual({
        contacts: [mockContact],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should apply search filter', async () => {
      const mockQueryBuilder = mockContactRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll('org-1', queryOptions);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(contact.firstName ILIKE :search OR contact.lastName ILIKE :search OR contact.email ILIKE :search)',
        { search: '%john%' },
      );
    });

    it('should apply lead status filter', async () => {
      const mockQueryBuilder = mockContactRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll('org-1', queryOptions);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'contact.leadStatus = :leadStatus',
        { leadStatus: 'new' },
      );
    });
  });

  describe('findOne', () => {
    it('should return a contact by id', async () => {
      mockContactRepository.findOne.mockResolvedValue(mockContact);

      const result = await service.findOne('1', 'org-1');

      expect(mockContactRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', orgId: 'org-1' },
        relations: ['company', 'assignedTo', 'activities', 'deals'],
      });
      expect(result).toEqual(mockContact);
    });

    it('should throw NotFoundException when contact not found', async () => {
      mockContactRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('999', 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    const updateContactDto: UpdateContactDto = {
      firstName: 'Jane',
      leadStatus: 'qualified',
    };

    it('should update a contact successfully', async () => {
      const updatedContact = { ...mockContact, ...updateContactDto };
      mockContactRepository.findOne.mockResolvedValue(mockContact);
      mockContactRepository.save.mockResolvedValue(updatedContact);

      const result = await service.update('1', updateContactDto, 'org-1');

      expect(mockContactRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', orgId: 'org-1' },
      });
      expect(mockContactRepository.save).toHaveBeenCalledWith({
        ...mockContact,
        ...updateContactDto,
      });
      expect(result).toEqual(updatedContact);
    });

    it('should throw NotFoundException when contact not found', async () => {
      mockContactRepository.findOne.mockResolvedValue(null);

      await expect(service.update('999', updateContactDto, 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('remove', () => {
    it('should delete a contact successfully', async () => {
      mockContactRepository.findOne.mockResolvedValue(mockContact);
      mockContactRepository.delete.mockResolvedValue({ affected: 1 });

      await service.remove('1', 'org-1');

      expect(mockContactRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', orgId: 'org-1' },
      });
      expect(mockContactRepository.delete).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException when contact not found', async () => {
      mockContactRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('999', 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getStats', () => {
    it('should return contact statistics', async () => {
      const mockStats = {
        total: 100,
        byStatus: { new: 30, qualified: 40, customer: 30 },
        bySource: { website: 50, referral: 30, social: 20 },
        recentlyCreated: 10,
      };

      const mockQueryBuilder = mockContactRepository.createQueryBuilder();
      mockQueryBuilder.getMany.mockResolvedValue([
        { leadStatus: 'new', leadSource: 'website', createdAt: new Date() },
        { leadStatus: 'qualified', leadSource: 'referral', createdAt: new Date() },
      ]);

      // Mock the count query
      jest.spyOn(service as any, 'getContactCount').mockResolvedValue(100);
      jest.spyOn(service as any, 'getRecentContactsCount').mockResolvedValue(10);

      const result = await service.getStats('org-1');

      expect(result).toHaveProperty('total');
      expect(result).toHaveProperty('byStatus');
      expect(result).toHaveProperty('bySource');
      expect(result).toHaveProperty('recentlyCreated');
    });
  });
});
