# Frontend Development Guide

## 🏗️ Project Structure

### Directory Organization
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Auth route group
│   ├── dashboard/         # Dashboard pages
│   ├── contacts/          # Contact management
│   ├── companies/         # Company management
│   ├── deals/             # Deal management
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── auth/              # Authentication components
│   ├── common/            # Shared components
│   ├── layout/            # Layout components
│   └── providers/         # Context providers
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and configurations
│   ├── api.ts             # API client
│   ├── keycloak.ts        # Keycloak configuration
│   ├── performance.ts     # Performance monitoring
│   └── accessibility.ts   # A11y utilities
├── types/                 # TypeScript definitions
└── utils/                 # Helper functions
```

## 🔧 Development Workflow

### 1. Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd onecrm/frontend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Start development server
npm run dev
```

### 2. Environment Configuration

Create `.env.local` with required variables:
```env
NEXT_PUBLIC_KONG_GATEWAY_URL=http://localhost:8000
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
```

### 3. Code Quality Standards

#### TypeScript
- Use strict mode (`strict: true`)
- Define explicit types for all props and state
- Use Zod schemas for runtime validation
- Avoid `any` type - use `unknown` instead

#### React Best Practices
```typescript
// ✅ Good: Functional component with proper typing
interface UserCardProps {
  user: UserProfile;
  onEdit: (id: string) => void;
}

export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  const handleEdit = useCallback(() => {
    onEdit(user.id);
  }, [user.id, onEdit]);

  return (
    <Card>
      <CardContent>
        <Typography variant="h6">{user.name}</Typography>
        <Button onClick={handleEdit}>Edit</Button>
      </CardContent>
    </Card>
  );
};

// ❌ Bad: Missing types and optimization
export const UserCard = ({ user, onEdit }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6">{user.name}</Typography>
        <Button onClick={() => onEdit(user.id)}>Edit</Button>
      </CardContent>
    </Card>
  );
};
```

#### State Management
```typescript
// ✅ Good: Use SWR for server state
const { data: contacts, error, mutate } = useSWR('/api/contacts', fetcher);

// ✅ Good: Use React state for UI state
const [isModalOpen, setIsModalOpen] = useState(false);

// ✅ Good: Use context for global app state
const { user, isAuthenticated } = useAuth();
```

### 4. Component Development

#### Component Structure
```typescript
// ComponentName.tsx
import React, { useState, useCallback } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { ComponentNameProps } from './types';

export const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2,
  onAction,
}) => {
  // Hooks
  const [localState, setLocalState] = useState(false);
  
  // Callbacks
  const handleAction = useCallback(() => {
    onAction(prop1);
  }, [prop1, onAction]);

  // Render
  return (
    <Box>
      <Typography variant="h6">{prop2}</Typography>
      <Button onClick={handleAction}>Action</Button>
    </Box>
  );
};

// types.ts
export interface ComponentNameProps {
  prop1: string;
  prop2: string;
  onAction: (value: string) => void;
}
```

#### Testing Components
```typescript
// ComponentName.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ComponentName } from './ComponentName';

describe('ComponentName', () => {
  const mockOnAction = jest.fn();
  
  const defaultProps = {
    prop1: 'test1',
    prop2: 'test2',
    onAction: mockOnAction,
  };

  beforeEach(() => {
    mockOnAction.mockClear();
  });

  it('renders correctly', () => {
    render(<ComponentName {...defaultProps} />);
    expect(screen.getByText('test2')).toBeInTheDocument();
  });

  it('calls onAction when button is clicked', () => {
    render(<ComponentName {...defaultProps} />);
    fireEvent.click(screen.getByText('Action'));
    expect(mockOnAction).toHaveBeenCalledWith('test1');
  });
});
```

### 5. API Integration

#### Using the API Client
```typescript
import { apiClient, API_ENDPOINTS } from '@/lib/api';
import { ContactSchema } from '@onecrm/types';

// ✅ Good: Type-safe API calls
const createContact = async (contactData: CreateContactRequest) => {
  try {
    const response = await apiClient.post(API_ENDPOINTS.CONTACTS.BASE, contactData);
    return ContactSchema.parse(response.data);
  } catch (error) {
    console.error('Failed to create contact:', error);
    throw error;
  }
};

// ✅ Good: Using SWR for data fetching
const useContacts = (filters?: ContactFilters) => {
  const queryString = filters ? buildQueryString(filters) : '';
  const { data, error, mutate } = useSWR(
    `${API_ENDPOINTS.CONTACTS.BASE}${queryString}`,
    fetcher
  );

  return {
    contacts: data?.items || [],
    isLoading: !error && !data,
    error,
    mutate,
  };
};
```

### 6. Form Handling

#### Using React Hook Form with Zod
```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ContactFormSchema } from '@onecrm/types';

const ContactForm: React.FC<ContactFormProps> = ({ onSubmit }) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
    },
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="firstName"
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            label="First Name"
            error={!!errors.firstName}
            helperText={errors.firstName?.message}
          />
        )}
      />
      <Button type="submit" disabled={isSubmitting}>
        Submit
      </Button>
    </form>
  );
};
```

### 7. Performance Optimization

#### Code Splitting
```typescript
// ✅ Good: Route-based code splitting
const ContactsPage = dynamic(() => import('./ContactsPage'), {
  loading: () => <LoadingSpinner />,
});

// ✅ Good: Component-based code splitting
const HeavyComponent = lazy(() => import('./HeavyComponent'));
```

#### Memoization
```typescript
// ✅ Good: Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// ✅ Good: Memoize callback functions
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

### 8. Accessibility Guidelines

#### ARIA Attributes
```typescript
// ✅ Good: Proper ARIA attributes
<button
  aria-label="Delete contact"
  aria-describedby="delete-help"
  onClick={handleDelete}
>
  <DeleteIcon />
</button>
<div id="delete-help" className="sr-only">
  This action cannot be undone
</div>
```

#### Keyboard Navigation
```typescript
// ✅ Good: Keyboard support
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    handleClick();
  }
};
```

### 9. Error Handling

#### Error Boundaries
```typescript
// ✅ Good: Component-level error boundary
<ErrorBoundary fallback={<ErrorFallback />}>
  <ContactsList />
</ErrorBoundary>
```

#### API Error Handling
```typescript
// ✅ Good: Comprehensive error handling
const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    // Handle authentication error
    redirectToLogin();
  } else if (error.response?.status >= 500) {
    // Handle server error
    showErrorNotification('Server error occurred');
  } else {
    // Handle other errors
    showErrorNotification(extractErrorMessage(error));
  }
};
```

### 10. Testing Strategy

#### Unit Tests
- Test component rendering
- Test user interactions
- Test custom hooks
- Test utility functions

#### Integration Tests
- Test API integration
- Test form submissions
- Test navigation flows

#### E2E Tests
- Test critical user journeys
- Test authentication flows
- Test data operations

### 11. Deployment Checklist

Before deploying:
- [ ] All tests pass (`npm run test`)
- [ ] TypeScript checks pass (`npm run typecheck`)
- [ ] Linting passes (`npm run lint`)
- [ ] Build succeeds (`npm run build`)
- [ ] Environment variables configured
- [ ] Performance metrics acceptable
- [ ] Accessibility audit passed

### 12. Debugging Tips

#### Development Tools
- React Developer Tools
- Redux DevTools (if using Redux)
- Network tab for API calls
- Performance tab for optimization

#### Common Issues
- **Hydration errors**: Check for client/server rendering differences
- **Memory leaks**: Clean up event listeners and subscriptions
- **Performance issues**: Use React Profiler to identify bottlenecks

### 13. Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Material-UI Documentation](https://mui.com/)
- [React Hook Form](https://react-hook-form.com/)
- [SWR Documentation](https://swr.vercel.app/)
- [Testing Library](https://testing-library.com/)

---

For questions or clarifications, please refer to the team documentation or reach out to the development team.
