import { test, expect } from '@playwright/test'

test.describe('Phase 2: Enhanced Components', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/component-test')
  })

  test.describe('Enhanced Button System', () => {
    test('should render all button variants correctly', async ({ page }) => {
      // Test button variants
      await expect(page.getByTestId('button-default')).toBeVisible()
      await expect(page.getByTestId('button-destructive')).toBeVisible()
      await expect(page.getByTestId('button-outline')).toBeVisible()
      
      // Check variant classes
      await expect(page.getByTestId('button-default')).toHaveClass(/bg-primary/)
      await expect(page.getByTestId('button-destructive')).toHaveClass(/bg-destructive/)
      await expect(page.getByTestId('button-outline')).toHaveClass(/border/)
    })

    test('should handle loading states properly', async ({ page }) => {
      const loadingButton = page.getByTestId('button-loading')
      
      // Click to toggle loading
      await loadingButton.click()
      
      // Should show loading state
      await expect(loadingButton).toBeDisabled()
      await expect(loadingButton.locator('svg')).toBeVisible() // Loading spinner
      
      // Click again to stop loading
      await loadingButton.click()
      await expect(loadingButton).toBeEnabled()
    })

    test('should support different sizes', async ({ page }) => {
      const smallButton = page.getByTestId('button-small')
      const largeButton = page.getByTestId('button-large')
      
      // Check size classes
      await expect(smallButton).toHaveClass(/h-8/)
      await expect(largeButton).toHaveClass(/h-10/)
    })

    test('should handle disabled state correctly', async ({ page }) => {
      const disabledButton = page.getByTestId('button-disabled')
      
      await expect(disabledButton).toBeDisabled()
      await expect(disabledButton).toHaveClass(/opacity-50/)
      
      // Should not trigger click events
      let clicked = false
      await page.exposeFunction('buttonClicked', () => { clicked = true })
      
      await disabledButton.click({ force: true })
      expect(clicked).toBe(false)
    })

    test('should be keyboard accessible', async ({ page }) => {
      const button = page.getByTestId('button-default')
      
      // Focus with tab
      await page.keyboard.press('Tab')
      await expect(button).toBeFocused()
      
      // Activate with Enter
      await page.keyboard.press('Enter')
      await expect(page.getByText('Button clicked!')).toBeVisible()
      
      // Activate with Space
      await page.keyboard.press('Space')
      await expect(page.getByText('Button clicked!')).toBeVisible()
    })
  })

  test.describe('Enhanced Form System', () => {
    test('should render form with all field types', async ({ page }) => {
      const form = page.getByTestId('enhanced-form')
      await expect(form).toBeVisible()

      // Check all form fields
      await expect(page.getByLabel('Name')).toBeVisible()
      await expect(page.getByLabel('Email')).toBeVisible()
      await expect(page.getByLabel('Description')).toBeVisible()
      await expect(page.getByLabel('Category')).toBeVisible()
      await expect(page.getByLabel('I agree to the terms and conditions')).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      const submitButton = page.getByRole('button', { name: /submit form/i })
      
      // Try to submit empty form
      await submitButton.click()
      
      // Should show validation errors
      await expect(page.getByText('Name is required')).toBeVisible()
      await expect(page.getByText('Email is required')).toBeVisible()
      await expect(page.getByText('Category is required')).toBeVisible()
    })

    test('should validate email format', async ({ page }) => {
      const emailInput = page.getByLabel('Email')
      const submitButton = page.getByRole('button', { name: /submit form/i })
      
      // Enter invalid email
      await emailInput.fill('invalid-email')
      await submitButton.click()
      
      // Should show email validation error
      await expect(page.getByText('Invalid email format')).toBeVisible()
    })

    test('should handle form submission successfully', async ({ page }) => {
      // Fill out form with valid data
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Description').fill('Test description')
      
      // Select category
      await page.getByLabel('Category').click()
      await page.getByText('Business').click()
      
      // Check agreement
      await page.getByLabel('I agree to the terms and conditions').check()
      
      // Submit form
      const submitButton = page.getByRole('button', { name: /submit form/i })
      await submitButton.click()
      
      // Should show loading state
      await expect(submitButton).toBeDisabled()
      await expect(submitButton).toContainText('Submitting')
      
      // Should show success message (with timeout for async operation)
      await expect(page.getByText('Form submitted successfully!')).toBeVisible({ timeout: 5000 })
    })

    test('should handle form reset', async ({ page }) => {
      // Fill out form
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      
      // Reset form
      await page.getByRole('button', { name: /reset/i }).click()
      
      // Page should reload, so fields should be empty
      await expect(page.getByLabel('Name')).toHaveValue('')
      await expect(page.getByLabel('Email')).toHaveValue('')
    })

    test('should show validation status', async ({ page }) => {
      // Initially should show errors
      const submitButton = page.getByRole('button', { name: /submit form/i })
      await submitButton.click()
      
      await expect(page.getByText(/error\(s\) found/i)).toBeVisible()
      
      // Fill required fields
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Category').click()
      await page.getByText('Business').click()
      await page.getByLabel('I agree to the terms and conditions').check()
      
      // Should show form is valid
      await expect(page.getByText('Form is valid')).toBeVisible()
    })
  })

  test.describe('Form Accessibility', () => {
    test('should have proper ARIA attributes', async ({ page }) => {
      const nameInput = page.getByLabel('Name')
      const emailInput = page.getByLabel('Email')
      
      // Check required attributes
      await expect(nameInput).toHaveAttribute('required')
      await expect(emailInput).toHaveAttribute('required')
      
      // Check ARIA attributes after validation
      const submitButton = page.getByRole('button', { name: /submit form/i })
      await submitButton.click()
      
      // Should have aria-invalid when there are errors
      await expect(nameInput).toHaveAttribute('aria-invalid', 'true')
      await expect(emailInput).toHaveAttribute('aria-invalid', 'true')
    })

    test('should associate errors with fields', async ({ page }) => {
      const submitButton = page.getByRole('button', { name: /submit form/i })
      await submitButton.click()
      
      const nameInput = page.getByLabel('Name')
      const errorId = await nameInput.getAttribute('aria-describedby')
      
      if (errorId) {
        const errorElement = page.locator(`#${errorId}`)
        await expect(errorElement).toBeVisible()
        await expect(errorElement).toContainText('required')
      }
    })

    test('should support keyboard navigation', async ({ page }) => {
      // Tab through form fields
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Name')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Email')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Description')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Category')).toBeFocused()
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      
      // Components should still be visible and functional
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
      await expect(page.getByTestId('button-default')).toBeVisible()
      
      // Form should be usable on mobile
      const nameInput = page.getByLabel('Name')
      await nameInput.tap()
      await expect(nameInput).toBeFocused()
      
      await nameInput.fill('Mobile Test')
      await expect(nameInput).toHaveValue('Mobile Test')
    })

    test('should adapt to different screen sizes', async ({ page }) => {
      // Test tablet size
      await page.setViewportSize({ width: 768, height: 1024 })
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
      
      // Test desktop size
      await page.setViewportSize({ width: 1920, height: 1080 })
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
      
      // Test small mobile
      await page.setViewportSize({ width: 320, height: 568 })
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle submission errors gracefully', async ({ page }) => {
      // Fill form with valid data
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Category').click()
      await page.getByText('Business').click()
      await page.getByLabel('I agree to the terms and conditions').check()
      
      // Submit form multiple times to trigger random error
      let errorShown = false
      for (let i = 0; i < 5; i++) {
        await page.getByRole('button', { name: /submit form/i }).click()
        
        // Wait for either success or error
        try {
          await page.getByText('Submission failed').waitFor({ timeout: 3000 })
          errorShown = true
          break
        } catch {
          // If no error, wait for success and try again
          await page.getByText('Form submitted successfully!').waitFor({ timeout: 3000 })
          // Wait for success message to disappear
          await page.getByText('Form submitted successfully!').waitFor({ state: 'hidden', timeout: 4000 })
        }
      }
      
      // At least one attempt should show error handling
      if (errorShown) {
        await expect(page.getByText('Submission failed')).toBeVisible()
      }
    })

    test('should recover from errors', async ({ page }) => {
      // This test verifies that the form can recover from errors
      // and successfully submit on retry
      
      await page.getByLabel('Name').fill('Recovery Test')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Category').click()
      await page.getByText('Business').click()
      await page.getByLabel('I agree to the terms and conditions').check()
      
      // Keep trying until we get a success
      let attempts = 0
      while (attempts < 10) {
        await page.getByRole('button', { name: /submit form/i }).click()
        
        try {
          await page.getByText('Form submitted successfully!').waitFor({ timeout: 3000 })
          break
        } catch {
          // If error, wait for it to clear and try again
          try {
            await page.getByText('Submission failed').waitFor({ timeout: 1000 })
          } catch {
            // No error message, continue
          }
        }
        attempts++
      }
      
      // Should eventually succeed
      await expect(page.getByText('Form submitted successfully!')).toBeVisible()
    })
  })

  test.describe('Performance', () => {
    test('should load components quickly', async ({ page }) => {
      const startTime = Date.now()
      
      // Navigate to component test page
      await page.goto('/component-test')
      
      // Wait for main components to be visible
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
      await expect(page.getByTestId('button-default')).toBeVisible()
      
      const loadTime = Date.now() - startTime
      
      // Should load within reasonable time (5 seconds)
      expect(loadTime).toBeLessThan(5000)
    })

    test('should handle rapid interactions', async ({ page }) => {
      const button = page.getByTestId('button-default')
      
      // Rapidly click button multiple times
      for (let i = 0; i < 10; i++) {
        await button.click()
        await page.waitForTimeout(50)
      }
      
      // Should still be responsive
      await expect(button).toBeVisible()
      await expect(button).toBeEnabled()
    })
  })
})
