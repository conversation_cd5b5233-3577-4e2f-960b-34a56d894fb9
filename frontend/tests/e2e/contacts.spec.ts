import { test, expect, Page } from '@playwright/test';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'testpassword123',
};

const testContact = {
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  title: 'Software Engineer',
  company: 'Tech Corp',
  leadStatus: 'New',
  leadSource: 'Website',
  notes: 'Test contact created via E2E test',
};

test.describe('Contacts Management', () => {
  let page: Page;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    
    // Login before running tests
    await login(page);
  });

  test.afterAll(async () => {
    await page.close();
  });

  async function login(page: Page) {
    await page.goto('/login');
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('/');
    await expect(page.locator('[data-testid="dashboard-title"]')).toBeVisible();
  }

  test('should navigate to contacts page', async () => {
    await page.goto('/contacts');
    
    // Verify page title and main elements
    await expect(page.locator('h1')).toContainText('Contacts');
    await expect(page.locator('[data-testid="add-contact-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-input"]')).toBeVisible();
  });

  test('should create a new contact', async () => {
    await page.goto('/contacts');
    
    // Click add contact button
    await page.click('[data-testid="add-contact-button"]');
    
    // Wait for form dialog to open
    await expect(page.locator('[data-testid="contact-form-dialog"]')).toBeVisible();
    
    // Fill contact form
    await page.fill('[data-testid="firstName-input"]', testContact.firstName);
    await page.fill('[data-testid="lastName-input"]', testContact.lastName);
    await page.fill('[data-testid="email-input"]', testContact.email);
    await page.fill('[data-testid="phone-input"]', testContact.phone);
    await page.fill('[data-testid="title-input"]', testContact.title);
    await page.fill('[data-testid="notes-input"]', testContact.notes);
    
    // Select lead status
    await page.click('[data-testid="leadStatus-select"]');
    await page.click(`[data-value="${testContact.leadStatus.toLowerCase()}"]`);
    
    // Select lead source
    await page.click('[data-testid="leadSource-select"]');
    await page.click(`[data-value="website"]`);
    
    // Submit form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for form to close and success message
    await expect(page.locator('[data-testid="contact-form-dialog"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify contact appears in the list
    await expect(page.locator(`[data-testid="contact-${testContact.firstName}-${testContact.lastName}"]`)).toBeVisible();
  });

  test('should search for contacts', async () => {
    await page.goto('/contacts');
    
    // Search for the created contact
    await page.fill('[data-testid="search-input"]', testContact.firstName);
    
    // Wait for search results
    await page.waitForTimeout(500); // Debounce delay
    
    // Verify search results
    await expect(page.locator(`[data-testid="contact-${testContact.firstName}-${testContact.lastName}"]`)).toBeVisible();
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '');
    await page.waitForTimeout(500);
  });

  test('should filter contacts by lead status', async () => {
    await page.goto('/contacts');
    
    // Open filters
    await page.click('[data-testid="filters-button"]');
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    
    // Select lead status filter
    await page.click('[data-testid="leadStatus-filter"]');
    await page.click(`[data-value="new"]`);
    
    // Apply filters
    await page.click('[data-testid="apply-filters-button"]');
    
    // Verify filtered results
    await expect(page.locator('[data-testid="contacts-table"]')).toBeVisible();
    
    // Clear filters
    await page.click('[data-testid="clear-filters-button"]');
  });

  test('should edit a contact', async () => {
    await page.goto('/contacts');
    
    // Find and click edit button for the test contact
    const contactRow = page.locator(`[data-testid="contact-${testContact.firstName}-${testContact.lastName}"]`);
    await contactRow.locator('[data-testid="more-actions-button"]').click();
    await page.click('[data-testid="edit-contact-action"]');
    
    // Wait for edit form to open
    await expect(page.locator('[data-testid="contact-form-dialog"]')).toBeVisible();
    
    // Update contact information
    const updatedTitle = 'Senior Software Engineer';
    await page.fill('[data-testid="title-input"]', updatedTitle);
    
    // Update lead status
    await page.click('[data-testid="leadStatus-select"]');
    await page.click(`[data-value="qualified"]`);
    
    // Submit form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for form to close and success message
    await expect(page.locator('[data-testid="contact-form-dialog"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify updated information
    await expect(contactRow.locator('[data-testid="contact-title"]')).toContainText(updatedTitle);
  });

  test('should view contact details', async () => {
    await page.goto('/contacts');
    
    // Click on contact to view details
    const contactRow = page.locator(`[data-testid="contact-${testContact.firstName}-${testContact.lastName}"]`);
    await contactRow.click();
    
    // Wait for details view to open
    await expect(page.locator('[data-testid="contact-details-dialog"]')).toBeVisible();
    
    // Verify contact details are displayed
    await expect(page.locator('[data-testid="contact-name"]')).toContainText(`${testContact.firstName} ${testContact.lastName}`);
    await expect(page.locator('[data-testid="contact-email"]')).toContainText(testContact.email);
    await expect(page.locator('[data-testid="contact-phone"]')).toContainText(testContact.phone);
    
    // Close details view
    await page.click('[data-testid="close-details-button"]');
    await expect(page.locator('[data-testid="contact-details-dialog"]')).not.toBeVisible();
  });

  test('should export contacts', async () => {
    await page.goto('/contacts');
    
    // Click export button
    await page.click('[data-testid="more-actions-button"]');
    await page.click('[data-testid="export-contacts-action"]');
    
    // Wait for download to start
    const downloadPromise = page.waitForEvent('download');
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toContain('contacts');
    expect(download.suggestedFilename()).toContain('.csv');
  });

  test('should handle bulk operations', async () => {
    await page.goto('/contacts');
    
    // Select multiple contacts
    await page.check('[data-testid="select-all-checkbox"]');
    
    // Verify bulk actions are available
    await expect(page.locator('[data-testid="bulk-actions-toolbar"]')).toBeVisible();
    
    // Test bulk delete (cancel to avoid actually deleting)
    await page.click('[data-testid="bulk-delete-button"]');
    await expect(page.locator('[data-testid="confirm-dialog"]')).toBeVisible();
    await page.click('[data-testid="cancel-button"]');
    
    // Deselect all
    await page.uncheck('[data-testid="select-all-checkbox"]');
    await expect(page.locator('[data-testid="bulk-actions-toolbar"]')).not.toBeVisible();
  });

  test('should validate form inputs', async () => {
    await page.goto('/contacts');
    
    // Click add contact button
    await page.click('[data-testid="add-contact-button"]');
    await expect(page.locator('[data-testid="contact-form-dialog"]')).toBeVisible();
    
    // Try to submit empty form
    await page.click('[data-testid="submit-button"]');
    
    // Verify validation messages (at least email should be required)
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    
    // Test invalid email format
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.click('[data-testid="submit-button"]');
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Invalid email');
    
    // Close form
    await page.click('[data-testid="cancel-button"]');
  });

  test('should handle pagination', async () => {
    await page.goto('/contacts');
    
    // Check if pagination is visible (assuming there are enough contacts)
    const pagination = page.locator('[data-testid="pagination"]');
    
    if (await pagination.isVisible()) {
      // Test page navigation
      const nextButton = page.locator('[data-testid="next-page-button"]');
      
      if (await nextButton.isEnabled()) {
        await nextButton.click();
        
        // Verify URL or page content changed
        await expect(page.url()).toContain('page=2');
        
        // Go back to first page
        await page.click('[data-testid="previous-page-button"]');
        await expect(page.url()).toContain('page=1');
      }
    }
  });

  test('should work on mobile viewport', async () => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/contacts');
    
    // Verify mobile navigation is visible
    await expect(page.locator('[data-testid="mobile-navigation"]')).toBeVisible();
    
    // Verify floating action button is visible
    await expect(page.locator('[data-testid="mobile-fab"]')).toBeVisible();
    
    // Test mobile contact creation
    await page.click('[data-testid="mobile-fab"]');
    await expect(page.locator('[data-testid="contact-form-dialog"]')).toBeVisible();
    
    // Close form
    await page.click('[data-testid="cancel-button"]');
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should delete a contact', async () => {
    await page.goto('/contacts');
    
    // Find the test contact and delete it
    const contactRow = page.locator(`[data-testid="contact-${testContact.firstName}-${testContact.lastName}"]`);
    await contactRow.locator('[data-testid="more-actions-button"]').click();
    await page.click('[data-testid="delete-contact-action"]');
    
    // Confirm deletion
    await expect(page.locator('[data-testid="confirm-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-button"]');
    
    // Verify contact is removed
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(contactRow).not.toBeVisible();
  });
});
