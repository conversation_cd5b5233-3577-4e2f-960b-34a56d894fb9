import { test, expect, Page } from '@playwright/test';

const testUser = {
  email: '<EMAIL>',
  password: 'testpassword123',
};

const testDeal = {
  title: 'Enterprise Software Deal',
  amount: '50000',
  probability: '75',
  expectedCloseDate: '2024-12-31',
  description: 'Large enterprise software deal for E2E testing',
};

test.describe('Deals Pipeline Management', () => {
  let page: Page;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    await login(page);
  });

  test.afterAll(async () => {
    await page.close();
  });

  async function login(page: Page) {
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/');
  }

  test('should navigate to deals page and display pipeline', async () => {
    await page.goto('/deals');
    
    // Verify page elements
    await expect(page.locator('h1')).toContainText('Deals');
    await expect(page.locator('[data-testid="add-deal-button"]')).toBeVisible();
    
    // Verify pipeline view is active by default
    await expect(page.locator('[data-testid="kanban-view"]')).toBeVisible();
    
    // Verify pipeline stages
    await expect(page.locator('[data-testid="stage-lead"]')).toBeVisible();
    await expect(page.locator('[data-testid="stage-qualification"]')).toBeVisible();
    await expect(page.locator('[data-testid="stage-proposal"]')).toBeVisible();
    await expect(page.locator('[data-testid="stage-negotiation"]')).toBeVisible();
    await expect(page.locator('[data-testid="stage-closed-won"]')).toBeVisible();
    await expect(page.locator('[data-testid="stage-closed-lost"]')).toBeVisible();
  });

  test('should create a new deal', async () => {
    await page.goto('/deals');
    
    // Click add deal button
    await page.click('[data-testid="add-deal-button"]');
    
    // Wait for form dialog
    await expect(page.locator('[data-testid="deal-form-dialog"]')).toBeVisible();
    
    // Fill deal form
    await page.fill('[data-testid="title-input"]', testDeal.title);
    await page.fill('[data-testid="amount-input"]', testDeal.amount);
    await page.fill('[data-testid="probability-input"]', testDeal.probability);
    await page.fill('[data-testid="expectedCloseDate-input"]', testDeal.expectedCloseDate);
    await page.fill('[data-testid="description-input"]', testDeal.description);
    
    // Select stage
    await page.click('[data-testid="stage-select"]');
    await page.click('[data-value="qualification"]');
    
    // Select priority
    await page.click('[data-testid="priority-select"]');
    await page.click('[data-value="high"]');
    
    // Submit form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for form to close and success message
    await expect(page.locator('[data-testid="deal-form-dialog"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify deal appears in qualification stage
    const qualificationStage = page.locator('[data-testid="stage-qualification"]');
    await expect(qualificationStage.locator(`[data-testid="deal-${testDeal.title}"]`)).toBeVisible();
  });

  test('should drag and drop deal between stages', async () => {
    await page.goto('/deals');
    
    // Find the deal card
    const dealCard = page.locator(`[data-testid="deal-${testDeal.title}"]`);
    await expect(dealCard).toBeVisible();
    
    // Get source and target stage elements
    const sourceStage = page.locator('[data-testid="stage-qualification"]');
    const targetStage = page.locator('[data-testid="stage-proposal"]');
    
    // Perform drag and drop
    await dealCard.dragTo(targetStage);
    
    // Wait for confirmation dialog
    await expect(page.locator('[data-testid="stage-change-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-stage-change"]');
    
    // Verify deal moved to proposal stage
    await expect(targetStage.locator(`[data-testid="deal-${testDeal.title}"]`)).toBeVisible();
    await expect(sourceStage.locator(`[data-testid="deal-${testDeal.title}"]`)).not.toBeVisible();
  });

  test('should update deal probability when stage changes', async () => {
    await page.goto('/deals');
    
    // Find and click on the deal to view details
    const dealCard = page.locator(`[data-testid="deal-${testDeal.title}"]`);
    await dealCard.click();
    
    // Verify deal details dialog opens
    await expect(page.locator('[data-testid="deal-details-dialog"]')).toBeVisible();
    
    // Check that probability reflects the new stage
    const probabilityElement = page.locator('[data-testid="deal-probability"]');
    await expect(probabilityElement).toContainText('75%');
    
    // Close details dialog
    await page.click('[data-testid="close-details-button"]');
  });

  test('should filter deals by stage', async () => {
    await page.goto('/deals');
    
    // Open filters
    await page.click('[data-testid="filters-button"]');
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    
    // Select proposal stage filter
    await page.click('[data-testid="stage-filter"]');
    await page.click('[data-value="proposal"]');
    
    // Apply filters
    await page.click('[data-testid="apply-filters-button"]');
    
    // Verify only proposal stage deals are visible
    await expect(page.locator('[data-testid="stage-proposal"]')).toBeVisible();
    await expect(page.locator(`[data-testid="deal-${testDeal.title}"]`)).toBeVisible();
    
    // Clear filters
    await page.click('[data-testid="clear-filters-button"]');
  });

  test('should switch between kanban and table views', async () => {
    await page.goto('/deals');
    
    // Switch to table view
    await page.click('[data-testid="table-view-button"]');
    await expect(page.locator('[data-testid="deals-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="kanban-view"]')).not.toBeVisible();
    
    // Verify deal appears in table
    await expect(page.locator(`[data-testid="deal-row-${testDeal.title}"]`)).toBeVisible();
    
    // Switch back to kanban view
    await page.click('[data-testid="kanban-view-button"]');
    await expect(page.locator('[data-testid="kanban-view"]')).toBeVisible();
    await expect(page.locator('[data-testid="deals-table"]')).not.toBeVisible();
  });

  test('should display deal statistics', async () => {
    await page.goto('/deals');
    
    // Verify stats cards are visible
    await expect(page.locator('[data-testid="total-deals-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-value-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="average-deal-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="conversion-rate-stat"]')).toBeVisible();
    
    // Verify stats contain numbers
    const totalDealsText = await page.locator('[data-testid="total-deals-stat"]').textContent();
    expect(totalDealsText).toMatch(/\d+/);
    
    const totalValueText = await page.locator('[data-testid="total-value-stat"]').textContent();
    expect(totalValueText).toMatch(/\$[\d,]+/);
  });

  test('should edit deal from pipeline', async () => {
    await page.goto('/deals');
    
    // Find deal and open menu
    const dealCard = page.locator(`[data-testid="deal-${testDeal.title}"]`);
    await dealCard.locator('[data-testid="deal-menu-button"]').click();
    
    // Click edit
    await page.click('[data-testid="edit-deal-action"]');
    
    // Wait for edit form
    await expect(page.locator('[data-testid="deal-form-dialog"]')).toBeVisible();
    
    // Update deal amount
    await page.fill('[data-testid="amount-input"]', '75000');
    
    // Update description
    await page.fill('[data-testid="description-input"]', 'Updated enterprise deal');
    
    // Submit form
    await page.click('[data-testid="submit-button"]');
    
    // Verify form closes and success message
    await expect(page.locator('[data-testid="deal-form-dialog"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify updated amount is displayed
    await expect(dealCard.locator('[data-testid="deal-amount"]')).toContainText('$75,000');
  });

  test('should search deals', async () => {
    await page.goto('/deals');
    
    // Search for the test deal
    await page.fill('[data-testid="search-input"]', testDeal.title);
    
    // Wait for search results
    await page.waitForTimeout(500);
    
    // Verify only matching deal is visible
    await expect(page.locator(`[data-testid="deal-${testDeal.title}"]`)).toBeVisible();
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '');
    await page.waitForTimeout(500);
  });

  test('should show deal forecast', async () => {
    await page.goto('/deals');
    
    // Switch to forecast view
    await page.click('[data-testid="forecast-view-button"]');
    
    // Verify forecast elements
    await expect(page.locator('[data-testid="forecast-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="weighted-pipeline"]')).toBeVisible();
    await expect(page.locator('[data-testid="monthly-forecast"]')).toBeVisible();
    
    // Verify forecast contains data
    const weightedPipelineText = await page.locator('[data-testid="weighted-pipeline"]').textContent();
    expect(weightedPipelineText).toMatch(/\$[\d,]+/);
  });

  test('should handle deal validation', async () => {
    await page.goto('/deals');
    
    // Try to create deal with invalid data
    await page.click('[data-testid="add-deal-button"]');
    await expect(page.locator('[data-testid="deal-form-dialog"]')).toBeVisible();
    
    // Submit empty form
    await page.click('[data-testid="submit-button"]');
    
    // Verify validation errors
    await expect(page.locator('[data-testid="title-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="amount-error"]')).toBeVisible();
    
    // Test invalid amount
    await page.fill('[data-testid="amount-input"]', '-1000');
    await page.click('[data-testid="submit-button"]');
    await expect(page.locator('[data-testid="amount-error"]')).toContainText('must be positive');
    
    // Test invalid probability
    await page.fill('[data-testid="probability-input"]', '150');
    await page.click('[data-testid="submit-button"]');
    await expect(page.locator('[data-testid="probability-error"]')).toContainText('between 0 and 100');
    
    // Close form
    await page.click('[data-testid="cancel-button"]');
  });

  test('should work on mobile viewport', async () => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/deals');
    
    // Verify mobile navigation
    await expect(page.locator('[data-testid="mobile-navigation"]')).toBeVisible();
    
    // Verify mobile FAB
    await expect(page.locator('[data-testid="mobile-fab"]')).toBeVisible();
    
    // Test mobile deal creation
    await page.click('[data-testid="mobile-fab"]');
    await expect(page.locator('[data-testid="deal-form-dialog"]')).toBeVisible();
    
    // Close form
    await page.click('[data-testid="cancel-button"]');
    
    // Verify pipeline is responsive
    await expect(page.locator('[data-testid="kanban-view"]')).toBeVisible();
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should delete deal', async () => {
    await page.goto('/deals');
    
    // Find deal and open menu
    const dealCard = page.locator(`[data-testid="deal-${testDeal.title}"]`);
    await dealCard.locator('[data-testid="deal-menu-button"]').click();
    
    // Click delete
    await page.click('[data-testid="delete-deal-action"]');
    
    // Confirm deletion
    await expect(page.locator('[data-testid="confirm-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-button"]');
    
    // Verify deal is removed
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(dealCard).not.toBeVisible();
  });
});
