import { test, expect } from '@playwright/test';

test.describe('Keycloak Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000');
  });

  test('should display login page with Keycloak option', async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page title is correct
    await expect(page).toHaveTitle(/OneCRM/);
    
    // Check if OneCRM branding is visible
    await expect(page.locator('h4:has-text("OneCRM")')).toBeVisible();
    
    // Check if the Keycloak login button is present
    await expect(page.locator('button:has-text("Sign in with Keycloak")')).toBeVisible();
    
    // Check if the subtitle is present
    await expect(page.locator('text=Enterprise Customer Relationship Management')).toBeVisible();
  });

  test('should redirect to Keycloak when clicking login button', async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click the Keycloak login button
    const loginButton = page.locator('button:has-text("Sign in with Keycloak")');
    await expect(loginButton).toBeVisible();
    
    // Click the login button and wait for navigation
    await Promise.all([
      page.waitForNavigation({ timeout: 10000 }),
      loginButton.click()
    ]);
    
    // Check if we're redirected to Keycloak or NextAuth
    const currentUrl = page.url();
    console.log('Current URL after login click:', currentUrl);
    
    // Should be redirected to either Keycloak or NextAuth callback
    expect(currentUrl).toMatch(/(stgsso\.cubeone\.in|localhost:3000\/api\/auth)/);
  });

  test('should handle Keycloak authentication flow', async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click the Keycloak login button
    const loginButton = page.locator('button:has-text("Sign in with Keycloak")');
    await loginButton.click();
    
    // Wait for redirect (either to Keycloak or error page)
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    
    const currentUrl = page.url();
    console.log('Final URL:', currentUrl);
    
    if (currentUrl.includes('stgsso.cubeone.in')) {
      // We're on Keycloak login page
      console.log('Successfully redirected to Keycloak');
      
      // Check for Keycloak login form elements
      await expect(page.locator('#username, input[name="username"]')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('#password, input[name="password"]')).toBeVisible();
      await expect(page.locator('input[type="submit"], button[type="submit"]')).toBeVisible();
      
      console.log('Keycloak login form is visible and ready');
    } else if (currentUrl.includes('/auth/error')) {
      // We're on error page - check error details
      console.log('Redirected to error page');
      
      // Check if error page is displayed
      await expect(page.locator('h4:has-text("Authentication Error")')).toBeVisible();
      
      // Log the error message
      const errorMessage = await page.locator('[role="alert"]').textContent();
      console.log('Error message:', errorMessage);
    } else {
      // Unexpected redirect
      console.log('Unexpected redirect to:', currentUrl);
      
      // Take a screenshot for debugging
      await page.screenshot({ path: 'keycloak-test-debug.png', fullPage: true });
    }
  });

  test('should display proper error handling', async ({ page }) => {
    // Navigate directly to auth error page to test error handling
    await page.goto('http://localhost:3000/auth/error?error=Configuration');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if error page elements are visible
    await expect(page.locator('h4:has-text("Authentication Error")')).toBeVisible();
    await expect(page.locator('button:has-text("Try Again")')).toBeVisible();
    await expect(page.locator('button:has-text("Go Home")')).toBeVisible();
    
    // Check if error message is displayed
    await expect(page.locator('text=There is a problem with the server configuration')).toBeVisible();
  });

  test('should test NextAuth providers endpoint', async ({ page }) => {
    // Test the NextAuth providers endpoint
    const response = await page.request.get('http://localhost:3000/api/auth/providers');
    
    console.log('NextAuth providers response status:', response.status());
    
    if (response.status() === 200) {
      const providers = await response.json();
      console.log('Available providers:', providers);
      
      // Check if Keycloak provider is available
      expect(providers).toHaveProperty('keycloak');
    } else {
      console.log('NextAuth providers endpoint error:', response.status());
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  });

  test('should test API connectivity through frontend proxy', async ({ page }) => {
    // Test API health endpoint through frontend proxy
    const response = await page.request.get('http://localhost:3000/api/health');
    
    expect(response.status()).toBe(200);
    
    const healthData = await response.json();
    expect(healthData).toHaveProperty('status', 'ok');
    expect(healthData).toHaveProperty('service', 'onecrm-api');
    
    console.log('API Health Check:', healthData);
  });
});
