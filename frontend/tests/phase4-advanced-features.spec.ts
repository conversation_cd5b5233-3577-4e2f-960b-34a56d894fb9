import { test, expect } from '@playwright/test'

test.describe('Phase 4: Advanced Features & Polish', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 })
  })

  test.describe('Toast Notifications', () => {
    test('should display success toast notification', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Trigger a success action that should show a toast
      await page.getByRole('button', { name: /submit form/i }).click()
      
      // Wait for toast to appear
      const toast = page.locator('[role="status"]').first()
      await expect(toast).toBeVisible({ timeout: 5000 })
      
      // Check toast content
      await expect(toast).toContainText('success', { ignoreCase: true })
      
      console.log('Success toast notification test passed')
    })

    test('should display error toast notification', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Trigger an error by submitting invalid form
      await page.getByRole('button', { name: /submit form/i }).click()
      
      // Wait for error toast
      const errorToast = page.locator('[role="status"]').first()
      await expect(errorToast).toBeVisible({ timeout: 5000 })
      
      console.log('Error toast notification test passed')
    })

    test('should auto-dismiss toast after timeout', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Trigger a toast
      await page.getByRole('button', { name: /submit form/i }).click()
      
      const toast = page.locator('[role="status"]').first()
      await expect(toast).toBeVisible()
      
      // Wait for auto-dismiss (should disappear after 5 seconds)
      await expect(toast).not.toBeVisible({ timeout: 7000 })
      
      console.log('Toast auto-dismiss test passed')
    })
  })

  test.describe('Command Palette', () => {
    test('should open command palette with Cmd+K', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Press Cmd+K (or Ctrl+K on Windows/Linux)
      await page.keyboard.press('Meta+k')
      
      // Check if command palette is visible
      const commandPalette = page.locator('[cmdk-root]')
      await expect(commandPalette).toBeVisible()
      
      console.log('Command palette keyboard shortcut test passed')
    })

    test('should search and navigate using command palette', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Open command palette
      await page.keyboard.press('Meta+k')
      
      // Type search query
      await page.fill('[cmdk-input]', 'dashboard')
      
      // Wait for search results
      await page.waitForTimeout(500)
      
      // Check if dashboard option appears
      const dashboardOption = page.locator('[cmdk-item]').filter({ hasText: 'Dashboard' })
      await expect(dashboardOption).toBeVisible()
      
      console.log('Command palette search test passed')
    })

    test('should execute quick actions from command palette', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Open command palette
      await page.keyboard.press('Meta+k')
      
      // Look for quick actions
      const newContactAction = page.locator('[cmdk-item]').filter({ hasText: 'New Contact' })
      if (await newContactAction.isVisible()) {
        await newContactAction.click()
        
        // Should navigate or trigger action
        await page.waitForTimeout(1000)
        console.log('Command palette quick action test passed')
      }
    })
  })

  test.describe('Error Handling', () => {
    test('should display error boundary for component errors', async ({ page }) => {
      // Navigate to a page that might have errors
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Inject an error to test error boundary
      await page.evaluate(() => {
        // Simulate a component error
        const errorEvent = new Error('Test error for error boundary')
        window.dispatchEvent(new ErrorEvent('error', { error: errorEvent }))
      })
      
      // Check if error boundary is displayed
      const errorBoundary = page.locator('text=Something went wrong')
      if (await errorBoundary.isVisible()) {
        await expect(errorBoundary).toBeVisible()
        console.log('Error boundary test passed')
      }
    })

    test('should provide error recovery options', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Look for retry buttons in case of errors
      const retryButton = page.locator('button:has-text("Try Again"), button:has-text("Retry")')
      if (await retryButton.isVisible()) {
        await expect(retryButton).toBeVisible()
        await retryButton.click()
        console.log('Error recovery test passed')
      }
    })
  })

  test.describe('Performance Monitoring', () => {
    test('should load page within acceptable time limits', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)
      
      console.log(`Page load time: ${loadTime}ms`)
    })

    test('should have good Core Web Vitals', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Measure Largest Contentful Paint (LCP)
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            resolve(lastEntry.startTime)
          }).observe({ entryTypes: ['largest-contentful-paint'] })
          
          // Fallback timeout
          setTimeout(() => resolve(0), 5000)
        })
      })
      
      // LCP should be under 2.5 seconds for good rating
      if (typeof lcp === 'number' && lcp > 0) {
        expect(lcp).toBeLessThan(2500)
        console.log(`LCP: ${lcp}ms`)
      }
    })
  })

  test.describe('Accessibility Features', () => {
    test('should have proper ARIA labels and roles', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Check for proper ARIA labels
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i)
        const hasAccessibleName = await button.evaluate(el => {
          return !!(
            el.textContent?.trim() ||
            el.getAttribute('aria-label') ||
            el.getAttribute('aria-labelledby')
          )
        })
        expect(hasAccessibleName).toBe(true)
      }
      
      console.log('ARIA labels test passed')
    })

    test('should support keyboard navigation', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Test tab navigation
      await page.keyboard.press('Tab')
      const firstFocusable = page.locator(':focus')
      await expect(firstFocusable).toBeVisible()
      
      // Test multiple tab presses
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
      
      console.log('Keyboard navigation test passed')
    })

    test('should have sufficient color contrast', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Check color contrast for text elements
      const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span').first()
      
      const contrastInfo = await textElements.evaluate(el => {
        const styles = window.getComputedStyle(el)
        return {
          color: styles.color,
          backgroundColor: styles.backgroundColor,
        }
      })
      
      // Basic check that colors are defined
      expect(contrastInfo.color).toBeTruthy()
      
      console.log('Color contrast check passed')
    })
  })

  test.describe('Data Visualization', () => {
    test('should render charts correctly', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Look for chart containers
      const chartContainers = page.locator('.recharts-wrapper')
      if (await chartContainers.count() > 0) {
        const firstChart = chartContainers.first()
        await expect(firstChart).toBeVisible()
        
        // Check if chart has rendered content
        const chartContent = firstChart.locator('svg')
        await expect(chartContent).toBeVisible()
        
        console.log('Chart rendering test passed')
      }
    })

    test('should handle chart interactions', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Look for interactive chart elements
      const chartElements = page.locator('.recharts-wrapper svg')
      if (await chartElements.count() > 0) {
        const firstChart = chartElements.first()
        
        // Hover over chart to trigger tooltip
        await firstChart.hover()
        
        // Look for tooltip
        const tooltip = page.locator('.recharts-tooltip-wrapper')
        if (await tooltip.isVisible()) {
          await expect(tooltip).toBeVisible()
          console.log('Chart interaction test passed')
        }
      }
    })
  })

  test.describe('Real-time Features', () => {
    test('should show loading states during async operations', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Trigger an async operation
      const submitButton = page.getByRole('button', { name: /submit/i })
      await submitButton.click()
      
      // Check for loading state
      const loadingIndicator = page.locator('text=Loading, text=Submitting, [data-loading="true"]').first()
      if (await loadingIndicator.isVisible()) {
        await expect(loadingIndicator).toBeVisible()
        console.log('Loading state test passed')
      }
    })

    test('should handle optimistic UI updates', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Look for elements that might show optimistic updates
      const interactiveElements = page.locator('button, [role="button"]')
      const elementCount = await interactiveElements.count()
      
      if (elementCount > 0) {
        const firstElement = interactiveElements.first()
        await firstElement.click()
        
        // Check if UI updates immediately (optimistic update)
        await page.waitForTimeout(100)
        console.log('Optimistic UI update test passed')
      }
    })
  })

  test.describe('Production Build Validation', () => {
    test('should have no console errors', async ({ page }) => {
      const consoleErrors: string[] = []
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text())
        }
      })
      
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Allow some time for any delayed errors
      await page.waitForTimeout(2000)
      
      // Filter out known acceptable errors (like network errors in test environment)
      const criticalErrors = consoleErrors.filter(error => 
        !error.includes('Failed to fetch') &&
        !error.includes('NetworkError') &&
        !error.includes('ERR_INTERNET_DISCONNECTED')
      )
      
      expect(criticalErrors.length).toBe(0)
      
      if (criticalErrors.length > 0) {
        console.log('Console errors found:', criticalErrors)
      } else {
        console.log('No critical console errors found')
      }
    })

    test('should have optimized bundle size', async ({ page }) => {
      await page.goto('/ui-test')
      await page.waitForLoadState('networkidle')
      
      // Check resource sizes
      const resourceSizes = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
        const jsResources = resources.filter(r => r.name.includes('.js'))
        
        return {
          totalJS: jsResources.reduce((sum, r) => sum + (r.transferSize || 0), 0),
          resourceCount: jsResources.length,
        }
      })
      
      // Total JS should be reasonable (under 2MB for initial load)
      expect(resourceSizes.totalJS).toBeLessThan(2 * 1024 * 1024)
      
      console.log(`Total JS size: ${(resourceSizes.totalJS / 1024).toFixed(2)}KB`)
      console.log(`JS resource count: ${resourceSizes.resourceCount}`)
    })
  })
})
