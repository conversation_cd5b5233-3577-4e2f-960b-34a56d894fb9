// Mock API service for development and testing
import { mockConfig, shouldUseMockAPI } from '../config/mock.config';

interface MockResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

interface MockApiOptions {
  delay?: number;
  errorRate?: number;
  status?: number;
}

class MockApiService {
  private baseUrl = 'http://localhost:3002/api';

  // Mock data generators
  private generateMockContacts(count: number = 10) {
    return Array.from({ length: count }, (_, i) => ({
      id: `contact-${i + 1}`,
      firstName: `First${i + 1}`,
      lastName: `Last${i + 1}`,
      email: `contact${i + 1}@example.com`,
      phone: `******-${String(i + 1).padStart(4, '0')}`,
      company: `Company ${i + 1}`,
      position: `Position ${i + 1}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
    }));
  }

  private generateMockCompanies(count: number = 8) {
    const industries = ['Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Retail'];
    return Array.from({ length: count }, (_, i) => ({
      id: `company-${i + 1}`,
      name: `Company ${i + 1}`,
      industry: industries[i % industries.length],
      website: `https://company${i + 1}.com`,
      employees: Math.floor(Math.random() * 1000) + 10,
      revenue: Math.floor(Math.random() * 10000000) + 100000,
      description: `Description for Company ${i + 1}`,
      createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
    }));
  }

  private generateMockDeals(count: number = 12) {
    const stages = ['Prospecting', 'Qualification', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost'];
    return Array.from({ length: count }, (_, i) => ({
      id: `deal-${i + 1}`,
      title: `Deal ${i + 1}`,
      value: Math.floor(Math.random() * 100000) + 5000,
      stage: stages[i % stages.length],
      probability: Math.floor(Math.random() * 100),
      expectedCloseDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      description: `Description for Deal ${i + 1}`,
      contactId: `contact-${(i % 10) + 1}`,
      companyId: `company-${(i % 8) + 1}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
    }));
  }

  private generateMockStats() {
    return {
      contacts: {
        total: 150,
        thisMonth: 12,
        growth: 8.5,
      },
      companies: {
        total: 45,
        thisMonth: 3,
        growth: 6.7,
      },
      deals: {
        total: 89,
        value: 2450000,
        thisMonth: 8,
        growth: 12.3,
      },
      revenue: {
        total: 1850000,
        thisMonth: 245000,
        growth: 15.2,
      },
    };
  }

  // Simulate network delay and potential errors
  private async simulateRequest<T>(
    data: T,
    options: MockApiOptions = {}
  ): Promise<MockResponse<T>> {
    const delay = options.delay ?? mockConfig.api.delay;
    const errorRate = options.errorRate ?? mockConfig.api.errorRate;
    const status = options.status ?? 200;

    // Simulate network delay
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Simulate random errors
    if (Math.random() * 100 < errorRate) {
      throw new Error('Mock API: Simulated network error');
    }

    return {
      data,
      status,
      statusText: 'OK',
    };
  }

  // Mock API endpoints
  async getContacts(): Promise<MockResponse<any[]>> {
    console.log('🎭 Mock API: Fetching contacts');
    return this.simulateRequest(this.generateMockContacts());
  }

  async getContact(id: string): Promise<MockResponse<any>> {
    console.log('🎭 Mock API: Fetching contact', id);
    const contacts = this.generateMockContacts();
    const contact = contacts.find(c => c.id === id) || contacts[0];
    return this.simulateRequest(contact);
  }

  async createContact(data: any): Promise<MockResponse<any>> {
    console.log('🎭 Mock API: Creating contact', data);
    const newContact = {
      id: `contact-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return this.simulateRequest(newContact, { status: 201 });
  }

  async getCompanies(): Promise<MockResponse<any[]>> {
    console.log('🎭 Mock API: Fetching companies');
    return this.simulateRequest(this.generateMockCompanies());
  }

  async getDeals(): Promise<MockResponse<any[]>> {
    console.log('🎭 Mock API: Fetching deals');
    return this.simulateRequest(this.generateMockDeals());
  }

  async getStats(): Promise<MockResponse<any>> {
    console.log('🎭 Mock API: Fetching stats');
    return this.simulateRequest(this.generateMockStats());
  }

  async getHealth(): Promise<MockResponse<any>> {
    console.log('🎭 Mock API: Health check');
    return this.simulateRequest({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'onecrm-mock-api',
      version: '1.0.0',
      environment: 'development',
    });
  }

  // Generic mock request handler
  async mockRequest<T>(endpoint: string, data?: T): Promise<MockResponse<T>> {
    console.log(`🎭 Mock API: ${endpoint}`, data);
    
    // Return the input data or a generic success response
    const responseData = data || { success: true, endpoint } as T;
    return this.simulateRequest(responseData);
  }
}

// Singleton instance
export const mockApi = new MockApiService();

// Wrapper function that decides whether to use mock or real API
export const apiRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  if (shouldUseMockAPI()) {
    // Use mock API
    const mockResponse = await mockApi.mockRequest<T>(endpoint, options.body ? JSON.parse(options.body as string) : undefined);
    return mockResponse.data;
  }

  // Use real API
  const response = await fetch(`http://localhost:3002/api${endpoint}`, options);
  
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

// Convenience methods for common API operations
export const api = {
  get: <T = any>(endpoint: string): Promise<T> => 
    apiRequest<T>(endpoint, { method: 'GET' }),
    
  post: <T = any>(endpoint: string, data: any): Promise<T> => 
    apiRequest<T>(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
    
  put: <T = any>(endpoint: string, data: any): Promise<T> => 
    apiRequest<T>(endpoint, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
    
  delete: <T = any>(endpoint: string): Promise<T> => 
    apiRequest<T>(endpoint, { method: 'DELETE' }),
};

// Development helper to check if mock API is enabled
export const isMockApiEnabled = (): boolean => {
  return shouldUseMockAPI();
};

// Get mock API configuration for display
export const getMockApiConfig = () => {
  return {
    enabled: shouldUseMockAPI(),
    delay: mockConfig.api.delay,
    errorRate: mockConfig.api.errorRate,
  };
};
