import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/** Formatters used across pages */
export function formatNumber(value: number | string | null | undefined): string {
  if (value === null || value === undefined) return '-'
  const n = typeof value === 'string' ? Number(value) : value
  if (Number.isNaN(n)) return String(value)
  return new Intl.NumberFormat(undefined, { maximumFractionDigits: 2 }).format(n)
}

export function formatCurrency(
  value: number | string | null | undefined,
  currency: string = 'USD'
): string {
  if (value === null || value === undefined) return '-'
  const n = typeof value === 'string' ? Number(value) : value
  if (Number.isNaN(n)) return String(value)
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency,
    maximumFractionDigits: 2
  }).format(n)
}

export function formatDate(
  date: Date | string | number | null | undefined,
  options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' }
): string {
  if (!date) return '-'
  const d = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  if (Number.isNaN(d.getTime())) return '-'
  return new Intl.DateTimeFormat(undefined, options).format(d)
}
