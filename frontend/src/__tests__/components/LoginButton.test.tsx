import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LoginButton } from '../../components/auth/LoginButton';
import { useAuth } from '../../hooks/useAuth';

// Mock the useAuth hook
jest.mock('../../hooks/useAuth');
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Button: ({ children, onClick, disabled, startIcon, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {startIcon}
      {children}
    </button>
  ),
  CircularProgress: ({ size }: any) => <div data-testid="loading-spinner">Loading...</div>,
}));

jest.mock('@mui/icons-material', () => ({
  Login: () => <div data-testid="login-icon">Login Icon</div>,
}));

describe('LoginButton', () => {
  const mockLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      organization: null,
      token: null,
      login: mockLogin,
      logout: jest.fn(),
      refreshToken: jest.fn(),
      hasRole: jest.fn(),
      hasPermission: jest.fn(),
    });
  });

  it('renders login button with correct text', () => {
    render(<LoginButton />);
    
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByTestId('login-icon')).toBeInTheDocument();
  });

  it('calls login function when clicked', async () => {
    render(<LoginButton />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledTimes(1);
    });
  });

  it('shows loading state when isLoading is true', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: true,
      user: null,
      organization: null,
      token: null,
      login: mockLogin,
      logout: jest.fn(),
      refreshToken: jest.fn(),
      hasRole: jest.fn(),
      hasPermission: jest.fn(),
    });

    render(<LoginButton />);
    
    expect(screen.getByText('Signing in...')).toBeInTheDocument();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('handles login error gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockLogin.mockRejectedValue(new Error('Login failed'));

    render(<LoginButton />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Login failed:', expect.any(Error));
    });

    consoleErrorSpy.mockRestore();
  });

  it('applies custom props correctly', () => {
    render(
      <LoginButton 
        variant="outlined" 
        size="large" 
        fullWidth={true}
        className="custom-class"
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });
});
