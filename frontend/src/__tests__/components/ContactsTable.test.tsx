import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import { ContactsTable } from '../../components/contacts/ContactsTable';

// Mock SWR
jest.mock('swr');

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

const theme = createTheme();

const mockContacts = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    company: 'Tech Corp',
    title: 'Software Engineer',
    leadStatus: 'new',
    leadSource: 'website',
    tags: ['developer', 'tech'],
    assignedTo: {
      firstName: 'Jane',
      lastName: 'Manager',
    },
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+**********',
    company: 'Design Studio',
    title: 'UX Designer',
    leadStatus: 'qualified',
    leadSource: 'referral',
    tags: ['design'],
    assignedTo: {
      firstName: 'Bob',
      lastName: 'Sales',
    },
    createdAt: '2024-01-02T00:00:00Z',
  },
];

const mockSWRResponse = {
  contacts: mockContacts,
  total: 2,
  page: 1,
  limit: 25,
  totalPages: 1,
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      <SWRConfig value={{ provider: () => new Map() }}>
        {component}
      </SWRConfig>
    </ThemeProvider>
  );
};

describe('ContactsTable', () => {
  const defaultProps = {
    searchQuery: '',
    filters: {},
    onEditContact: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock SWR to return our test data
    require('swr').default.mockReturnValue({
      data: mockSWRResponse,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });
  });

  it('renders contacts table with data', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Check table headers
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Contact Info')).toBeInTheDocument();
    expect(screen.getByText('Company')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();

    // Check contact data
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Tech Corp')).toBeInTheDocument();
    expect(screen.getByText('Design Studio')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    require('swr').default.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Should show skeleton loaders
    expect(screen.getAllByTestId('skeleton')).toHaveLength(90); // 9 columns × 10 rows
  });

  it('handles contact selection', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Find and click the first contact's checkbox
    const checkboxes = screen.getAllByRole('checkbox');
    const firstContactCheckbox = checkboxes[1]; // Skip the "select all" checkbox

    if (firstContactCheckbox) {
      fireEvent.click(firstContactCheckbox);
    }

    expect(firstContactCheckbox).toBeChecked();
  });

  it('handles select all functionality', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Find and click the "select all" checkbox
    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];

    if (selectAllCheckbox) {
      fireEvent.click(selectAllCheckbox);
    }

    // All contact checkboxes should be checked
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.slice(1).forEach(checkbox => {
      expect(checkbox).toBeChecked();
    });
  });

  it('opens contact menu on more actions click', async () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Find and click the first "more actions" button
    const moreButtons = screen.getAllByLabelText('more actions');
    if (moreButtons[0]) {
      fireEvent.click(moreButtons[0]);
    }

    // Menu should appear
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });
  });

  it('calls onEditContact when edit is clicked', async () => {
    const onEditContact = jest.fn();
    renderWithProviders(
      <ContactsTable {...defaultProps} onEditContact={onEditContact} />
    );

    // Open menu and click edit
    const moreButtons = screen.getAllByLabelText('more actions');
    if (moreButtons[0]) {
      fireEvent.click(moreButtons[0]);
    }

    await waitFor(() => {
      const editButton = screen.getByText('Edit');
      fireEvent.click(editButton);
    });

    expect(onEditContact).toHaveBeenCalledWith(mockContacts[0]);
  });

  it('handles pagination', () => {
    const mockDataWithPagination = {
      ...mockSWRResponse,
      total: 50,
      totalPages: 2,
    };

    require('swr').default.mockReturnValue({
      data: mockDataWithPagination,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Check pagination component
    expect(screen.getByText('1–25 of 50')).toBeInTheDocument();
    
    // Check next page button
    const nextButton = screen.getByLabelText('Go to next page');
    expect(nextButton).toBeInTheDocument();
    expect(nextButton).not.toBeDisabled();
  });

  it('applies search filter to SWR key', () => {
    const searchQuery = 'john';
    renderWithProviders(
      <ContactsTable {...defaultProps} searchQuery={searchQuery} />
    );

    // Verify SWR was called with search parameter
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('search=john')
    );
  });

  it('applies filters to SWR key', () => {
    const filters = {
      leadStatus: 'qualified',
      leadSource: 'website',
      tags: ['developer'],
    };

    renderWithProviders(
      <ContactsTable {...defaultProps} filters={filters} />
    );

    // Verify SWR was called with filter parameters
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('leadStatus=qualified')
    );
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('leadSource=website')
    );
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('tags=developer')
    );
  });

  it('displays contact tags', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Check that tags are displayed
    expect(screen.getByText('developer')).toBeInTheDocument();
    expect(screen.getByText('tech')).toBeInTheDocument();
    expect(screen.getByText('design')).toBeInTheDocument();
  });

  it('displays assigned user avatars', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Check for assigned user avatars (by their initials)
    expect(screen.getByText('JM')).toBeInTheDocument(); // Jane Manager
    expect(screen.getByText('BS')).toBeInTheDocument(); // Bob Sales
  });

  it('handles email and phone links', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Check for email links
    const emailLinks = screen.getAllByRole('link');
    const emailLink = emailLinks.find(link => 
      link.getAttribute('href')?.includes('mailto:')
    );
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');

    // Check for phone links
    const phoneLink = emailLinks.find(link => 
      link.getAttribute('href')?.includes('tel:')
    );
    expect(phoneLink).toHaveAttribute('href', 'tel:+**********');
  });

  it('handles empty state', () => {
    require('swr').default.mockReturnValue({
      data: { contacts: [], total: 0, page: 1, limit: 25, totalPages: 0 },
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Should show empty state message
    expect(screen.getByText('No contacts found')).toBeInTheDocument();
  });

  it('handles error state', () => {
    require('swr').default.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to fetch'),
      mutate: jest.fn(),
    });

    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Should show error message
    expect(screen.getByText('Error loading contacts')).toBeInTheDocument();
  });

  it('updates page when pagination is changed', () => {
    const mockDataWithPagination = {
      ...mockSWRResponse,
      total: 50,
      totalPages: 2,
    };

    require('swr').default.mockReturnValue({
      data: mockDataWithPagination,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Click next page
    const nextButton = screen.getByLabelText('Go to next page');
    fireEvent.click(nextButton);

    // Should update the SWR key with new page
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('page=2')
    );
  });

  it('updates rows per page when changed', () => {
    renderWithProviders(<ContactsTable {...defaultProps} />);

    // Find and change rows per page
    const rowsPerPageSelect = screen.getByDisplayValue('25');
    fireEvent.mouseDown(rowsPerPageSelect);
    
    const option50 = screen.getByText('50');
    fireEvent.click(option50);

    // Should update the SWR key with new limit
    expect(require('swr').default).toHaveBeenCalledWith(
      expect.stringContaining('limit=50')
    );
  });
});
