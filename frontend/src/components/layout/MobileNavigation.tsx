'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  BottomNavigation,
  BottomNavigationAction,
  Paper,
  Badge,
  Box,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  Add as AddIcon,
  PersonAdd as PersonAddIcon,
  BusinessCenter as BusinessCenterIcon,
  AttachMoney as MoneyIcon,
  NoteAdd as NoteAddIcon,
} from '@mui/icons-material';

const navigationItems = [
  { label: 'Dashboard', icon: DashboardIcon, path: '/' },
  { label: 'Contacts', icon: PeopleIcon, path: '/contacts' },
  { label: 'Companies', icon: BusinessIcon, path: '/companies' },
  { label: 'Deals', icon: TrendingUpIcon, path: '/deals' },
  { label: 'Activities', icon: AssignmentIcon, path: '/activities' },
];

const quickActions = [
  { icon: <PersonAddIcon />, name: 'Add Contact', path: '/contacts?action=create' },
  { icon: <BusinessCenterIcon />, name: 'Add Company', path: '/companies?action=create' },
  { icon: <MoneyIcon />, name: 'Add Deal', path: '/deals?action=create' },
  { icon: <NoteAddIcon />, name: 'Add Activity', path: '/activities?action=create' },
];

interface MobileNavigationProps {
  pendingActivities?: number;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  pendingActivities = 0,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [speedDialOpen, setSpeedDialOpen] = useState(false);

  if (!isMobile) {
    return null;
  }

  const getCurrentValue = () => {
    const currentItem = navigationItems.find(item => item.path === pathname);
    return currentItem ? navigationItems.indexOf(currentItem) : 0;
  };

  const handleNavigationChange = (event: React.SyntheticEvent, newValue: number) => {
    const item = navigationItems[newValue];
    if (item) {
      router.push(item.path);
    }
  };

  const handleSpeedDialAction = (path: string) => {
    setSpeedDialOpen(false);
    router.push(path);
  };

  return (
    <>
      {/* Bottom Navigation */}
      <Paper
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          borderTop: 1,
          borderColor: 'divider',
        }}
        elevation={8}
      >
        <BottomNavigation
          value={getCurrentValue()}
          onChange={handleNavigationChange}
          showLabels
          sx={{
            height: 70,
            '& .MuiBottomNavigationAction-root': {
              minWidth: 'auto',
              padding: '6px 12px 8px',
            },
            '& .MuiBottomNavigationAction-label': {
              fontSize: '0.75rem',
              '&.Mui-selected': {
                fontSize: '0.75rem',
              },
            },
          }}
        >
          {navigationItems.map((item, index) => {
            const IconComponent = item.icon;
            const isActivities = item.path === '/activities';
            
            return (
              <BottomNavigationAction
                key={item.path}
                label={item.label}
                icon={
                  isActivities && pendingActivities > 0 ? (
                    <Badge badgeContent={pendingActivities} color="error" max={99}>
                      <IconComponent />
                    </Badge>
                  ) : (
                    <IconComponent />
                  )
                }
              />
            );
          })}
        </BottomNavigation>
      </Paper>

      {/* Speed Dial for Quick Actions */}
      <SpeedDial
        ariaLabel="Quick Actions"
        sx={{
          position: 'fixed',
          bottom: 90, // Above bottom navigation
          right: 16,
          zIndex: 1001,
        }}
        icon={<SpeedDialIcon />}
        open={speedDialOpen}
        onOpen={() => setSpeedDialOpen(true)}
        onClose={() => setSpeedDialOpen(false)}
        direction="up"
      >
        {quickActions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
            onClick={() => handleSpeedDialAction(action.path)}
            sx={{
              '& .MuiSpeedDialAction-fab': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              },
            }}
          />
        ))}
      </SpeedDial>

      {/* Spacer to prevent content from being hidden behind bottom navigation */}
      <Box sx={{ height: 70 }} />
    </>
  );
};
