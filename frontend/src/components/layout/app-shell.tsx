'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import Navbar from '@/components/layout/Navbar'
import { Sidebar } from '@/components/layout/sidebar'

export interface AppShellProps {
  children: React.ReactNode
  withNavbar?: boolean
  withSidebar?: boolean
  className?: string
}

export function AppShell({
  children,
  withNavbar = true,
  withSidebar = true,
  className
}: AppShellProps) {
  return (
    <div className="min-h-screen bg-ambient">
      {withNavbar ? <Navbar /> : null}
      <div className={cn('mx-auto flex w-full max-w-screen-2xl', withNavbar ? 'pt-0' : 'pt-4')}>
        {withSidebar ? (
          <aside className="hidden md:block w-64 shrink-0">
            <div className="sticky top-16 h-[calc(100vh-4rem)]">
              <Sidebar />
            </div>
          </aside>
        ) : null}
        <main
          className={cn(
            'flex-1',
            className
          )}
        >
          {children}
        </main>
      </div>
    </div>
  )
}

export function Container({
  children,
  className
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn('px-4 sm:px-6 lg:px-8', className)}>
      {children}
    </div>
  )
}

export function PageHeader({
  title,
  description,
  actions,
  className
}: {
  title: string
  description?: string
  actions?: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn('flex flex-col gap-2 sm:flex-row sm:items-end sm:justify-between', className)}>
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
        {description ? (
          <p className="text-sm text-muted-foreground">{description}</p>
        ) : null}
      </div>
      {actions ? <div className="flex items-center gap-2">{actions}</div> : null}
    </div>
  )
}

export function ContentSection({
  title,
  description,
  children,
  className,
  headerRight
}: {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  headerRight?: React.ReactNode
}) {
  return (
    <section className={cn('space-y-3', className)}>
      {(title || description || headerRight) ? (
        <div className="flex items-end justify-between">
          <div>
            {title ? <h2 className="text-lg font-semibold">{title}</h2> : null}
            {description ? <p className="text-sm text-muted-foreground">{description}</p> : null}
          </div>
          {headerRight}
        </div>
      ) : null}
      <div className="card p-4">{children}</div>
    </section>
  )
}
