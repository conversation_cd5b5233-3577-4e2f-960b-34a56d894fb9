'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  LayoutDashboard,
  Users,
  Building2,
  TrendingUp,
  Settings,
  Search,
  Bell,
  ChevronDown,
  ChevronRight,
  ShieldCheck,
  Calendar
} from 'lucide-react'
import { NavigationItem } from '@/types/ui'

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/dashboard',
    icon: <LayoutDashboard className="h-4 w-4" />
  },
  {
    id: 'contacts',
    label: 'Contacts',
    href: '/contacts',
    icon: <Users className="h-4 w-4" />,
    badge: '1,247'
  },
  {
    id: 'companies',
    label: 'Companies',
    href: '/companies',
    icon: <Building2 className="h-4 w-4" />,
    badge: '156'
  },
  {
    id: 'deals',
    label: 'Deals',
    href: '/deals',
    icon: <TrendingUp className="h-4 w-4" />,
    badge: '89'
  },
  {
    id: 'activities',
    label: 'Activities',
    href: '/activities',
    icon: <Calendar className="h-4 w-4" />
  },
  {
    id: 'organizations',
    label: 'Organizations',
    href: '/organizations',
    icon: <Building2 className="h-4 w-4" />
  },
  {
    id: 'users',
    label: 'Users',
    href: '/users',
    icon: <Users className="h-4 w-4" />
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/analytics',
    icon: <Search className="h-4 w-4" />
  }
]

const bottomNavigationItems: NavigationItem[] = [
  {
    id: 'notifications',
    label: 'Notifications',
    href: '/notifications',
    icon: <Bell className="h-4 w-4" />,
    badge: '3'
  },
  {
    id: 'rbac',
    label: 'RBAC',
    href: '/rbac',
    icon: <ShieldCheck className="h-4 w-4" />
  },
  {
    id: 'settings',
    label: 'Settings',
    href: '/settings',
    icon: <Settings className="h-4 w-4" />
  }
]

interface SidebarProps {
  onNavigate?: () => void
}

export function Sidebar({ onNavigate }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/' || pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const active = isActive(item.href)
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)

    return (
      <div key={item.id}>
        <div className="relative">
          {hasChildren ? (
            <Button
              variant="ghost"
              className={cn(
                'w-full justify-start gap-3 h-10',
                level > 0 && 'ml-4 w-[calc(100%-1rem)]',
                active && 'bg-accent text-accent-foreground'
              )}
              onClick={() => toggleExpanded(item.id)}
            >
              {item.icon}
              <span className="flex-1 text-left">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
              {hasChildren && (
                isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )
              )}
            </Button>
          ) : (
            <Button
              variant="ghost"
              className={cn(
                'w-full justify-start gap-3 h-10',
                level > 0 && 'ml-4 w-[calc(100%-1rem)]',
                active && 'bg-accent text-accent-foreground'
              )}
              asChild
            >
              <Link
                href={item.href}
                onClick={(e) => {
                  if (onNavigate) {
                    e.preventDefault();
                    onNavigate();
                  }
                }}
              >
                {item.icon}
                <span className="flex-1 text-left">{item.label}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </Link>
            </Button>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex h-full flex-col border-r bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      {/* Brand */}
      <div className="flex h-16 items-center gap-3 px-6">
        <Link href="/" className="flex items-center gap-3">
          <div className="h-9 w-9 rounded-2xl bg-primary shadow-soft text-white grid place-items-center font-bold">O</div>
          <span className="text-base font-semibold">OneCRM</span>
        </Link>
      </div>

      <Separator />

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto px-3 py-4">
        <nav className="space-y-1">
          {navigationItems.map(item => renderNavigationItem(item))}
        </nav>
      </div>

      <Separator />

      {/* Bottom navigation */}
      <div className="p-3 space-y-1">
        {bottomNavigationItems.map(item => renderNavigationItem(item))}
      </div>
    </div>
  )
}
