'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Bell, Search, ChevronDown } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface NavbarProps {
  className?: string
}

export function Navbar({ className }: NavbarProps) {
  const pathname = usePathname()

  return (
    <header
      className={cn(
        'sticky top-0 z-40 w-full border-b bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60',
        className
      )}
    >
      <div className="mx-auto flex h-16 w-full max-w-screen-2xl items-center gap-3 px-4">
        {/* Left: Section breadcrumb / title */}
        <div className="hidden md:flex items-center text-sm text-muted-foreground">
          <Link href="/" className="hover:text-foreground transition-colors">Home</Link>
          <span className="mx-2">/</span>
          <span className="font-medium text-foreground">
            {pathname === '/' ? 'Dashboard' : pathname.replace('/', '').split('/')[0] || 'Dashboard'}
          </span>
        </div>

        {/* Center: Global search */}
        <div className="flex-1 max-w-xl">
          <Input
            placeholder="Search contacts, companies, deals..."
            leftIcon={<Search className="h-4 w-4" />}
            className="w-full bg-white/80"
            aria-label="Global search"
          />
        </div>

        {/* Right: Actions */}
        <div className="ml-auto flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            aria-label="Notifications"
          >
            <Bell className="h-5 w-5" />
            <Badge
              variant="secondary"
              className="absolute -right-1 -top-1 h-5 min-w-[20px] rounded-full px-1 py-0 text-[10px] leading-5"
            >
              3
            </Badge>
          </Button>

          <Button
            variant="outline"
            className="hidden sm:flex items-center gap-2"
            aria-label="Quick actions"
          >
            New
            <ChevronDown className="h-4 w-4" />
          </Button>

          <Button asChild variant="ghost" className="p-0">
            <Link href="/settings" aria-label="Profile settings">
              <div className="flex items-center gap-2 pl-2 pr-3">
                <Avatar className="h-9 w-9">
                  <AvatarImage alt="User avatar" />
                  <AvatarFallback>OC</AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium leading-tight">OneCRM User</div>
                  <div className="text-xs text-muted-foreground leading-tight">Admin</div>
                </div>
              </div>
            </Link>
          </Button>
        </div>
      </div>
    </header>
  )
}

export default Navbar