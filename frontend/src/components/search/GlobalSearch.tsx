'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  Chip,
  Divider,
  CircularProgress,
  IconButton,
  Popper,
  ClickAwayListener,
  Fade,
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  TrendingUp as DealsIcon,
  Assignment as ActivityIcon,
  Close as CloseIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import useSWR from 'swr';
import { useDebounce } from '../../hooks/useDebounce';

interface SearchResult {
  id: string;
  type: 'contact' | 'company' | 'deal' | 'activity';
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: string;
  metadata?: Record<string, any>;
}

interface GlobalSearchProps {
  placeholder?: string;
  size?: 'small' | 'medium';
  fullWidth?: boolean;
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'contact':
      return <PersonIcon />;
    case 'company':
      return <BusinessIcon />;
    case 'deal':
      return <DealsIcon />;
    case 'activity':
      return <ActivityIcon />;
    default:
      return <SearchIcon />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'contact':
      return 'primary';
    case 'company':
      return 'secondary';
    case 'deal':
      return 'success';
    case 'activity':
      return 'warning';
    default:
      return 'default';
  }
};

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'contact':
      return 'Contact';
    case 'company':
      return 'Company';
    case 'deal':
      return 'Deal';
    case 'activity':
      return 'Activity';
    default:
      return type;
  }
};

const formatCurrency = (amount?: number) => {
  if (!amount) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const GlobalSearch: React.FC<GlobalSearchProps> = ({
  placeholder = 'Search contacts, companies, deals...',
  size = 'medium',
  fullWidth = false,
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const debouncedQuery = useDebounce(query, 300);

  // Fetch search results
  const { data: searchResults, isLoading } = useSWR(
    debouncedQuery.length >= 2 ? `/api/search?q=${encodeURIComponent(debouncedQuery)}` : null,
    { revalidateOnFocus: false }
  );

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('onecrm-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = (searchQuery: string) => {
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('onecrm-recent-searches', JSON.stringify(updated));
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    
    if (value.length >= 2) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };

  const handleInputFocus = () => {
    if (query.length >= 2 || recentSearches.length > 0) {
      setIsOpen(true);
    }
  };

  const handleClickAway = () => {
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  const handleResultClick = (result: SearchResult) => {
    saveRecentSearch(query);
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);

    // Navigate to the appropriate page
    switch (result.type) {
      case 'contact':
        router.push(`/contacts?id=${result.id}`);
        break;
      case 'company':
        router.push(`/companies?id=${result.id}`);
        break;
      case 'deal':
        router.push(`/deals?id=${result.id}`);
        break;
      case 'activity':
        router.push(`/activities?id=${result.id}`);
        break;
    }
  };

  const handleRecentSearchClick = (searchQuery: string) => {
    setQuery(searchQuery);
    setIsOpen(true);
    inputRef.current?.focus();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('onecrm-recent-searches');
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isOpen) return;

    const results = searchResults?.results || [];
    const maxIndex = results.length - 1;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => (prev < maxIndex ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : maxIndex));
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  const renderSearchResults = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={2}>
          <CircularProgress size={24} />
        </Box>
      );
    }

    if (debouncedQuery.length >= 2 && searchResults?.results?.length === 0) {
      return (
        <Box p={2} textAlign="center">
          <Typography variant="body2" color="text.secondary">
            No results found for "{debouncedQuery}"
          </Typography>
        </Box>
      );
    }

    if (debouncedQuery.length < 2 && recentSearches.length > 0) {
      return (
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center" px={2} py={1}>
            <Typography variant="caption" color="text.secondary">
              Recent Searches
            </Typography>
            <IconButton size="small" onClick={clearRecentSearches}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          <List dense>
            {recentSearches.map((search, index) => (
              <ListItem
                key={index}
                button
                onClick={() => handleRecentSearchClick(search)}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'action.hover', width: 32, height: 32 }}>
                    <HistoryIcon fontSize="small" />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText primary={search} />
              </ListItem>
            ))}
          </List>
        </>
      );
    }

    if (searchResults?.results?.length > 0) {
      const groupedResults = searchResults.results.reduce((acc: Record<string, SearchResult[]>, result: SearchResult) => {
        if (!acc[result.type]) {
          acc[result.type] = [];
        }
        acc[result.type]?.push(result);
        return acc;
      }, {});

      return (
        <>
          {Object.entries(groupedResults).map(([type, results], groupIndex) => (
            <React.Fragment key={type}>
              {groupIndex > 0 && <Divider />}
              <Box px={2} py={1}>
                <Typography variant="caption" color="text.secondary" fontWeight={600}>
                  {getTypeLabel(type)}s ({(results as any[]).length})
                </Typography>
              </Box>
              <List dense>
                {(results as any[]).map((result: any, index: number) => {
                  const globalIndex = Object.values(groupedResults)
                    .slice(0, groupIndex)
                    .reduce((sum: number, group: any) => sum + group.length, 0) + index;
                  
                  return (
                    <ListItem
                      key={result.id}
                      button
                      selected={selectedIndex === globalIndex}
                      onClick={() => handleResultClick(result)}
                      sx={{
                        '&.Mui-selected': {
                          backgroundColor: 'action.selected',
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: `${getTypeColor(result.type)}.main`, width: 32, height: 32 }}>
                          {getTypeIcon(result.type)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2" fontWeight={600}>
                              {result.title}
                            </Typography>
                            <Chip
                              label={getTypeLabel(result.type)}
                              size="small"
                              color={getTypeColor(result.type) as any}
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 18 }}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            {result.subtitle && (
                              <Typography variant="caption" color="text.secondary">
                                {result.subtitle}
                              </Typography>
                            )}
                            {result.description && (
                              <Typography variant="caption" color="text.secondary" display="block">
                                {result.description}
                              </Typography>
                            )}
                            {result.metadata?.amount && (
                              <Typography variant="caption" color="success.main" fontWeight={600}>
                                {formatCurrency(result.metadata.amount)}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  );
                })}
              </List>
            </React.Fragment>
          ))}
          
          {searchResults.total > searchResults.results.length && (
            <Box p={2} textAlign="center">
              <Typography variant="caption" color="text.secondary">
                Showing {searchResults.results.length} of {searchResults.total} results
              </Typography>
            </Box>
          )}
        </>
      );
    }

    return null;
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box ref={searchRef} sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>
        <TextField
          ref={inputRef}
          fullWidth={fullWidth}
          size={size}
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: query && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={clearSearch}>
                  <CloseIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'background.paper',
            },
          }}
        />

        <Popper
          open={isOpen}
          anchorEl={searchRef.current}
          placement="bottom-start"
          style={{ width: searchRef.current?.offsetWidth, zIndex: 1300 }}
          transition
        >
          {({ TransitionProps }) => (
            <Fade {...TransitionProps} timeout={200}>
              <Paper
                elevation={8}
                sx={{
                  mt: 1,
                  maxHeight: 400,
                  overflow: 'auto',
                  border: 1,
                  borderColor: 'divider',
                }}
              >
                {renderSearchResults()}
              </Paper>
            </Fade>
          )}
        </Popper>
      </Box>
    </ClickAwayListener>
  );
};
