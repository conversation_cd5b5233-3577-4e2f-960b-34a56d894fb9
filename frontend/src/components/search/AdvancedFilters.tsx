'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Chip,
  Button,
  IconButton,
  Collapse,
  Typography,
  Divider,
  Switch,
  FormControlLabel,
  Slider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
// Date picker imports removed - using TextField instead
import useSWR from 'swr';

interface AdvancedFiltersProps {
  entityType: 'contacts' | 'companies' | 'deals' | 'activities';
  filters: Record<string, any>;
  onChange: (filters: Record<string, any>) => void;
  onSave?: (filterName: string, filters: Record<string, any>) => void;
}

interface FilterSection {
  title: string;
  expanded: boolean;
  fields: FilterField[];
}

interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'range' | 'boolean';
  options?: { value: string; label: string }[];
  min?: number;
  max?: number;
  step?: number;
}

const getFilterSections = (entityType: string): FilterSection[] => {
  switch (entityType) {
    case 'contacts':
      return [
        {
          title: 'Basic Information',
          expanded: true,
          fields: [
            { key: 'leadStatus', label: 'Lead Status', type: 'select', options: [
              { value: 'new', label: 'New' },
              { value: 'contacted', label: 'Contacted' },
              { value: 'qualified', label: 'Qualified' },
              { value: 'unqualified', label: 'Unqualified' },
              { value: 'customer', label: 'Customer' },
              { value: 'lost', label: 'Lost' },
            ]},
            { key: 'leadSource', label: 'Lead Source', type: 'select', options: [
              { value: 'website', label: 'Website' },
              { value: 'referral', label: 'Referral' },
              { value: 'social-media', label: 'Social Media' },
              { value: 'email-campaign', label: 'Email Campaign' },
              { value: 'cold-call', label: 'Cold Call' },
              { value: 'trade-show', label: 'Trade Show' },
              { value: 'partner', label: 'Partner' },
              { value: 'other', label: 'Other' },
            ]},
            { key: 'tags', label: 'Tags', type: 'multiselect' },
          ],
        },
        {
          title: 'Assignment & Dates',
          expanded: false,
          fields: [
            { key: 'assignedToId', label: 'Assigned To', type: 'select' },
            { key: 'createdDateFrom', label: 'Created From', type: 'date' },
            { key: 'createdDateTo', label: 'Created To', type: 'date' },
            { key: 'hasEmail', label: 'Has Email', type: 'boolean' },
            { key: 'hasPhone', label: 'Has Phone', type: 'boolean' },
          ],
        },
      ];

    case 'companies':
      return [
        {
          title: 'Company Details',
          expanded: true,
          fields: [
            { key: 'industry', label: 'Industry', type: 'select' },
            { key: 'size', label: 'Company Size', type: 'select', options: [
              { value: 'startup', label: 'Startup (1-10)' },
              { value: 'small', label: 'Small (11-50)' },
              { value: 'medium', label: 'Medium (51-200)' },
              { value: 'large', label: 'Large (201-1000)' },
              { value: 'enterprise', label: 'Enterprise (1000+)' },
            ]},
            { key: 'tags', label: 'Tags', type: 'multiselect' },
          ],
        },
        {
          title: 'Financial Information',
          expanded: false,
          fields: [
            { key: 'revenueRange', label: 'Annual Revenue', type: 'range', min: 0, max: *********, step: 1000000 },
            { key: 'employeeRange', label: 'Employee Count', type: 'range', min: 1, max: 10000, step: 10 },
          ],
        },
      ];

    case 'deals':
      return [
        {
          title: 'Deal Information',
          expanded: true,
          fields: [
            { key: 'stage', label: 'Stage', type: 'select', options: [
              { value: 'lead', label: 'Lead' },
              { value: 'qualification', label: 'Qualification' },
              { value: 'proposal', label: 'Proposal' },
              { value: 'negotiation', label: 'Negotiation' },
              { value: 'closed-won', label: 'Closed Won' },
              { value: 'closed-lost', label: 'Closed Lost' },
            ]},
            { key: 'priority', label: 'Priority', type: 'select', options: [
              { value: 'low', label: 'Low' },
              { value: 'medium', label: 'Medium' },
              { value: 'high', label: 'High' },
              { value: 'urgent', label: 'Urgent' },
            ]},
            { key: 'source', label: 'Deal Source', type: 'text' },
            { key: 'type', label: 'Deal Type', type: 'text' },
          ],
        },
        {
          title: 'Financial & Timeline',
          expanded: false,
          fields: [
            { key: 'amountRange', label: 'Deal Amount', type: 'range', min: 0, max: 1000000, step: 1000 },
            { key: 'probabilityRange', label: 'Probability', type: 'range', min: 0, max: 100, step: 5 },
            { key: 'expectedCloseDateFrom', label: 'Expected Close From', type: 'date' },
            { key: 'expectedCloseDateTo', label: 'Expected Close To', type: 'date' },
          ],
        },
      ];

    case 'activities':
      return [
        {
          title: 'Activity Details',
          expanded: true,
          fields: [
            { key: 'type', label: 'Activity Type', type: 'select', options: [
              { value: 'call', label: 'Call' },
              { value: 'email', label: 'Email' },
              { value: 'meeting', label: 'Meeting' },
              { value: 'task', label: 'Task' },
              { value: 'note', label: 'Note' },
              { value: 'demo', label: 'Demo' },
              { value: 'follow-up', label: 'Follow-up' },
            ]},
            { key: 'status', label: 'Status', type: 'select', options: [
              { value: 'pending', label: 'Pending' },
              { value: 'in-progress', label: 'In Progress' },
              { value: 'completed', label: 'Completed' },
              { value: 'cancelled', label: 'Cancelled' },
              { value: 'overdue', label: 'Overdue' },
            ]},
            { key: 'priority', label: 'Priority', type: 'select', options: [
              { value: 'low', label: 'Low' },
              { value: 'medium', label: 'Medium' },
              { value: 'high', label: 'High' },
              { value: 'urgent', label: 'Urgent' },
            ]},
          ],
        },
        {
          title: 'Timeline & Assignment',
          expanded: false,
          fields: [
            { key: 'dueDateFrom', label: 'Due Date From', type: 'date' },
            { key: 'dueDateTo', label: 'Due Date To', type: 'date' },
            { key: 'assignedToId', label: 'Assigned To', type: 'select' },
            { key: 'isCompleted', label: 'Completed', type: 'boolean' },
            { key: 'isOverdue', label: 'Overdue', type: 'boolean' },
          ],
        },
      ];

    default:
      return [];
  }
};

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  entityType,
  filters,
  onChange,
  onSave,
}) => {
  const [sections, setSections] = useState(() => getFilterSections(entityType));
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [filterName, setFilterName] = useState('');

  const { data: users } = useSWR('/api/users/organization');
  const { data: tags } = useSWR(`/api/${entityType}/tags`);

  const toggleSection = (index: number) => {
    setSections(prev => prev.map((section, i) => 
      i === index ? { ...section, expanded: !section.expanded } : section
    ));
  };

  const handleFieldChange = (key: string, value: any) => {
    onChange({ ...filters, [key]: value });
  };

  const clearFilters = () => {
    onChange({});
  };

  const renderField = (field: FilterField) => {
    const value = filters[field.key];

    switch (field.type) {
      case 'text':
        return (
          <TextField
            fullWidth
            size="small"
            label={field.label}
            value={value || ''}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
          />
        );

      case 'select':
        const options = field.key === 'assignedToId' ? 
          users?.map((user: any) => ({ value: user.id, label: `${user.firstName} ${user.lastName}` })) || [] :
          field.options || [];

        return (
          <FormControl fullWidth size="small">
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={value || ''}
              label={field.label}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
            >
              <MenuItem value="">All</MenuItem>
              {options.map((option: any) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'multiselect':
        const multiselectOptions = field.key === 'tags' ? 
          tags?.map((tag: string) => ({ value: tag, label: tag })) || [] :
          field.options || [];

        return (
          <Autocomplete
            multiple
            size="small"
            options={multiselectOptions.map((opt: any) => opt.value)}
            value={value || []}
            onChange={(_, newValue) => handleFieldChange(field.key, newValue)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  size="small"
                  {...getTagProps({ index })}
                  key={option}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...(params as any)}
                label={field.label}
                placeholder={`Select ${field.label.toLowerCase()}...`}
                size="medium"
              />
            )}
          />
        );

      case 'date':
        return (
          <TextField
            label={field.label}
            type="date"
            value={value || ''}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
            size="small"
            fullWidth
            InputLabelProps={{ shrink: true }}
          />
        );

      case 'range':
        const rangeValue = value || [field.min || 0, field.max || 100];
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              {field.label}: {rangeValue[0].toLocaleString()} - {rangeValue[1].toLocaleString()}
            </Typography>
            <Slider
              value={rangeValue}
              onChange={(_, newValue) => handleFieldChange(field.key, newValue)}
              valueLabelDisplay="auto"
              min={field.min || 0}
              max={field.max || 100}
              step={field.step || 1}
              valueLabelFormat={(value) => value.toLocaleString()}
            />
          </Box>
        );

      case 'boolean':
        return (
          <FormControlLabel
            control={
              <Switch
                checked={value || false}
                onChange={(e) => handleFieldChange(field.key, e.target.checked)}
              />
            }
            label={field.label}
          />
        );

      default:
        return null;
    }
  };

  const activeFiltersCount = Object.keys(filters).filter(key => {
    const value = filters[key];
    return value !== undefined && value !== null && value !== '' && 
           (Array.isArray(value) ? value.length > 0 : true);
  }).length;

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <FilterIcon />
            <Typography variant="h6">Advanced Filters</Typography>
            {activeFiltersCount > 0 && (
              <Chip
                label={`${activeFiltersCount} active`}
                size="small"
                color="primary"
              />
            )}
          </Box>
        }
        action={
          <Box display="flex" gap={1}>
            {onSave && (
              <Button
                size="small"
                startIcon={<SaveIcon />}
                onClick={() => setSaveDialogOpen(true)}
                disabled={activeFiltersCount === 0}
              >
                Save
              </Button>
            )}
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
            >
              Clear
            </Button>
          </Box>
        }
      />
      <CardContent>
        {sections.map((section, index) => (
          <Box key={section.title} mb={2}>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{ cursor: 'pointer' }}
              onClick={() => toggleSection(index)}
              mb={1}
            >
              <Typography variant="subtitle2" fontWeight={600}>
                {section.title}
              </Typography>
              <IconButton size="small">
                {section.expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>
            
            <Collapse in={section.expanded}>
              <Grid container spacing={2}>
                {section.fields.map((field) => (
                  <Grid item xs={12} sm={6} md={4} key={field.key}>
                    {renderField(field)}
                  </Grid>
                ))}
              </Grid>
            </Collapse>
            
            {index < sections.length - 1 && <Divider sx={{ mt: 2 }} />}
          </Box>
        ))}
      </CardContent>
    </Card>
  );
};
