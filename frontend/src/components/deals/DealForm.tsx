'use client';

import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  MenuItem,
  Box,
  Slider,
  Typography,
} from '@mui/material';
import { useNotifications } from '../common/NotificationSystem';

interface DealFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  initialData?: any;
  deal?: any;
  loading?: boolean;
}

const stages = [
  'Prospecting',
  'Qualification',
  'Proposal',
  'Negotiation',
  'Closed Won',
  'Closed Lost',
];

export const DealForm: React.FC<DealFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  deal,
  loading = false,
}) => {
  const { success, error } = useNotifications();
  const [formData, setFormData] = useState({
    title: initialData?.title || deal?.title || '',
    value: initialData?.value || deal?.value || '',
    stage: initialData?.stage || deal?.stage || 'Prospecting',
    probability: initialData?.probability || deal?.probability || 50,
    expectedCloseDate: initialData?.expectedCloseDate || deal?.expectedCloseDate || '',
    description: initialData?.description || deal?.description || '',
    contactId: initialData?.contactId || deal?.contactId || '',
    companyId: initialData?.companyId || deal?.companyId || '',
    ...initialData,
    ...deal,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: event.target.value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleProbabilityChange = (event: Event, newValue: number | number[]) => {
    setFormData((prev: any) => ({
      ...prev,
      probability: newValue as number,
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Deal title is required';
    }

    if (!formData.value || isNaN(Number(formData.value))) {
      newErrors.value = 'Please enter a valid deal value';
    }

    if (!formData.stage) {
      newErrors.stage = 'Stage is required';
    }

    if (!formData.expectedCloseDate) {
      newErrors.expectedCloseDate = 'Expected close date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      error('Please fix the form errors');
      return;
    }

    const submitData = {
      ...formData,
      value: Number(formData.value),
    };

    if (onSubmit) {
      onSubmit(submitData);
    }
  };

  const handleClose = () => {
    setFormData({
      title: '',
      value: '',
      stage: 'Prospecting',
      probability: 50,
      expectedCloseDate: '',
      description: '',
      contactId: '',
      companyId: '',
    });
    setErrors({});
    onClose();
  };

  const getProbabilityColor = (value: number) => {
    if (value >= 80) return 'success';
    if (value >= 60) return 'warning';
    if (value >= 40) return 'info';
    return 'error';
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {initialData ? 'Edit Deal' : 'Add New Deal'}
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Deal Title"
                  value={formData.title}
                  onChange={handleChange('title')}
                  error={!!errors.title}
                  helperText={errors.title}
                  required
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Deal Value (USD)"
                  type="number"
                  value={formData.value}
                  onChange={handleChange('value')}
                  error={!!errors.value}
                  helperText={errors.value}
                  required
                  InputProps={{
                    startAdornment: '$',
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  select
                  label="Stage"
                  value={formData.stage}
                  onChange={handleChange('stage')}
                  error={!!errors.stage}
                  helperText={errors.stage}
                  required
                >
                  {stages.map((stage) => (
                    <MenuItem key={stage} value={stage}>
                      {stage}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Expected Close Date"
                  type="date"
                  value={formData.expectedCloseDate}
                  onChange={handleChange('expectedCloseDate')}
                  error={!!errors.expectedCloseDate}
                  helperText={errors.expectedCloseDate}
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography gutterBottom>
                  Probability: {formData.probability}%
                </Typography>
                <Slider
                  value={formData.probability}
                  onChange={handleProbabilityChange}
                  min={0}
                  max={100}
                  step={5}
                  marks={[
                    { value: 0, label: '0%' },
                    { value: 25, label: '25%' },
                    { value: 50, label: '50%' },
                    { value: 75, label: '75%' },
                    { value: 100, label: '100%' },
                  ]}
                  color={getProbabilityColor(formData.probability)}
                  sx={{ mt: 2 }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Contact ID"
                  value={formData.contactId}
                  onChange={handleChange('contactId')}
                  helperText="Optional: Link to a contact"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Company ID"
                  value={formData.companyId}
                  onChange={handleChange('companyId')}
                  helperText="Optional: Link to a company"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Description"
                  value={formData.description}
                  onChange={handleChange('description')}
                  placeholder="Add notes about this deal..."
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading}
          >
            {loading ? 'Saving...' : (initialData ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
