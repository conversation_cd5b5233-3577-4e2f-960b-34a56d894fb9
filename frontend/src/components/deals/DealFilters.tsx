'use client';

import React, { useState } from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Button,
  Grid,
  Paper,
  Typography,
  Chip,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  FilterList,
  Clear,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';

interface DealFiltersProps {
  onFiltersChange?: (filters: any) => void;
  onChange?: (filters: any) => void;
  filters?: any;
  loading?: boolean;
}

const stages = [
  'All Stages',
  'Prospecting',
  'Qualification',
  'Proposal',
  'Negotiation',
  'Closed Won',
  'Closed Lost',
];

const valueRanges = [
  { label: 'All Values', value: '' },
  { label: 'Under $10K', value: '0-10000' },
  { label: '$10K - $50K', value: '10000-50000' },
  { label: '$50K - $100K', value: '50000-100000' },
  { label: '$100K+', value: '100000+' },
];

const probabilityRanges = [
  { label: 'All Probabilities', value: '' },
  { label: '0-25%', value: '0-25' },
  { label: '26-50%', value: '26-50' },
  { label: '51-75%', value: '51-75' },
  { label: '76-100%', value: '76-100' },
];

export const DealFilters: React.FC<DealFiltersProps> = ({
  onFiltersChange,
  onChange,
  filters: externalFilters,
  loading = false,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    stage: '',
    valueRange: '',
    probabilityRange: '',
    assignedTo: '',
    expectedCloseDate: '',
  });

  const handleFilterChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...filters,
      [field]: event.target.value,
    };
    setFilters(newFilters);
    if (onFiltersChange) onFiltersChange(newFilters);
    if (onChange) onChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      stage: '',
      valueRange: '',
      probabilityRange: '',
      assignedTo: '',
      expectedCloseDate: '',
    };
    setFilters(clearedFilters);
    if (onFiltersChange) onFiltersChange(clearedFilters);
    if (onChange) onChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== '').length;
  };

  return (
    <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FilterList />
          <Typography variant="h6">Deal Filters</Typography>
          {getActiveFiltersCount() > 0 && (
            <Chip 
              label={`${getActiveFiltersCount()} active`} 
              size="small" 
              color="primary" 
            />
          )}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getActiveFiltersCount() > 0 && (
            <Button
              size="small"
              startIcon={<Clear />}
              onClick={clearFilters}
              disabled={loading}
            >
              Clear All
            </Button>
          )}
          <IconButton
            onClick={() => setExpanded(!expanded)}
            disabled={loading}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>
      </Box>

      {/* Always visible search */}
      <TextField
        fullWidth
        placeholder="Search deals..."
        value={filters.search}
        onChange={handleFilterChange('search')}
        disabled={loading}
        sx={{ mb: 2 }}
      />

      {/* Expandable advanced filters */}
      <Collapse in={expanded}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Stage"
              value={filters.stage}
              onChange={handleFilterChange('stage')}
              disabled={loading}
            >
              {stages.map((stage) => (
                <MenuItem key={stage} value={stage === 'All Stages' ? '' : stage}>
                  {stage}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Deal Value"
              value={filters.valueRange}
              onChange={handleFilterChange('valueRange')}
              disabled={loading}
            >
              {valueRanges.map((range) => (
                <MenuItem key={range.value} value={range.value}>
                  {range.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Probability"
              value={filters.probabilityRange}
              onChange={handleFilterChange('probabilityRange')}
              disabled={loading}
            >
              {probabilityRanges.map((range) => (
                <MenuItem key={range.value} value={range.value}>
                  {range.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="Expected Close Date"
              type="date"
              value={filters.expectedCloseDate}
              onChange={handleFilterChange('expectedCloseDate')}
              disabled={loading}
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
        </Grid>
      </Collapse>
    </Paper>
  );
};
