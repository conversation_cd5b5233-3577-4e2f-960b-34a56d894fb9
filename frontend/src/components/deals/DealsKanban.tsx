'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  Tooltip,
  Skeleton,
  Button,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AttachMoney as MoneyIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface Deal {
  id: string;
  title: string;
  amount: number;
  stage: string;
  probability: number;
  expectedCloseDate?: string;
  owner?: {
    firstName: string;
    lastName: string;
  };
  contact?: {
    firstName: string;
    lastName: string;
  };
  company?: {
    name: string;
  };
  priority?: string;
}

interface DealsKanbanProps {
  searchQuery: string;
  filters: any;
  onEditDeal: (deal: Deal) => void;
}

const stageColors: Record<string, string> = {
  lead: '#94a3b8',
  qualification: '#3b82f6',
  proposal: '#f59e0b',
  negotiation: '#ef4444',
  'closed-won': '#22c55e',
  'closed-lost': '#6b7280',
};

const stageLabels: Record<string, string> = {
  lead: 'Lead',
  qualification: 'Qualification',
  proposal: 'Proposal',
  negotiation: 'Negotiation',
  'closed-won': 'Closed Won',
  'closed-lost': 'Closed Lost',
};

const priorityColors: Record<string, string> = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error',
};

const DealCard: React.FC<{ deal: Deal; onEdit: (deal: Deal) => void }> = ({ deal, onEdit }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(deal);
  };

  const handleDelete = () => {
    handleMenuClose();
    console.log('Delete deal:', deal.id);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card
      sx={{
        mb: 2,
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          boxShadow: 4,
          transform: 'translateY(-2px)',
        },
      }}
      onClick={() => onEdit(deal)}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Typography variant="subtitle2" fontWeight={600} sx={{ flex: 1, mr: 1 }}>
            {deal.title}
          </Typography>
          
          <IconButton size="small" onClick={handleMenuOpen}>
            <MoreVertIcon fontSize="small" />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            onClick={(e) => e.stopPropagation()}
          >
            <MenuItem onClick={handleEdit}>
              <EditIcon sx={{ mr: 1 }} fontSize="small" />
              Edit
            </MenuItem>
            <MenuItem onClick={handleDelete}>
              <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
              Delete
            </MenuItem>
          </Menu>
        </Box>

        <Typography variant="h6" color="primary" gutterBottom>
          {formatCurrency(deal.amount)}
        </Typography>

        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <LinearProgress
            variant="determinate"
            value={deal.probability}
            sx={{ flex: 1, height: 6, borderRadius: 3 }}
          />
          <Typography variant="caption" color="text.secondary">
            {deal.probability}%
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
          {deal.priority && (
            <Chip
              label={deal.priority}
              size="small"
              color={priorityColors[deal.priority] as any}
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
          
          {deal.expectedCloseDate && (
            <Typography variant="caption" color="text.secondary">
              Due: {formatDate(deal.expectedCloseDate)}
            </Typography>
          )}
        </Box>

        <Box display="flex" alignItems="center" gap={1} mt={2}>
          {deal.owner && (
            <Tooltip title={`Owner: ${deal.owner.firstName} ${deal.owner.lastName}`}>
              <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                {deal.owner.firstName[0]}{deal.owner.lastName[0]}
              </Avatar>
            </Tooltip>
          )}
          
          {deal.contact && (
            <Box display="flex" alignItems="center" gap={0.5}>
              <PersonIcon fontSize="small" color="action" />
              <Typography variant="caption" color="text.secondary">
                {deal.contact.firstName} {deal.contact.lastName}
              </Typography>
            </Box>
          )}
        </Box>

        {deal.company && (
          <Box display="flex" alignItems="center" gap={0.5} mt={1}>
            <BusinessIcon fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary">
              {deal.company.name}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

const KanbanColumn: React.FC<{
  stage: string;
  deals: Deal[];
  isLoading: boolean;
  onEditDeal: (deal: Deal) => void;
}> = ({ stage, deals, isLoading, onEditDeal }) => {
  const stageValue = deals.reduce((sum, deal) => sum + deal.amount, 0);
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <Card sx={{ height: 'fit-content', minHeight: 400 }}>
        <CardHeader
          title={<Skeleton variant="text" width={120} />}
          subheader={<Skeleton variant="text" width={80} />}
        />
        <CardContent>
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} variant="rectangular" height={120} sx={{ mb: 2, borderRadius: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 'fit-content', minHeight: 400 }}>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                bgcolor: stageColors[stage] || '#94a3b8',
              }}
            />
            <Typography variant="h6" fontWeight={600}>
              {stageLabels[stage] || stage}
            </Typography>
            <Chip
              label={deals.length}
              size="small"
              sx={{ ml: 'auto' }}
            />
          </Box>
        }
        subheader={
          <Typography variant="body2" color="text.secondary">
            {formatCurrency(stageValue)} total
          </Typography>
        }
        sx={{ pb: 1 }}
      />
      <CardContent sx={{ pt: 0, maxHeight: 600, overflow: 'auto' }}>
        {deals.length === 0 ? (
          <Box textAlign="center" py={4}>
            <TrendingUpIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              No deals in this stage
            </Typography>
          </Box>
        ) : (
          deals.map((deal) => (
            <DealCard key={deal.id} deal={deal} onEdit={onEditDeal} />
          ))
        )}
      </CardContent>
    </Card>
  );
};

export const DealsKanban: React.FC<DealsKanbanProps> = ({
  searchQuery,
  filters,
  onEditDeal,
}) => {
  const { data: pipelineData, isLoading } = useSWR('/api/deals/pipeline');

  const stages = ['lead', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost'];

  // Filter deals based on search and filters
  const filterDeals = (deals: Deal[]) => {
    return deals.filter((deal) => {
      // Search filter
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        const matchesSearch = 
          deal.title.toLowerCase().includes(searchLower) ||
          deal.contact?.firstName?.toLowerCase().includes(searchLower) ||
          deal.contact?.lastName?.toLowerCase().includes(searchLower) ||
          deal.company?.name?.toLowerCase().includes(searchLower);
        
        if (!matchesSearch) return false;
      }

      // Other filters
      if (filters.ownerId && (deal.owner as any)?.id !== filters.ownerId) return false;
      if (filters.priority && deal.priority !== filters.priority) return false;
      // Add more filter logic as needed

      return true;
    });
  };

  return (
    <Grid container spacing={2}>
      {stages.map((stage) => {
        const stageData = pipelineData?.pipeline?.find((p: any) => p.stage === stage);
        const deals = stageData ? filterDeals(stageData.deals || []) : [];

        return (
          <Grid item xs={12} sm={6} md={4} lg={2} key={stage}>
            <KanbanColumn
              stage={stage}
              deals={deals}
              isLoading={isLoading}
              onEditDeal={onEditDeal}
            />
          </Grid>
        );
      })}
    </Grid>
  );
};
