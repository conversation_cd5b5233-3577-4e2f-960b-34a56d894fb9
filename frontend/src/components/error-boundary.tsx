'use client'

import React from 'react'
import { Al<PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Home, Bug } from 'lucide-react'
import { EnhancedButton } from './ui/enhanced-button'
import { EnhancedCard } from './ui/enhanced-card'
import { Alert, AlertDescription } from './ui/alert'

interface ErrorInfo {
  componentStack: string
  errorBoundary?: string
  errorBoundaryStack?: string
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  isolate?: boolean
  level?: 'page' | 'section' | 'component'
}

interface ErrorFallbackProps {
  error: Error | null
  errorInfo: ErrorInfo | null
  resetError: () => void
  errorId: string | null
  level: 'page' | 'section' | 'component'
}

// Error reporting service
class ErrorReportingService {
  private static instance: ErrorReportingService
  private errors: Array<{ id: string; error: Error; errorInfo: ErrorInfo; timestamp: Date }> = []

  static getInstance(): ErrorReportingService {
    if (!ErrorReportingService.instance) {
      ErrorReportingService.instance = new ErrorReportingService()
    }
    return ErrorReportingService.instance
  }

  reportError(error: Error, errorInfo: ErrorInfo): string {
    const errorId = this.generateErrorId()
    const errorReport = {
      id: errorId,
      error,
      errorInfo,
      timestamp: new Date(),
    }

    this.errors.push(errorReport)
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorService(errorReport)
    } else {
      console.group(`🚨 Error Report [${errorId}]`)
      console.error('Error:', error)
      console.error('Component Stack:', errorInfo.componentStack)
      console.error('Error Boundary Stack:', errorInfo.errorBoundaryStack)
      console.groupEnd()
    }

    return errorId
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async sendToErrorService(errorReport: any) {
    try {
      // Replace with your error reporting service
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: errorReport.id,
          message: errorReport.error.message,
          stack: errorReport.error.stack,
          componentStack: errorReport.errorInfo.componentStack,
          timestamp: errorReport.timestamp.toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      })
    } catch (err) {
      console.error('Failed to report error:', err)
    }
  }

  getRecentErrors(limit: number = 10) {
    return this.errors.slice(-limit)
  }
}

const errorReporter = ErrorReportingService.getInstance()

// Default error fallback components
function PageErrorFallback({ error, errorInfo, resetError, errorId }: ErrorFallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <EnhancedCard className="max-w-2xl w-full" variant="outlined">
        <div className="text-center space-y-6 p-8">
          <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>
          
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-foreground">Something went wrong</h1>
            <p className="text-muted-foreground">
              We encountered an unexpected error. Our team has been notified and is working on a fix.
            </p>
          </div>

          {process.env.NODE_ENV === 'development' && error && (
            <Alert variant="destructive">
              <Bug className="h-4 w-4" />
              <AlertDescription className="text-left">
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">Error Details (Development)</summary>
                  <div className="mt-2 space-y-2 text-xs">
                    <div>
                      <strong>Error:</strong> {error.message}
                    </div>
                    <div>
                      <strong>Error ID:</strong> {errorId}
                    </div>
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 whitespace-pre-wrap bg-muted p-2 rounded text-xs overflow-auto max-h-32">
                        {error.stack}
                      </pre>
                    </div>
                  </div>
                </details>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <EnhancedButton
              onClick={resetError}
              leftIcon={<RefreshCw className="w-4 h-4" />}
            >
              Try Again
            </EnhancedButton>
            <EnhancedButton
              variant="outline"
              onClick={() => window.location.href = '/'}
              leftIcon={<Home className="w-4 h-4" />}
            >
              Go Home
            </EnhancedButton>
          </div>

          {errorId && (
            <p className="text-xs text-muted-foreground">
              Error ID: {errorId} • Please include this ID when reporting the issue
            </p>
          )}
        </div>
      </EnhancedCard>
    </div>
  )
}

function SectionErrorFallback({ error, errorInfo, resetError, errorId }: ErrorFallbackProps) {
  return (
    <EnhancedCard variant="outlined" className="border-destructive/20">
      <div className="text-center space-y-4 p-6">
        <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-6 h-6 text-destructive" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Section Error</h3>
          <p className="text-sm text-muted-foreground">
            This section encountered an error and couldn't load properly.
          </p>
        </div>

        <div className="flex gap-2 justify-center">
          <EnhancedButton
            size="sm"
            onClick={resetError}
            leftIcon={<RefreshCw className="w-3 h-3" />}
          >
            Retry
          </EnhancedButton>
        </div>

        {errorId && (
          <p className="text-xs text-muted-foreground">Error ID: {errorId}</p>
        )}
      </div>
    </EnhancedCard>
  )
}

function ComponentErrorFallback({ error, errorInfo, resetError, errorId }: ErrorFallbackProps) {
  return (
    <div className="border border-destructive/20 rounded-md p-4 bg-destructive/5">
      <div className="flex items-center gap-2 text-sm">
        <AlertTriangle className="w-4 h-4 text-destructive flex-shrink-0" />
        <span className="text-destructive font-medium">Component Error</span>
        <EnhancedButton
          size="sm"
          variant="ghost"
          onClick={resetError}
          className="ml-auto h-6 px-2 text-xs"
        >
          Retry
        </EnhancedButton>
      </div>
      {errorId && (
        <p className="text-xs text-muted-foreground mt-1">ID: {errorId}</p>
      )}
    </div>
  )
}

const defaultFallbacks = {
  page: PageErrorFallback,
  section: SectionErrorFallback,
  component: ComponentErrorFallback,
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null

  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = errorReporter.reportError(error, errorInfo)
    
    this.setState({
      errorInfo,
      errorId,
    })

    this.props.onError?.(error, errorInfo)

    // Auto-retry for component-level errors
    if (this.props.level === 'component' && !this.props.isolate) {
      this.resetTimeoutId = window.setTimeout(() => {
        this.resetError()
      }, 5000)
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId)
    }
  }

  resetError = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId)
      this.resetTimeoutId = null
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    })
  }

  render() {
    if (this.state.hasError) {
      const level = this.props.level || 'page'
      const FallbackComponent = this.props.fallback || defaultFallbacks[level]

      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetError={this.resetError}
          errorId={this.state.errorId}
          level={level}
        />
      )
    }

    return this.props.children
  }
}

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Hook for error reporting
export function useErrorHandler() {
  const reportError = React.useCallback((error: Error, context?: string) => {
    const errorInfo: ErrorInfo = {
      componentStack: context || 'Manual error report',
    }
    return errorReporter.reportError(error, errorInfo)
  }, [])

  return { reportError }
}

// Global error handler setup
export function setupGlobalErrorHandling() {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = new Error(event.reason?.message || 'Unhandled Promise Rejection')
    error.stack = event.reason?.stack
    
    errorReporter.reportError(error, {
      componentStack: 'Unhandled Promise Rejection',
    })
  })

  // Handle global JavaScript errors
  window.addEventListener('error', (event) => {
    const error = new Error(event.message)
    error.stack = event.error?.stack
    
    errorReporter.reportError(error, {
      componentStack: `Global Error at ${event.filename}:${event.lineno}:${event.colno}`,
    })
  })
}

export { ErrorReportingService, type ErrorFallbackProps }
