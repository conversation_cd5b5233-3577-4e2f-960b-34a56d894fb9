'use client';

import React from 'react';
import { SWRConfig } from 'swr';
import { useAuth } from './MockAuthProvider';
import { useTenant } from './TenantProvider';

interface SWRProviderProps {
  children: React.ReactNode;
}

export const SWRProvider: React.FC<SWRProviderProps> = ({ children }) => {
  const auth = useAuth();
  const token = 'token' in auth ? auth.token : auth.getToken?.();
  const user = auth.user;

  const fetcher = async (url: string) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (user && 'organizationId' in user && user.organizationId) {
      headers['X-Org-Id'] = user.organizationId as string;
    } else if (user && 'orgId' in user && (user as any).orgId) {
      headers['X-Org-Id'] = (user as any).orgId;
    }

    const response = await fetch(url, { headers });

    if (!response.ok) {
      const error = new Error('An error occurred while fetching the data.');
      // Attach extra info to the error object
      (error as any).info = await response.json();
      (error as any).status = response.status;
      throw error;
    }

    return response.json();
  };

  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnFocus: false,
        revalidateOnReconnect: true,
        refreshInterval: 0,
        errorRetryCount: 3,
        errorRetryInterval: 5000,
        onError: (error, key) => {
          console.error('SWR Error:', error, 'Key:', key);
          
          // Handle authentication errors
          if (error.status === 401) {
            // Token might be expired, try to refresh
            console.warn('Authentication error, token might be expired');
          }
          
          // Handle tenant errors
          if (error.status === 403) {
            console.warn('Access denied, insufficient permissions');
          }
        },
        onErrorRetry: (error, key, config, revalidate, { retryCount }) => {
          // Never retry on 404
          if (error.status === 404) return;

          // Never retry on 401 (authentication errors)
          if (error.status === 401) return;

          // Never retry on 403 (authorization errors)
          if (error.status === 403) return;

          // Only retry up to 3 times
          if (retryCount >= 3) return;

          // Retry after 5 seconds
          setTimeout(() => revalidate({ retryCount }), 5000);
        },
      }}
    >
      {children}
    </SWRConfig>
  );
};
