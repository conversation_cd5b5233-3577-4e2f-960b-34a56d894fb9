'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { mockConfig, shouldUseMockAuth, logMockConfig } from '../../config/mock.config';

interface MockUser {
  id: string;
  email: string;
  name: string;
  roles: string[];
  organizationId: string;
  authenticated: boolean;
}

interface MockAuthContextType {
  user: MockUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email?: string, password?: string) => Promise<void>;
  logout: () => void;
  hasRole: (role: string) => boolean;
  getToken: () => string | null;
  refreshToken: () => Promise<string | null>;
}

const MockAuthContext = createContext<MockAuthContextType | undefined>(undefined);

interface MockAuthProviderProps {
  children: ReactNode;
}

export const MockAuthProvider: React.FC<MockAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<MockUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Log mock configuration on startup
    logMockConfig();

    // Auto-login if mock auth is enabled
    if (shouldUseMockAuth() && mockConfig.auth.autoLogin) {
      const mockUser: MockUser = {
        ...mockConfig.auth.mockUser,
        authenticated: true,
      };
      
      setUser(mockUser);
      
      // Store mock token
      localStorage.setItem('mock_token', generateMockToken(mockUser));
      
      console.log('🎭 Mock Auth: Auto-logged in as', mockUser.email);
    }
    
    setIsLoading(false);
  }, []);

  const login = async (email?: string, password?: string): Promise<void> => {
    setIsLoading(true);
    
    // Simulate login delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser: MockUser = {
      ...mockConfig.auth.mockUser,
      email: email || mockConfig.auth.mockUser.email,
      authenticated: true,
    };
    
    setUser(mockUser);
    localStorage.setItem('mock_token', generateMockToken(mockUser));
    
    console.log('🎭 Mock Auth: Logged in as', mockUser.email);
    setIsLoading(false);
  };

  const logout = (): void => {
    setUser(null);
    localStorage.removeItem('mock_token');
    console.log('🎭 Mock Auth: Logged out');
  };

  const hasRole = (role: string): boolean => {
    return user?.roles.includes(role) || false;
  };

  const getToken = (): string | null => {
    if (!shouldUseMockAuth()) return null;
    return localStorage.getItem('mock_token');
  };

  const refreshToken = async (): Promise<string | null> => {
    if (!shouldUseMockAuth() || !user) return null;
    
    // Simulate token refresh
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newToken = generateMockToken(user);
    localStorage.setItem('mock_token', newToken);
    
    return newToken;
  };

  const value: MockAuthContextType = {
    user,
    isAuthenticated: !!user?.authenticated,
    isLoading,
    login,
    logout,
    hasRole,
    getToken,
    refreshToken,
  };

  return (
    <MockAuthContext.Provider value={value}>
      {children}
    </MockAuthContext.Provider>
  );
};

export const useMockAuth = (): MockAuthContextType => {
  const context = useContext(MockAuthContext);
  if (!context) {
    throw new Error('useMockAuth must be used within a MockAuthProvider');
  }
  return context;
};

// Utility function to generate mock JWT tokens
const generateMockToken = (user: MockUser): string => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: user.id,
    email: user.email,
    name: user.name,
    roles: user.roles,
    org_id: user.organizationId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
    iss: 'onecrm-mock',
  }));
  const signature = btoa('mock-signature');
  
  return `${header}.${payload}.${signature}`;
};

// Hook that works with both mock and real auth
export const useAuth = () => {
  if (shouldUseMockAuth()) {
    return useMockAuth();
  }
  
  // Return a basic auth object when not using mock
  return {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    login: async () => {},
    logout: () => {},
    hasRole: () => false,
    getToken: () => null,
    refreshToken: async () => null,
  };
};

// Get current mock auth status for external components
export const getMockAuthStatus = () => {
  // This can be used by external components to get auth status
  return {
    enabled: shouldUseMockAuth(),
    // Additional status info can be added here
  };
};
