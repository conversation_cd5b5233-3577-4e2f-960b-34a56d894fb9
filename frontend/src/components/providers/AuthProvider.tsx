'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { ReactKeycloakProvider } from '@react-keycloak/web';
import getKeycloak, { keycloakInitOptions, keycloakTokenRefreshOptions } from '../../lib/keycloak';
import { 
  AuthContextType, 
  UserProfile, 
  Organization, 
  KeycloakTokenPayload,
  UserProfileSchema,
  OrganizationSchema 
} from '../../types/auth';
import { apiClient } from '../../lib/api';

// Create Auth Context
export const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProviderInner: React.FC<AuthProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<UserProfile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);

  // Get token from Keycloak
  const getToken = useCallback((): string | null => {
    const keycloak = getKeycloak();
    return keycloak?.token || null;
  }, []);

  // Parse JWT token payload
  const parseTokenPayload = useCallback((token: string): KeycloakTokenPayload | null => {
    try {
      const base64Url = token.split('.')[1];
      if (!base64Url) return null;
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to parse token payload:', error);
      return null;
    }
  }, []);

  // Fetch user profile from API
  const fetchUserProfile = useCallback(async (keycloakSub: string): Promise<UserProfile | null> => {
    try {
      const response = await apiClient.get(`/users/profile/${keycloakSub}`);
      return UserProfileSchema.parse(response.data);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      return null;
    }
  }, []);

  // Fetch organization details
  const fetchOrganization = useCallback(async (orgId: string): Promise<Organization | null> => {
    try {
      const response = await apiClient.get(`/organizations/${orgId}`);
      return OrganizationSchema.parse(response.data);
    } catch (error) {
      console.error('Failed to fetch organization:', error);
      return null;
    }
  }, []);

  // Initialize user data from token
  const initializeUserData = useCallback(async () => {
    const token = getToken();
    if (!token) {
      setUser(null);
      setOrganization(null);
      setIsLoading(false);
      return;
    }

    const payload = parseTokenPayload(token);
    if (!payload) {
      setUser(null);
      setOrganization(null);
      setIsLoading(false);
      return;
    }

    try {
      // Fetch user profile
      const userProfile = await fetchUserProfile(payload.sub);
      setUser(userProfile);

      // Fetch organization if user exists
      if (userProfile && payload.org_id) {
        const org = await fetchOrganization(payload.org_id);
        setOrganization(org);
      }
    } catch (error) {
      console.error('Failed to initialize user data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getToken, parseTokenPayload, fetchUserProfile, fetchOrganization]);

  // Login function
  const login = useCallback(async (email?: string, password?: string): Promise<void> => {
    try {
      const keycloak = getKeycloak();
      if (keycloak) {
        await keycloak.login();
      } else {
        // Mock login for development
        if (email && password) {
          setUser({
            id: '1',
            username: email,
            email,
            firstName: 'Demo',
            lastName: 'User',
            orgId: 'demo-org',
            role: 'admin',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }, []);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    try {
      setUser(null);
      setOrganization(null);
      const keycloak = getKeycloak();
      if (keycloak) {
        await keycloak.logout();
      }
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }, []);

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const keycloak = getKeycloak();
      if (!keycloak) return false;
      const refreshed = await keycloak.updateToken(keycloakTokenRefreshOptions.minValidity);
      if (refreshed) {
        await initializeUserData();
      }
      return refreshed;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }, [initializeUserData]);

  // Check if user has specific role
  const hasRole = useCallback((role: string): boolean => {
    if (!user) return false;
    return user.role === role || (user.role === 'admin' && role !== 'admin');
  }, [user]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false;
    
    // Define permission mappings
    const permissions: Record<string, string[]> = {
      'contacts:read': ['admin', 'user', 'viewer'],
      'contacts:write': ['admin', 'user'],
      'contacts:delete': ['admin'],
      'companies:read': ['admin', 'user', 'viewer'],
      'companies:write': ['admin', 'user'],
      'companies:delete': ['admin'],
      'deals:read': ['admin', 'user', 'viewer'],
      'deals:write': ['admin', 'user'],
      'deals:delete': ['admin'],
      'organizations:read': ['admin'],
      'organizations:write': ['admin'],
      'users:read': ['admin'],
      'users:write': ['admin'],
    };

    const allowedRoles = permissions[permission] || [];
    return allowedRoles.includes(user.role);
  }, [user]);

  // Set up token refresh interval
  useEffect(() => {
    const keycloak = getKeycloak();
    if (!keycloak?.authenticated) return;

    const interval = setInterval(async () => {
      try {
        const kc = getKeycloak();
        if (kc) {
          await kc.updateToken(keycloakTokenRefreshOptions.minValidity);
        }
      } catch (error) {
        console.error('Automatic token refresh failed:', error);
        // Optionally redirect to login
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  // Initialize user data when Keycloak is ready
  useEffect(() => {
    const keycloak = getKeycloak();
    if (keycloak?.authenticated) {
      initializeUserData();
    } else {
      setIsLoading(false);
    }
  }, [initializeUserData]);

  const contextValue: AuthContextType = {
    isAuthenticated: !!getKeycloak()?.authenticated || !!user,
    isLoading,
    user,
    organization,
    token: getToken(),
    login,
    logout,
    refreshToken,
    hasRole,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isClient, setIsClient] = React.useState(false);
  const [keycloakInstance, setKeycloakInstance] = React.useState<any>(null);
  const [initError, setInitError] = React.useState<string | null>(null);

  React.useEffect(() => {
    setIsClient(true);

    // Get Keycloak instance only once when component mounts
    try {
      const instance = getKeycloak();
      if (instance) {
        setKeycloakInstance(instance);
      } else {
        setInitError('Failed to initialize Keycloak instance');
      }
    } catch (error) {
      console.error('Keycloak initialization error:', error);
      setInitError('Keycloak initialization failed');
    }
  }, []);

  // Don't render Keycloak provider on server side
  if (!isClient) {
    return <div>Loading authentication...</div>;
  }

  if (initError) {
    return <div>Authentication initialization failed: {initError}</div>;
  }

  if (!keycloakInstance) {
    return <div>Initializing authentication...</div>;
  }

  return (
    <ReactKeycloakProvider
      authClient={keycloakInstance}
      initOptions={keycloakInitOptions}
      LoadingComponent={<div>Loading authentication...</div>}
      onEvent={(event, error) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Keycloak event:', event, error);
        }
        if (error) {
          console.error('Keycloak error:', error);
        }
      }}
      onTokens={(tokens) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Keycloak tokens updated:', tokens);
        }
      }}
    >
      <AuthProviderInner>{children}</AuthProviderInner>
    </ReactKeycloakProvider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
