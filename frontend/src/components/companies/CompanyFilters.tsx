'use client';

import React, { useState } from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Button,
  Grid,
  Paper,
  Typography,
  Chip,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  FilterList,
  Clear,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';

interface CompanyFiltersProps {
  onFiltersChange?: (filters: any) => void;
  onChange?: (filters: any) => void;
  filters?: any;
  loading?: boolean;
}

const industries = [
  'All Industries',
  'Technology',
  'Healthcare',
  'Finance',
  'Manufacturing',
  'Retail',
  'Education',
  'Real Estate',
  'Consulting',
  'Other',
];

const employeeRanges = [
  { label: 'All Sizes', value: '' },
  { label: '1-10', value: '1-10' },
  { label: '11-50', value: '11-50' },
  { label: '51-200', value: '51-200' },
  { label: '201-1000', value: '201-1000' },
  { label: '1000+', value: '1000+' },
];

const revenueRanges = [
  { label: 'All Revenue', value: '' },
  { label: 'Under $1M', value: '0-1000000' },
  { label: '$1M - $10M', value: '1000000-10000000' },
  { label: '$10M - $100M', value: '10000000-*********' },
  { label: '$100M+', value: '*********+' },
];

export const CompanyFilters: React.FC<CompanyFiltersProps> = ({
  onFiltersChange,
  onChange,
  filters: externalFilters,
  loading = false,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    industry: '',
    employeeRange: '',
    revenueRange: '',
    hasWebsite: '',
  });

  const handleFilterChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...filters,
      [field]: event.target.value,
    };
    setFilters(newFilters);
    if (onFiltersChange) onFiltersChange(newFilters);
    if (onChange) onChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      industry: '',
      employeeRange: '',
      revenueRange: '',
      hasWebsite: '',
    };
    setFilters(clearedFilters);
    if (onFiltersChange) onFiltersChange(clearedFilters);
    if (onChange) onChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== '').length;
  };

  const getActiveFiltersDisplay = () => {
    const activeFilters = [];
    
    if (filters.search) activeFilters.push(`Search: "${filters.search}"`);
    if (filters.industry) activeFilters.push(`Industry: ${filters.industry}`);
    if (filters.employeeRange) activeFilters.push(`Size: ${filters.employeeRange}`);
    if (filters.revenueRange) {
      const range = revenueRanges.find(r => r.value === filters.revenueRange);
      activeFilters.push(`Revenue: ${range?.label}`);
    }
    if (filters.hasWebsite) activeFilters.push(`Has Website: ${filters.hasWebsite}`);
    
    return activeFilters;
  };

  return (
    <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FilterList />
          <Typography variant="h6">Filters</Typography>
          {getActiveFiltersCount() > 0 && (
            <Chip 
              label={`${getActiveFiltersCount()} active`} 
              size="small" 
              color="primary" 
            />
          )}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getActiveFiltersCount() > 0 && (
            <Button
              size="small"
              startIcon={<Clear />}
              onClick={clearFilters}
              disabled={loading}
            >
              Clear All
            </Button>
          )}
          <IconButton
            onClick={() => setExpanded(!expanded)}
            disabled={loading}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>
      </Box>

      {/* Always visible search */}
      <TextField
        fullWidth
        placeholder="Search companies..."
        value={filters.search}
        onChange={handleFilterChange('search')}
        disabled={loading}
        sx={{ mb: 2 }}
      />

      {/* Active filters display */}
      {getActiveFiltersCount() > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Active Filters:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {getActiveFiltersDisplay().map((filter, index) => (
              <Chip
                key={index}
                label={filter}
                size="small"
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Expandable advanced filters */}
      <Collapse in={expanded}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Industry"
              value={filters.industry}
              onChange={handleFilterChange('industry')}
              disabled={loading}
            >
              {industries.map((industry) => (
                <MenuItem key={industry} value={industry === 'All Industries' ? '' : industry}>
                  {industry}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Company Size"
              value={filters.employeeRange}
              onChange={handleFilterChange('employeeRange')}
              disabled={loading}
            >
              {employeeRanges.map((range) => (
                <MenuItem key={range.value} value={range.value}>
                  {range.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Revenue Range"
              value={filters.revenueRange}
              onChange={handleFilterChange('revenueRange')}
              disabled={loading}
            >
              {revenueRanges.map((range) => (
                <MenuItem key={range.value} value={range.value}>
                  {range.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Has Website"
              value={filters.hasWebsite}
              onChange={handleFilterChange('hasWebsite')}
              disabled={loading}
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="yes">Yes</MenuItem>
              <MenuItem value="no">No</MenuItem>
            </TextField>
          </Grid>
        </Grid>
      </Collapse>
    </Paper>
  );
};
