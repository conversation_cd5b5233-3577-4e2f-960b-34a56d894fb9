'use client';

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Skeleton,
} from '@mui/material';
import {
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Skeleton variant="text" width={120} height={24} />
              <Skeleton variant="text" width={80} height={40} />
              {subtitle && <Skeleton variant="text" width={100} height={20} />}
            </Box>
            <Skeleton variant="circular" width={48} height={48} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: color,
              width: 48,
              height: 48,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );
};

export const CompaniesStats: React.FC = () => {
  const { data: stats, isLoading } = useSWR('/api/companies/stats');

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getTopIndustry = () => {
    if (!stats?.byIndustry) return 'N/A';
    const entries = Object.entries(stats.byIndustry);
    if (entries.length === 0) return 'N/A';
    const [topIndustry] = entries.reduce((a, b) => (a[1] as number) > (b[1] as number) ? a : b);
    return topIndustry.charAt(0).toUpperCase() + topIndustry.slice(1);
  };

  const getTopSize = () => {
    if (!stats?.bySize) return 'N/A';
    const entries = Object.entries(stats.bySize);
    if (entries.length === 0) return 'N/A';
    const [topSize] = entries.reduce((a, b) => (a[1] as number) > (b[1] as number) ? a : b);
    return topSize.charAt(0).toUpperCase() + topSize.slice(1);
  };

  const statsData = [
    {
      title: 'Total Companies',
      value: stats ? formatNumber(stats.total) : 0,
      subtitle: stats?.recentlyCreated ? `+${stats.recentlyCreated} this month` : undefined,
      icon: <BusinessIcon />,
      color: 'primary.main',
      isLoading,
    },
    {
      title: 'Total Revenue',
      value: stats?.totalRevenue ? formatCurrency(stats.totalRevenue) : '$0',
      subtitle: stats?.averageRevenue ? `${formatCurrency(stats.averageRevenue)} avg` : undefined,
      icon: <MoneyIcon />,
      color: 'success.main',
      isLoading,
    },
    {
      title: 'Top Industry',
      value: getTopIndustry(),
      subtitle: stats?.byIndustry ? `${Object.keys(stats.byIndustry).length} industries` : undefined,
      icon: <TrendingUpIcon />,
      color: 'warning.main',
      isLoading,
    },
    {
      title: 'Top Size',
      value: getTopSize(),
      subtitle: stats?.bySize ? `${Object.keys(stats.bySize).length} size categories` : undefined,
      icon: <PeopleIcon />,
      color: 'info.main',
      isLoading,
    },
  ];

  return (
    <Grid container spacing={3}>
      {statsData.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <StatCard {...stat} subtitle={stat.subtitle || ''} />
        </Grid>
      ))}
    </Grid>
  );
};
