'use client'

import * as React from 'react'
import { useForm, FormProvider, useFormContext, FieldPath, FieldValues } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { cn } from '@/lib/utils'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { EnhancedButton } from './enhanced-button'
import { AlertCircle, CheckCircle2 } from 'lucide-react'

// Form context and provider
interface FormContextType {
  formId: string
}

const FormContext = React.createContext<FormContextType | null>(null)

export interface EnhancedFormProps<T extends FieldValues = FieldValues> {
  schema: z.ZodSchema<T>
  onSubmit: (data: T) => void | Promise<void>
  defaultValues?: Partial<T>
  children: React.ReactNode
  className?: string
  id?: string
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'onTouched' | 'all'
}

export function EnhancedForm<T extends FieldValues>({
  schema,
  onSubmit,
  defaultValues,
  children,
  className,
  id = 'enhanced-form',
  mode = 'onBlur',
}: EnhancedFormProps<T>) {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode,
  })

  const handleSubmit = async (data: T) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  return (
    <FormContext.Provider value={{ formId: id }}>
      <FormProvider {...form}>
        <form
          id={id}
          onSubmit={form.handleSubmit(handleSubmit)}
          className={cn('space-y-6', className)}
          noValidate
        >
          {children}
        </form>
      </FormProvider>
    </FormContext.Provider>
  )
}

// Form field wrapper
export interface FormFieldProps<T extends FieldValues = FieldValues> {
  name: FieldPath<T>
  label?: string
  description?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

export function FormField<T extends FieldValues>({
  name,
  label,
  description,
  required,
  children,
  className,
}: FormFieldProps<T>) {
  const { formState } = useFormContext<T>()
  const error = formState.errors[name]
  const fieldId = `field-${name}`

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label htmlFor={fieldId} className={cn(required && 'after:content-["*"] after:ml-0.5 after:text-destructive')}>
          {label}
        </Label>
      )}
      
      <div className="relative">
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId,
          name,
          'aria-invalid': !!error,
          'aria-describedby': error ? `${fieldId}-error` : description ? `${fieldId}-description` : undefined,
        })}
        
        {error && (
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            <AlertCircle className="h-4 w-4 text-destructive" />
          </div>
        )}
      </div>

      {description && !error && (
        <p id={`${fieldId}-description`} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}

      {error && (
        <p id={`${fieldId}-error`} className="text-sm text-destructive flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error.message as string}
        </p>
      )}
    </div>
  )
}

// Enhanced input components
export interface FormInputProps extends React.ComponentProps<typeof Input> {
  name: string
}

export const FormInput = React.forwardRef<HTMLInputElement, FormInputProps>(
  ({ name, ...props }, ref) => {
    const { register, formState } = useFormContext()
    const error = formState.errors[name]

    return (
      <Input
        ref={ref}
        {...register(name)}
        className={cn(error && 'border-destructive focus-visible:ring-destructive')}
        {...props}
      />
    )
  }
)

FormInput.displayName = 'FormInput'

export interface FormTextareaProps extends React.ComponentProps<typeof Textarea> {
  name: string
}

export const FormTextarea = React.forwardRef<HTMLTextAreaElement, FormTextareaProps>(
  ({ name, ...props }, ref) => {
    const { register, formState } = useFormContext()
    const error = formState.errors[name]

    return (
      <Textarea
        ref={ref}
        {...register(name)}
        className={cn(error && 'border-destructive focus-visible:ring-destructive')}
        {...props}
      />
    )
  }
)

FormTextarea.displayName = 'FormTextarea'

export interface FormSelectProps {
  name: string
  placeholder?: string
  options: Array<{ value: string; label: string; disabled?: boolean }>
  onValueChange?: (value: string) => void
}

export function FormSelect({ name, placeholder, options, onValueChange }: FormSelectProps) {
  const { setValue, watch, formState } = useFormContext()
  const value = watch(name)
  const error = formState.errors[name]

  const handleValueChange = (newValue: string) => {
    setValue(name, newValue, { shouldValidate: true })
    onValueChange?.(newValue)
  }

  return (
    <Select value={value || ''} onValueChange={handleValueChange}>
      <SelectTrigger className={cn(error && 'border-destructive focus:ring-destructive')}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

export interface FormCheckboxProps {
  name: string
  label: string
  description?: string
}

export function FormCheckbox({ name, label, description }: FormCheckboxProps) {
  const { setValue, watch, formState } = useFormContext()
  const checked = watch(name) || false
  const error = formState.errors[name]

  const handleCheckedChange = (newChecked: boolean) => {
    setValue(name, newChecked, { shouldValidate: true })
  }

  return (
    <div className="flex items-top space-x-2">
      <Checkbox
        id={name}
        checked={checked}
        onCheckedChange={handleCheckedChange}
        className={cn(error && 'border-destructive')}
      />
      <div className="grid gap-1.5 leading-none">
        <Label
          htmlFor={name}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </Label>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {error && (
          <p className="text-xs text-destructive">{error.message as string}</p>
        )}
      </div>
    </div>
  )
}

// Form actions
export interface FormActionsProps {
  children: React.ReactNode
  className?: string
  align?: 'left' | 'center' | 'right'
}

export function FormActions({ children, className, align = 'right' }: FormActionsProps) {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
  }

  return (
    <div className={cn('flex gap-2', alignClasses[align], className)}>
      {children}
    </div>
  )
}

// Form submit button
export interface FormSubmitProps extends React.ComponentProps<typeof EnhancedButton> {
  loadingText?: string
}

export function FormSubmit({ children = 'Submit', loadingText = 'Submitting...', ...props }: FormSubmitProps) {
  const { formState } = useFormContext()

  return (
    <EnhancedButton
      type="submit"
      loading={formState.isSubmitting}
      loadingText={loadingText}
      disabled={formState.isSubmitting}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
}

// Form validation status
export function FormValidationStatus() {
  const { formState } = useFormContext()
  const { isValid, errors } = formState

  if (Object.keys(errors).length === 0) {
    return (
      <div className="flex items-center gap-2 text-sm text-green-600">
        <CheckCircle2 className="h-4 w-4" />
        Form is valid
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2 text-sm text-destructive">
      <AlertCircle className="h-4 w-4" />
      {Object.keys(errors).length} error(s) found
    </div>
  )
}
