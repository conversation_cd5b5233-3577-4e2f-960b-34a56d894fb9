'use client'

import React from 'react'
import { MetricCard, EnhancedCard } from './enhanced-card'
import { cn } from '@/lib/utils'

export type StatChange = {
  value: number
  type: 'increase' | 'decrease'
  period?: string
} | undefined

export type StatItem = {
  id: string
  label: string
  value: string | number
  change?: StatChange
  icon?: React.ReactNode
}

export function StatsGrid({
  items,
  className,
  columns = { base: 1, md: 2, lg: 4 }
}: {
  items: StatItem[]
  className?: string
  columns?: { base?: number; md?: number; lg?: number }
}) {
  const md = columns.md ?? 2
  const lg = columns.lg ?? 4

  const gridClasses = cn(
    'grid gap-6 grid-cols-1',
    md === 2 ? 'md:grid-cols-2' : md === 3 ? 'md:grid-cols-3' : md === 4 ? 'md:grid-cols-4' : '',
    lg === 2 ? 'lg:grid-cols-2' : lg === 3 ? 'lg:grid-cols-3' : lg === 4 ? 'lg:grid-cols-4' : lg === 5 ? 'lg:grid-cols-5' : '',
    className
  )

  return (
    <div className={gridClasses}>
      {items.map((item) => (
        <MetricCard
          key={item.id}
          value={item.value}
          label={item.label}
          change={item.change as any}
          icon={item.icon}
        />
      ))}
    </div>
  )
}

export function StatsGroup({
  title,
  description,
  items,
  headerActions,
  className,
}: {
  title?: string
  description?: string
  items: StatItem[]
  headerActions?: React.ReactNode
  className?: string
}) {
  return (
    <EnhancedCard
      {...(title ? { title } : {})}
      {...(description ? { description } : {})}
      {...(headerActions ? { headerActions } : {})}
      className={cn('space-y-4', className)}
    >
      <StatsGrid items={items} />
    </EnhancedCard>
  )
}