import * as React from 'react'
import * as RadixSeparator from '@radix-ui/react-separator'
import { cn } from '@/lib/utils'

export interface SeparatorProps extends React.ComponentPropsWithoutRef<typeof RadixSeparator.Root> {
  inset?: boolean
}

export const Separator = React.forwardRef<
  React.ElementRef<typeof RadixSeparator.Root>,
  SeparatorProps
>(({ className, orientation = 'horizontal', decorative = true, inset, ...props }, ref) => (
  <RadixSeparator.Root
    ref={ref}
    decorative={decorative}
    orientation={orientation}
    className={cn(
      'shrink-0 bg-border',
      orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px',
      inset && 'mx-6',
      className
    )}
    {...props}
  />
))
Separator.displayName = 'Separator'
