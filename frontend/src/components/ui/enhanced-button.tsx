'use client'

import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

const enhancedButtonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*="size-"])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive min-w-[64px]',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
        success: 'bg-green-600 text-white shadow-xs hover:bg-green-700 focus-visible:ring-green-600/20',
        warning: 'bg-yellow-600 text-white shadow-xs hover:bg-yellow-700 focus-visible:ring-yellow-600/20',
        info: 'bg-blue-600 text-white shadow-xs hover:bg-blue-700 focus-visible:ring-blue-600/20',
        gradient: 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-xs hover:from-primary/90 hover:to-primary/70',
      },
      size: {
        xs: 'h-7 rounded px-2 text-xs has-[>svg]:px-1.5',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        xl: 'h-12 rounded-lg px-8 text-base has-[>svg]:px-6',
        icon: 'size-9',
        'icon-sm': 'size-8',
        'icon-lg': 'size-10',
      },
      loading: {
        true: 'cursor-not-allowed',
        false: '',
      },
      fullWidth: {
        true: 'w-full',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      loading: false,
      fullWidth: false,
    },
  }
)

export interface EnhancedButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof enhancedButtonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  tooltip?: string
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      children,
      disabled,
      fullWidth,
      tooltip,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'
    const isDisabled = disabled || loading

    const content = (
      <>
        {loading ? (
          <Loader2 className="animate-spin" />
        ) : (
          leftIcon && <span className="shrink-0">{leftIcon}</span>
        )}
        
        <span className={cn(loading && loadingText && 'sr-only')}>
          {children}
        </span>
        
        {loading && loadingText && (
          <span>{loadingText}</span>
        )}
        
        {!loading && rightIcon && (
          <span className="shrink-0">{rightIcon}</span>
        )}
      </>
    )

    const button = (
      <Comp
        ref={ref}
        className={cn(enhancedButtonVariants({ variant, size, loading, fullWidth, className }))}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        data-loading={loading}
        {...props}
      >
        {content}
      </Comp>
    )

    if (tooltip) {
      return (
        <div className="relative group">
          {button}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
            {tooltip}
          </div>
        </div>
      )
    }

    return button
  }
)

EnhancedButton.displayName = 'EnhancedButton'

// Specialized button components
export const IconButton = React.forwardRef<
  HTMLButtonElement,
  Omit<EnhancedButtonProps, 'leftIcon' | 'rightIcon'> & { icon: React.ReactNode }
>(({ icon, size = 'icon', ...props }, ref) => (
  <EnhancedButton ref={ref} size={size} {...props}>
    {icon}
  </EnhancedButton>
))

IconButton.displayName = 'IconButton'

export const LoadingButton = React.forwardRef<
  HTMLButtonElement,
  EnhancedButtonProps & { isLoading?: boolean }
>(({ isLoading, ...props }, ref) => (
  <EnhancedButton ref={ref} loading={isLoading} {...props} />
))

LoadingButton.displayName = 'LoadingButton'

export const ActionButton = React.forwardRef<
  HTMLButtonElement,
  EnhancedButtonProps & {
    action: 'create' | 'edit' | 'delete' | 'save' | 'cancel' | 'submit'
  }
>(({ action, variant, children, ...props }, ref) => {
  const actionConfig = {
    create: { variant: 'default' as const, text: 'Create' },
    edit: { variant: 'outline' as const, text: 'Edit' },
    delete: { variant: 'destructive' as const, text: 'Delete' },
    save: { variant: 'success' as const, text: 'Save' },
    cancel: { variant: 'ghost' as const, text: 'Cancel' },
    submit: { variant: 'default' as const, text: 'Submit' },
  }

  const config = actionConfig[action]

  return (
    <EnhancedButton
      ref={ref}
      variant={variant || config.variant}
      {...props}
    >
      {children || config.text}
    </EnhancedButton>
  )
})

ActionButton.displayName = 'ActionButton'

export { EnhancedButton, enhancedButtonVariants }
