'use client'

import * as React from 'react'
import * as RadixAvatar from '@radix-ui/react-avatar'
import { cn } from '@/lib/utils'

export interface AvatarProps extends React.ComponentPropsWithoutRef<typeof RadixAvatar.Root> {
  fallback?: string
}

export const Avatar = React.forwardRef<
  React.ElementRef<typeof RadixAvatar.Root>,
  AvatarProps
>(({ className, fallback, children, ...props }, ref) => (
  <RadixAvatar.Root
    ref={ref}
    className={cn(
      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full border border-border bg-white dark:bg-zinc-900',
      className
    )}
    {...props}
  >
    {children}
    {fallback ? (
      <RadixAvatar.Fallback className="flex h-full w-full items-center justify-center text-sm font-medium text-muted-foreground">
        {fallback}
      </RadixAvatar.Fallback>
    ) : null}
  </RadixAvatar.Root>
))
Avatar.displayName = 'Avatar'

export const AvatarImage = React.forwardRef<
  React.ElementRef<typeof RadixAvatar.Image>,
  React.ComponentPropsWithoutRef<typeof RadixAvatar.Image>
>(({ className, ...props }, ref) => (
  <RadixAvatar.Image ref={ref} className={cn('h-full w-full object-cover', className)} {...props} />
))
AvatarImage.displayName = 'AvatarImage'

export const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof RadixAvatar.Fallback>,
  React.ComponentPropsWithoutRef<typeof RadixAvatar.Fallback>
>(({ className, ...props }, ref) => (
  <RadixAvatar.Fallback
    ref={ref}
    className={cn('flex h-full w-full items-center justify-center text-sm', className)}
    {...props}
  />
))
AvatarFallback.displayName = 'AvatarFallback'
