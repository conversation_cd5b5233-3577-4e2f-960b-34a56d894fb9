'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './dialog'
import { Button } from './button'
import { EnhancedButton } from './enhanced-button'
import { X, AlertTriangle, CheckCircle, Info, HelpCircle } from 'lucide-react'

const enhancedDialogVariants = cva(
  'relative',
  {
    variants: {
      size: {
        xs: 'max-w-xs',
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-lg',
        xl: 'max-w-xl',
        '2xl': 'max-w-2xl',
        '3xl': 'max-w-3xl',
        '4xl': 'max-w-4xl',
        '5xl': 'max-w-5xl',
        full: 'max-w-[95vw] max-h-[95vh]',
      },
      variant: {
        default: '',
        destructive: 'border-destructive/20',
        success: 'border-green-500/20',
        warning: 'border-yellow-500/20',
        info: 'border-blue-500/20',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  }
)

export interface EnhancedDialogProps
  extends React.ComponentProps<typeof Dialog>,
    VariantProps<typeof enhancedDialogVariants> {
  title?: string
  description?: string
  children?: React.ReactNode
  trigger?: React.ReactNode
  footer?: React.ReactNode
  showCloseButton?: boolean
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  onClose?: () => void
}

export function EnhancedDialog({
  title,
  description,
  children,
  trigger,
  footer,
  size,
  variant,
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  onClose,
  open,
  onOpenChange,
  ...props
}: EnhancedDialogProps) {
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && onClose) {
      onClose()
    }
    onOpenChange?.(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange} {...props}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      <DialogContent
        className={cn(enhancedDialogVariants({ size, variant }))}
        onPointerDownOutside={closeOnOverlayClick ? undefined : (e) => e.preventDefault()}
        onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
      >
        {showCloseButton && (
          <button
            onClick={() => handleOpenChange(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>
        )}

        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        )}

        {children && <div className="py-4">{children}</div>}

        {footer && <DialogFooter>{footer}</DialogFooter>}
      </DialogContent>
    </Dialog>
  )
}

// Confirmation Dialog
export interface ConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  onConfirm,
  onCancel,
  loading = false,
}: ConfirmationDialogProps) {
  const [isLoading, setIsLoading] = React.useState(false)

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      console.error('Confirmation action failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange(false)
  }

  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <AlertTriangle className="h-6 w-6 text-destructive" />
      default:
        return <HelpCircle className="h-6 w-6 text-primary" />
    }
  }

  return (
    <EnhancedDialog
      open={open}
      onOpenChange={onOpenChange}
      size="sm"
      variant={variant}
      closeOnOverlayClick={!isLoading}
      closeOnEscape={!isLoading}
    >
      <div className="flex items-start gap-4">
        <div className="shrink-0">{getIcon()}</div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">{title}</h3>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      </div>

      <DialogFooter className="mt-6">
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading || loading}
        >
          {cancelText}
        </Button>
        <EnhancedButton
          variant={variant === 'destructive' ? 'destructive' : 'default'}
          onClick={handleConfirm}
          loading={isLoading || loading}
          loadingText="Processing..."
        >
          {confirmText}
        </EnhancedButton>
      </DialogFooter>
    </EnhancedDialog>
  )
}

// Alert Dialog
export interface AlertDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  type?: 'info' | 'success' | 'warning' | 'error'
  actionText?: string
  onAction?: () => void
}

export function AlertDialog({
  open,
  onOpenChange,
  title,
  description,
  type = 'info',
  actionText = 'OK',
  onAction,
}: AlertDialogProps) {
  const handleAction = () => {
    onAction?.()
    onOpenChange(false)
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />
      case 'error':
        return <AlertTriangle className="h-6 w-6 text-destructive" />
      default:
        return <Info className="h-6 w-6 text-blue-500" />
    }
  }

  const getVariant = () => {
    switch (type) {
      case 'error':
        return 'destructive' as const
      case 'warning':
        return 'warning' as const
      case 'success':
        return 'success' as const
      default:
        return 'info' as const
    }
  }

  return (
    <EnhancedDialog
      open={open}
      onOpenChange={onOpenChange}
      size="sm"
      variant={getVariant()}
    >
      <div className="flex items-start gap-4">
        <div className="shrink-0">{getIcon()}</div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">{title}</h3>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      </div>

      <DialogFooter className="mt-6">
        <EnhancedButton onClick={handleAction}>
          {actionText}
        </EnhancedButton>
      </DialogFooter>
    </EnhancedDialog>
  )
}

// Form Dialog
export interface FormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  children: React.ReactNode
  submitText?: string
  cancelText?: string
  onSubmit?: () => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  size?: VariantProps<typeof enhancedDialogVariants>['size']
}

export function FormDialog({
  open,
  onOpenChange,
  title,
  description,
  children,
  submitText = 'Submit',
  cancelText = 'Cancel',
  onSubmit,
  onCancel,
  loading = false,
  size = 'md',
}: FormDialogProps) {
  const [isLoading, setIsLoading] = React.useState(false)

  const handleSubmit = async () => {
    if (!onSubmit) return
    
    try {
      setIsLoading(true)
      await onSubmit()
      onOpenChange(false)
    } catch (error) {
      console.error('Form submission failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange(false)
  }

  return (
    <EnhancedDialog
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      size={size}
      closeOnOverlayClick={!isLoading && !loading}
      closeOnEscape={!isLoading && !loading}
    >
      {children}

      <DialogFooter>
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading || loading}
        >
          {cancelText}
        </Button>
        <EnhancedButton
          onClick={handleSubmit}
          loading={isLoading || loading}
          loadingText="Submitting..."
        >
          {submitText}
        </EnhancedButton>
      </DialogFooter>
    </EnhancedDialog>
  )
}
