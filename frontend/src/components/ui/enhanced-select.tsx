'use client'

import * as React from 'react'
import { Check, ChevronDown, Search, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export interface SelectOption {
  value: string
  label: string
  description?: string
  icon?: React.ReactNode
  disabled?: boolean
  group?: string
}

export interface EnhancedSelectProps {
  options: SelectOption[]
  value?: string | string[]
  onValueChange: (value: string | string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
  disabled?: boolean
  loading?: boolean
  maxSelected?: number
  className?: string
  label?: string
  description?: string
  error?: string
  required?: boolean
}

export function EnhancedSelect({
  options,
  value,
  onValueChange,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search options...',
  emptyMessage = 'No options found.',
  multiple = false,
  searchable = true,
  clearable = false,
  disabled = false,
  loading = false,
  maxSelected,
  className,
  label,
  description,
  error,
  required = false,
}: EnhancedSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  const selectedValues = React.useMemo(() => {
    if (multiple) {
      return Array.isArray(value) ? value : []
    }
    return value ? [value] : []
  }, [value, multiple])

  const selectedOptions = React.useMemo(() => {
    return options.filter(option => selectedValues.includes(option.value))
  }, [options, selectedValues])

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options
    
    return options.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
      option.description?.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue])

  const groupedOptions = React.useMemo(() => {
    const groups: Record<string, SelectOption[]> = {}
    
    filteredOptions.forEach(option => {
      const group = option.group || 'default'
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(option)
    })
    
    return groups
  }, [filteredOptions])

  const handleSelect = (optionValue: string) => {
    if (multiple) {
      const currentValues = Array.isArray(value) ? value : []
      let newValues: string[]
      
      if (currentValues.includes(optionValue)) {
        newValues = currentValues.filter(v => v !== optionValue)
      } else {
        if (maxSelected && currentValues.length >= maxSelected) {
          return
        }
        newValues = [...currentValues, optionValue]
      }
      
      onValueChange(newValues)
    } else {
      onValueChange(optionValue)
      setOpen(false)
    }
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValueChange(multiple ? [] : '')
  }

  const handleRemoveTag = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (multiple && Array.isArray(value)) {
      const newValues = value.filter(v => v !== optionValue)
      onValueChange(newValues)
    }
  }

  const getDisplayValue = () => {
    if (selectedOptions.length === 0) {
      return placeholder
    }

    if (multiple) {
      if (selectedOptions.length === 1) {
        return selectedOptions[0].label
      }
      return `${selectedOptions.length} selected`
    }

    return selectedOptions[0]?.label || placeholder
  }

  const fieldId = React.useId()

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label htmlFor={fieldId} className={cn(required && 'after:content-["*"] after:ml-0.5 after:text-destructive')}>
          {label}
        </Label>
      )}
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={fieldId}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'w-full justify-between',
              !selectedValues.length && 'text-muted-foreground',
              error && 'border-destructive focus-visible:ring-destructive',
              disabled && 'opacity-50 cursor-not-allowed'
            )}
            disabled={disabled || loading}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {multiple && selectedOptions.length > 0 ? (
                <div className="flex flex-wrap gap-1 max-w-full">
                  {selectedOptions.slice(0, 3).map((option) => (
                    <Badge
                      key={option.value}
                      variant="secondary"
                      className="text-xs"
                    >
                      {option.icon && <span className="mr-1">{option.icon}</span>}
                      {option.label}
                      <button
                        type="button"
                        onClick={(e) => handleRemoveTag(option.value, e)}
                        className="ml-1 hover:bg-muted rounded-full"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                  {selectedOptions.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{selectedOptions.length - 3} more
                    </Badge>
                  )}
                </div>
              ) : (
                <span className="truncate">
                  {selectedOptions[0]?.icon && (
                    <span className="mr-2">{selectedOptions[0].icon}</span>
                  )}
                  {getDisplayValue()}
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              {clearable && selectedValues.length > 0 && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="hover:bg-muted rounded-full p-1"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
              <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command>
            {searchable && (
              <div className="flex items-center border-b px-3">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <CommandInput
                  placeholder={searchPlaceholder}
                  value={searchValue}
                  onValueChange={setSearchValue}
                  className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            )}
            
            <CommandList>
              <CommandEmpty>{emptyMessage}</CommandEmpty>
              
              {Object.entries(groupedOptions).map(([group, groupOptions]) => (
                <CommandGroup key={group} heading={group !== 'default' ? group : undefined}>
                  {groupOptions.map((option) => {
                    const isSelected = selectedValues.includes(option.value)
                    const isDisabled = option.disabled || 
                      (maxSelected && multiple && !isSelected && selectedValues.length >= maxSelected)
                    
                    return (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        onSelect={() => !isDisabled && handleSelect(option.value)}
                        disabled={isDisabled}
                        className={cn(
                          'flex items-center gap-2 cursor-pointer',
                          isDisabled && 'opacity-50 cursor-not-allowed'
                        )}
                      >
                        {multiple && (
                          <div className={cn(
                            'flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                            isSelected ? 'bg-primary text-primary-foreground' : 'opacity-50'
                          )}>
                            {isSelected && <Check className="h-3 w-3" />}
                          </div>
                        )}
                        
                        {!multiple && (
                          <Check
                            className={cn(
                              'mr-2 h-4 w-4',
                              isSelected ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                        )}
                        
                        {option.icon && <span>{option.icon}</span>}
                        
                        <div className="flex-1 min-w-0">
                          <div className="truncate">{option.label}</div>
                          {option.description && (
                            <div className="text-xs text-muted-foreground truncate">
                              {option.description}
                            </div>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {description && !error && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

// Combobox component for single selection with custom input
export interface ComboboxProps {
  options: SelectOption[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  allowCustom?: boolean
  onCreateOption?: (value: string) => void
  className?: string
  disabled?: boolean
}

export function Combobox({
  options,
  value,
  onValueChange,
  placeholder = 'Search or select...',
  searchPlaceholder = 'Search...',
  emptyMessage = 'No options found.',
  allowCustom = false,
  onCreateOption,
  className,
  disabled = false,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState('')

  const selectedOption = options.find(option => option.value === value)

  React.useEffect(() => {
    if (selectedOption) {
      setInputValue(selectedOption.label)
    } else if (value) {
      setInputValue(value)
    } else {
      setInputValue('')
    }
  }, [selectedOption, value])

  const filteredOptions = React.useMemo(() => {
    if (!inputValue) return options
    
    return options.filter(option =>
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    )
  }, [options, inputValue])

  const handleSelect = (optionValue: string) => {
    const option = options.find(opt => opt.value === optionValue)
    if (option) {
      setInputValue(option.label)
      onValueChange(optionValue)
    }
    setOpen(false)
  }

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue)
    setOpen(true)
    
    // If the input matches an option exactly, select it
    const exactMatch = options.find(option => 
      option.label.toLowerCase() === newValue.toLowerCase()
    )
    if (exactMatch) {
      onValueChange(exactMatch.value)
    } else if (allowCustom) {
      onValueChange(newValue)
    }
  }

  const handleCreateOption = () => {
    if (allowCustom && onCreateOption && inputValue.trim()) {
      onCreateOption(inputValue.trim())
      setOpen(false)
    }
  }

  return (
    <div className={cn('relative', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder={placeholder}
              disabled={disabled}
              className="pr-8"
              onFocus={() => setOpen(true)}
            />
            <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 opacity-50" />
          </div>
        </PopoverTrigger>
        
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command>
            <CommandList>
              {filteredOptions.length === 0 ? (
                <CommandEmpty>
                  {emptyMessage}
                  {allowCustom && inputValue.trim() && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCreateOption}
                      className="mt-2 w-full"
                    >
                      Create "{inputValue.trim()}"
                    </Button>
                  )}
                </CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => handleSelect(option.value)}
                      className="flex items-center gap-2"
                    >
                      {option.icon && <span>{option.icon}</span>}
                      <div className="flex-1">
                        <div>{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        )}
                      </div>
                      {value === option.value && <Check className="h-4 w-4" />}
                    </CommandItem>
                  ))}
                  
                  {allowCustom && inputValue.trim() && 
                   !filteredOptions.some(opt => opt.label.toLowerCase() === inputValue.toLowerCase()) && (
                    <CommandItem onSelect={handleCreateOption}>
                      Create "{inputValue.trim()}"
                    </CommandItem>
                  )}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
