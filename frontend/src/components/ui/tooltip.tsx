'use client'

import * as React from 'react'
import * as RadixTooltip from '@radix-ui/react-tooltip'
import { cn } from '@/lib/utils'

export interface TooltipProps extends React.ComponentPropsWithoutRef<typeof RadixTooltip.Root> {
  content: React.ReactNode
  side?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
}

export function Tooltip({
  children,
  content,
  side = 'top',
  align = 'center',
  delayDuration = 150,
  ...props
}: TooltipProps) {
  return (
    <RadixTooltip.Provider delayDuration={delayDuration}>
      <RadixTooltip.Root {...props}>
        <RadixTooltip.Trigger asChild>{children}</RadixTooltip.Trigger>
        <RadixTooltip.Portal>
          <RadixTooltip.Content
            side={side}
            align={align}
            className={cn(
              'z-50 select-none rounded-md bg-foreground/90 px-2 py-1.5 text-xs text-background shadow-md backdrop-blur supports-[backdrop-filter]:bg-foreground/80',
              'data-[state=delayed-open]:data-[side=top]:animate-in data-[state=delayed-open]:data-[side=top]:fade-in-0 data-[state=delayed-open]:data-[side=top]:zoom-in-95'
            )}
          >
            {content}
            <RadixTooltip.Arrow className="fill-foreground/90" />
          </RadixTooltip.Content>
        </RadixTooltip.Portal>
      </RadixTooltip.Root>
    </RadixTooltip.Provider>
  )
}
