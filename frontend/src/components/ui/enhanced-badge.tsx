'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { Badge } from './badge'
import { X, Check, AlertTriangle, Info, Clock } from 'lucide-react'

const enhancedBadgeVariants = cva(
  'inline-flex items-center gap-1 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',
        secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',
        outline: 'text-foreground',
        success: 'border-transparent bg-green-500 text-white shadow hover:bg-green-600',
        warning: 'border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600',
        info: 'border-transparent bg-blue-500 text-white shadow hover:bg-blue-600',
        purple: 'border-transparent bg-purple-500 text-white shadow hover:bg-purple-600',
        pink: 'border-transparent bg-pink-500 text-white shadow hover:bg-pink-600',
        indigo: 'border-transparent bg-indigo-500 text-white shadow hover:bg-indigo-600',
        gradient: 'border-transparent bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs',
        default: 'px-2.5 py-0.5 text-xs',
        lg: 'px-3 py-1 text-sm',
      },
      shape: {
        default: 'rounded-full',
        rounded: 'rounded-md',
        square: 'rounded-none',
      },
      interactive: {
        true: 'cursor-pointer hover:scale-105 active:scale-95',
        false: '',
      },
      pulse: {
        true: 'animate-pulse',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      shape: 'default',
      interactive: false,
      pulse: false,
    },
  }
)

export interface EnhancedBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof enhancedBadgeVariants> {
  icon?: React.ReactNode
  onRemove?: () => void
  dot?: boolean
  count?: number
  maxCount?: number
}

const EnhancedBadge = React.forwardRef<HTMLDivElement, EnhancedBadgeProps>(
  (
    {
      className,
      variant,
      size,
      shape,
      interactive,
      pulse,
      icon,
      onRemove,
      dot,
      count,
      maxCount = 99,
      children,
      onClick,
      ...props
    },
    ref
  ) => {
    const displayCount = count !== undefined && count > maxCount ? `${maxCount}+` : count

    return (
      <div
        ref={ref}
        className={cn(
          enhancedBadgeVariants({ variant, size, shape, interactive, pulse }),
          className
        )}
        onClick={onClick}
        {...props}
      >
        {dot && (
          <div className="w-1.5 h-1.5 rounded-full bg-current" />
        )}
        
        {icon && !dot && (
          <span className="shrink-0">
            {icon}
          </span>
        )}
        
        {count !== undefined ? displayCount : children}
        
        {onRemove && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation()
              onRemove()
            }}
            className="ml-1 shrink-0 hover:bg-black/10 rounded-full p-0.5 transition-colors"
            aria-label="Remove"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>
    )
  }
)

EnhancedBadge.displayName = 'EnhancedBadge'

// Specialized badge components
export const StatusBadge = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedBadgeProps, 'variant' | 'icon'> & {
    status: 'active' | 'inactive' | 'pending' | 'completed' | 'failed' | 'draft'
  }
>(({ status, ...props }, ref) => {
  const statusConfig = {
    active: { variant: 'success' as const, icon: <Check className="h-3 w-3" />, label: 'Active' },
    inactive: { variant: 'secondary' as const, icon: <X className="h-3 w-3" />, label: 'Inactive' },
    pending: { variant: 'warning' as const, icon: <Clock className="h-3 w-3" />, label: 'Pending' },
    completed: { variant: 'success' as const, icon: <Check className="h-3 w-3" />, label: 'Completed' },
    failed: { variant: 'destructive' as const, icon: <AlertTriangle className="h-3 w-3" />, label: 'Failed' },
    draft: { variant: 'outline' as const, icon: <Info className="h-3 w-3" />, label: 'Draft' },
  }

  const config = statusConfig[status]

  return (
    <EnhancedBadge
      ref={ref}
      variant={config.variant}
      icon={config.icon}
      {...props}
    >
      {config.label}
    </EnhancedBadge>
  )
})

StatusBadge.displayName = 'StatusBadge'

export const PriorityBadge = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedBadgeProps, 'variant'> & {
    priority: 'low' | 'medium' | 'high' | 'urgent'
  }
>(({ priority, ...props }, ref) => {
  const priorityConfig = {
    low: { variant: 'secondary' as const, label: 'Low' },
    medium: { variant: 'info' as const, label: 'Medium' },
    high: { variant: 'warning' as const, label: 'High' },
    urgent: { variant: 'destructive' as const, label: 'Urgent' },
  }

  const config = priorityConfig[priority]

  return (
    <EnhancedBadge
      ref={ref}
      variant={config.variant}
      {...props}
    >
      {config.label}
    </EnhancedBadge>
  )
})

PriorityBadge.displayName = 'PriorityBadge'

export const CategoryBadge = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedBadgeProps, 'variant'> & {
    category: string
    color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'pink' | 'indigo'
  }
>(({ category, color = 'blue', ...props }, ref) => {
  const colorVariants = {
    blue: 'info' as const,
    green: 'success' as const,
    yellow: 'warning' as const,
    red: 'destructive' as const,
    purple: 'purple' as const,
    pink: 'pink' as const,
    indigo: 'indigo' as const,
  }

  return (
    <EnhancedBadge
      ref={ref}
      variant={colorVariants[color]}
      {...props}
    >
      {category}
    </EnhancedBadge>
  )
})

CategoryBadge.displayName = 'CategoryBadge'

export const CountBadge = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedBadgeProps, 'children'> & {
    count: number
    maxCount?: number
    showZero?: boolean
  }
>(({ count, maxCount = 99, showZero = false, ...props }, ref) => {
  if (count === 0 && !showZero) {
    return null
  }

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString()

  return (
    <EnhancedBadge
      ref={ref}
      variant="destructive"
      size="sm"
      {...props}
    >
      {displayCount}
    </EnhancedBadge>
  )
})

CountBadge.displayName = 'CountBadge'

export const TagBadge = React.forwardRef<
  HTMLDivElement,
  EnhancedBadgeProps & {
    tag: string
    removable?: boolean
    onRemove?: () => void
  }
>(({ tag, removable, onRemove, ...props }, ref) => (
  <EnhancedBadge
    ref={ref}
    variant="outline"
    interactive={removable}
    onRemove={removable ? onRemove : undefined}
    {...props}
  >
    {tag}
  </EnhancedBadge>
))

TagBadge.displayName = 'TagBadge'

export const NotificationBadge = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedBadgeProps, 'children' | 'variant'> & {
    count?: number
    dot?: boolean
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
    children: React.ReactNode
  }
>(({ count, dot = false, position = 'top-right', children, ...props }, ref) => {
  const positionClasses = {
    'top-right': '-top-1 -right-1',
    'top-left': '-top-1 -left-1',
    'bottom-right': '-bottom-1 -right-1',
    'bottom-left': '-bottom-1 -left-1',
  }

  return (
    <div ref={ref} className="relative inline-block">
      {children}
      {(count !== undefined && count > 0) || dot ? (
        <EnhancedBadge
          variant="destructive"
          size="sm"
          className={cn('absolute', positionClasses[position])}
          dot={dot && count === undefined}
          count={!dot ? count : undefined}
          {...props}
        />
      ) : null}
    </div>
  )
})

NotificationBadge.displayName = 'NotificationBadge'

export { EnhancedBadge, enhancedBadgeVariants }
