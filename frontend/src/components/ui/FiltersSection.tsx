'use client'

import React from 'react'
import { EnhancedCard } from './enhanced-card'
import { EnhancedButton } from './enhanced-button'
import { EnhancedBadge } from './enhanced-badge'
import { cn } from '@/lib/utils'
import { Filter, X } from 'lucide-react'

export type ActiveFilter = {
  key: string
  label: string
  value: string
  onRemove?: (() => void) | undefined
}

export function FiltersSection({
  search,
  onChangeSearch,
  placeholder = 'Search...',
  showAdvanced,
  onToggleAdvanced,
  activeFilters = [],
  rightActions,
  children, // advanced filters content
  className
}: {
  search: string
  onChangeSearch: (v: string) => void
  placeholder?: string
  showAdvanced: boolean
  onToggleAdvanced: () => void
  activeFilters?: ActiveFilter[]
  rightActions?: React.ReactNode
  children?: React.ReactNode
  className?: string
}) {
  return (
    <EnhancedCard className={cn('space-y-4', className)}>
      <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
        <div className="flex-1">
          <div className="relative">
            <input
              className="w-full rounded-md border bg-background px-10 py-2 text-sm outline-none ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring"
              placeholder={placeholder}
              value={search}
              onChange={(e) => onChangeSearch(e.target.value)}
            />
            <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
              <Filter className="h-4 w-4" />
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <EnhancedButton
            variant={showAdvanced ? 'default' : 'outline'}
            size="sm"
            leftIcon={<Filter className="h-4 w-4" />}
            onClick={onToggleAdvanced}
          >
            Filters
          </EnhancedButton>

          {rightActions}
        </div>
      </div>

      {/* Active filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          {activeFilters.map((f) => (
            <EnhancedBadge
              key={`${f.key}:${f.value}`}
              variant="outline"
              interactive
              {...(f.onRemove ? { onRemove: f.onRemove } : {})}
              className="text-xs"
            >
              {f.label}: {f.value}
            </EnhancedBadge>
          ))}

          {/* Clear all */}
          {activeFilters.some((f) => !!f.onRemove) && (
            <EnhancedButton
              variant="ghost"
              size="sm"
              leftIcon={<X className="h-4 w-4" />}
              onClick={() => activeFilters.forEach((f) => f.onRemove?.())}
            >
              Clear all
            </EnhancedButton>
          )}
        </div>
      )}

      {/* Advanced content slot */}
      {showAdvanced && children && (
        <div className="pt-2 border-t">{children}</div>
      )}
    </EnhancedCard>
  )
}