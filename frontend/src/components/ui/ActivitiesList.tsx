'use client'

import React from 'react'
import { EnhancedCard } from './enhanced-card'
import { cn, formatDate } from '@/lib/utils'
import { Activity, Building2, Users, TrendingUp, Calendar } from 'lucide-react'

export type ActivityItem = {
  id: string
  type?: 'activity' | 'deal' | 'company' | 'contact' | 'meeting'
  title: string
  description?: string
  timestamp: string | Date
  user?: string
}

function TypeIcon({ type }: { type?: ActivityItem['type'] }) {
  const base = 'h-4 w-4 text-primary'
  switch (type) {
    case 'deal':
      return <TrendingUp className={base} />
    case 'company':
      return <Building2 className={base} />
    case 'contact':
      return <Users className={base} />
    case 'meeting':
      return <Calendar className={base} />
    default:
      return <Activity className={base} />
  }
}

export function ActivitiesList({
  items,
  className,
  compact = false,
}: {
  items: ActivityItem[]
  className?: string
  compact?: boolean
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {items.map((activity) => (
        <div key={activity.id} className={cn('flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors', compact && 'p-2')}>
          <div className="p-2 rounded-full bg-primary/10">
            <TypeIcon type={activity.type} />
          </div>
          <div className="flex-1 min-w-0">
            <p className={cn('font-medium text-sm', compact && 'text-xs')}>{activity.title}</p>
            {activity.description && (
              <p className={cn('text-sm text-muted-foreground', compact && 'text-xs')}>{activity.description}</p>
            )}
            <div className="flex items-center gap-2 mt-1">
              <span className={cn('text-xs text-muted-foreground', compact && 'text-[10px]')}>
                {formatDate(activity.timestamp)}
              </span>
              {activity.user && (
                <>
                  <span className="text-xs text-muted-foreground">•</span>
                  <span className={cn('text-xs text-muted-foreground', compact && 'text-[10px]')}>{activity.user}</span>
                </>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function ActivitiesCard({
  title = 'Recent Activities',
  description = 'Latest updates and changes',
  items,
  headerActions,
  className,
}: {
  title?: string
  description?: string
  items: ActivityItem[]
  headerActions?: React.ReactNode
  className?: string
}) {
  return (
    <EnhancedCard
      title={title}
      description={description}
      headerActions={headerActions}
      className={className}
    >
      <ActivitiesList items={items} />
    </EnhancedCard>
  )
}