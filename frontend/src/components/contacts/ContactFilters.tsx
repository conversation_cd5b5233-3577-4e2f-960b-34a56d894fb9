'use client';

import React from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Autocomplete,
  Chip,
  Box,
} from '@mui/material';
import useSWR from 'swr';

interface ContactFiltersProps {
  filters: {
    leadStatus: string;
    leadSource: string;
    assignedToId: string;
    tags: string[];
  };
  onChange: (filters: any) => void;
}

const leadStatuses = [
  { value: '', label: 'All Statuses' },
  { value: 'new', label: 'New' },
  { value: 'contacted', label: 'Contacted' },
  { value: 'qualified', label: 'Qualified' },
  { value: 'unqualified', label: 'Unqualified' },
  { value: 'customer', label: 'Customer' },
  { value: 'lost', label: 'Lost' },
];

const leadSources = [
  { value: '', label: 'All Sources' },
  { value: 'website', label: 'Website' },
  { value: 'referral', label: 'Referral' },
  { value: 'social-media', label: 'Social Media' },
  { value: 'email-campaign', label: 'Email Campaign' },
  { value: 'cold-call', label: 'Cold Call' },
  { value: 'trade-show', label: 'Trade Show' },
  { value: 'partner', label: 'Partner' },
  { value: 'other', label: 'Other' },
];

const commonTags = [
  'VIP',
  'Hot Lead',
  'Decision Maker',
  'Budget Approved',
  'Technical Contact',
  'Procurement',
  'C-Level',
  'Influencer',
  'Champion',
  'Blocker',
];

export const ContactFilters: React.FC<ContactFiltersProps> = ({ filters, onChange }) => {
  const { data: users } = useSWR('/api/users/organization');

  const handleFilterChange = (field: string, value: any) => {
    onChange({
      ...filters,
      [field]: value,
    });
  };

  const handleTagsChange = (event: any, newTags: string[]) => {
    handleFilterChange('tags', newTags);
  };

  return (
    <Grid container spacing={2}>
      {/* Lead Status Filter */}
      <Grid item xs={12} sm={6} md={3}>
        <FormControl fullWidth size="small">
          <InputLabel>Lead Status</InputLabel>
          <Select
            value={filters.leadStatus}
            label="Lead Status"
            onChange={(e) => handleFilterChange('leadStatus', e.target.value)}
          >
            {leadStatuses.map((status) => (
              <MenuItem key={status.value} value={status.value}>
                {status.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Lead Source Filter */}
      <Grid item xs={12} sm={6} md={3}>
        <FormControl fullWidth size="small">
          <InputLabel>Lead Source</InputLabel>
          <Select
            value={filters.leadSource}
            label="Lead Source"
            onChange={(e) => handleFilterChange('leadSource', e.target.value)}
          >
            {leadSources.map((source) => (
              <MenuItem key={source.value} value={source.value}>
                {source.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Assigned To Filter */}
      <Grid item xs={12} sm={6} md={3}>
        <FormControl fullWidth size="small">
          <InputLabel>Assigned To</InputLabel>
          <Select
            value={filters.assignedToId}
            label="Assigned To"
            onChange={(e) => handleFilterChange('assignedToId', e.target.value)}
          >
            <MenuItem value="">All Users</MenuItem>
            {users?.map((user: any) => (
              <MenuItem key={user.id} value={user.id}>
                {user.firstName} {user.lastName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Tags Filter */}
      <Grid item xs={12} sm={6} md={3}>
        <Autocomplete
          multiple
          size="small"
          options={commonTags}
          value={filters.tags}
          onChange={handleTagsChange}
          freeSolo
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip
                variant="outlined"
                label={option}
                size="small"
                {...getTagProps({ index })}
                key={option}
              />
            ))
          }
          renderInput={(params) => (
            <TextField
              {...(params as any)}
              label="Tags"
              placeholder="Select or type tags..."
              size="medium"
            />
          )}
        />
      </Grid>
    </Grid>
  );
};
