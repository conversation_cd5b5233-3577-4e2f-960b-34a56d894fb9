'use client';

import React from 'react';
import { Button, CircularProgress } from '@mui/material';
import { Login as LoginIcon } from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';

interface LoginButtonProps {
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  className?: string;
}

export const LoginButton: React.FC<LoginButtonProps> = ({
  variant = 'contained',
  size = 'medium',
  fullWidth = false,
  className,
}) => {
  const { login, isLoading } = useAuth();

  const handleLogin = async () => {
    try {
      await login();
    } catch (error) {
      console.error('Login failed:', error);
      // Handle login error (show toast, etc.)
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      className={className || ''}
      onClick={handleLogin}
      disabled={isLoading}
      startIcon={isLoading ? <CircularProgress size={20} /> : <LoginIcon />}
    >
      {isLoading ? 'Signing in...' : 'Sign In'}
    </Button>
  );
};
