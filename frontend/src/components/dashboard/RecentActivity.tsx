'use client';

import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Chip,
  CircularProgress,
  Button,
} from '@mui/material';
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  AttachMoney as MoneyIcon,
  Edit as EditIcon,
  Add as AddIcon,
} from '@mui/icons-material';

interface Activity {
  id: string;
  type: 'contact' | 'company' | 'deal' | 'task' | 'note';
  title: string;
  description: string;
  timestamp: string;
  user: string;
}

interface RecentActivityProps {
  activities?: Activity[];
  loading?: boolean;
  maxItems?: number;
}

export const RecentActivity: React.FC<RecentActivityProps> = ({
  activities = [],
  loading = false,
  maxItems = 10,
}) => {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'contact':
        return <PersonIcon />;
      case 'company':
        return <BusinessIcon />;
      case 'deal':
        return <MoneyIcon />;
      case 'task':
        return <EditIcon />;
      case 'note':
        return <AddIcon />;
      default:
        return <EditIcon />;
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'contact':
        return 'primary';
      case 'company':
        return 'secondary';
      case 'deal':
        return 'success';
      case 'task':
        return 'warning';
      case 'note':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" py={4}>
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (activities.length === 0) {
    return (
      <Box textAlign="center" py={4}>
        <Typography variant="body2" color="text.secondary">
          No recent activity to display
        </Typography>
        <Button variant="outlined" size="small" sx={{ mt: 2 }}>
          Create First Activity
        </Button>
      </Box>
    );
  }

  const displayedActivities = activities.slice(0, maxItems);

  return (
    <Box>
      <List disablePadding>
        {displayedActivities.map((activity, index) => (
          <ListItem
            key={activity.id}
            divider={index < displayedActivities.length - 1}
            sx={{ px: 0 }}
          >
            <ListItemAvatar>
              <Avatar
                sx={{
                  bgcolor: `${getActivityColor(activity.type)}.main`,
                  width: 40,
                  height: 40,
                }}
              >
                {getActivityIcon(activity.type)}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                  <Typography variant="body2" fontWeight="medium">
                    {activity.title}
                  </Typography>
                  <Chip
                    label={activity.type}
                    size="small"
                    color={getActivityColor(activity.type) as any}
                    variant="outlined"
                    sx={{ fontSize: '0.7rem', height: 20 }}
                  />
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {activity.description}
                  </Typography>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="caption" color="text.secondary">
                      by {activity.user}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimestamp(activity.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      {activities.length > maxItems && (
        <Box textAlign="center" mt={2}>
          <Button variant="text" size="small">
            View All Activity ({activities.length} total)
          </Button>
        </Box>
      )}
    </Box>
  );
};
