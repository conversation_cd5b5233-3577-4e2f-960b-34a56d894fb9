'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Skeleton,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Assignment as AssignmentIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import useSWR from 'swr';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface AdvancedAnalyticsProps {
  timeRange: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const MetricCard: React.FC<{
  title: string;
  value: string | number;
  change: number;
  icon: React.ReactNode;
  color: string;
  isLoading?: boolean;
}> = ({ title, value, change, icon, color, isLoading }) => {
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Skeleton variant="text" width={120} height={24} />
              <Skeleton variant="text" width={80} height={40} />
              <Skeleton variant="text" width={100} height={20} />
            </Box>
            <Skeleton variant="circular" width={48} height={48} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  const isPositive = change >= 0;

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            <Box display="flex" alignItems="center" mt={1}>
              {isPositive ? (
                <TrendingUpIcon color="success" fontSize="small" />
              ) : (
                <TrendingDownIcon color="error" fontSize="small" />
              )}
              <Typography
                variant="body2"
                color={isPositive ? 'success.main' : 'error.main'}
                sx={{ ml: 0.5 }}
              >
                {Math.abs(change)}% vs last period
              </Typography>
            </Box>
          </Box>
          <Avatar
            sx={{
              bgcolor: color,
              width: 48,
              height: 48,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );
};

const SalesChart: React.FC<{ timeRange: string }> = ({ timeRange }) => {
  const { data, isLoading } = useSWR(`/api/analytics/sales-trend?timeRange=${timeRange}`);

  if (isLoading) {
    return <Skeleton variant="rectangular" height={300} />;
  }

  return (
    <Card>
      <CardHeader title="Sales Trend" />
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data?.chartData || []}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="period" />
            <YAxis />
            <Tooltip formatter={(value) => [`$${value?.toLocaleString()}`, 'Revenue']} />
            <Area
              type="monotone"
              dataKey="revenue"
              stroke="#8884d8"
              fill="#8884d8"
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const PipelineChart: React.FC = () => {
  const { data, isLoading } = useSWR('/api/deals/pipeline');

  if (isLoading) {
    return <Skeleton variant="rectangular" height={300} />;
  }

  const chartData = data?.pipeline?.map((stage: any) => ({
    name: stage.stage.charAt(0).toUpperCase() + stage.stage.slice(1),
    value: stage.totalValue,
    count: stage.count,
  })) || [];

  return (
    <Card>
      <CardHeader title="Pipeline Distribution" />
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry: any, index: number) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => [`$${value?.toLocaleString()}`, 'Value']} />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const ConversionFunnel: React.FC = () => {
  const { data, isLoading } = useSWR('/api/analytics/conversion-funnel');

  if (isLoading) {
    return <Skeleton variant="rectangular" height={300} />;
  }

  const stages = [
    { name: 'Leads', value: data?.leads || 0, color: '#94a3b8' },
    { name: 'Qualified', value: data?.qualified || 0, color: '#3b82f6' },
    { name: 'Proposals', value: data?.proposals || 0, color: '#f59e0b' },
    { name: 'Negotiations', value: data?.negotiations || 0, color: '#ef4444' },
    { name: 'Closed Won', value: data?.closedWon || 0, color: '#22c55e' },
  ];

  const maxValue = Math.max(...stages.map(s => s.value));

  return (
    <Card>
      <CardHeader title="Conversion Funnel" />
      <CardContent>
        <Box>
          {stages.map((stage, index) => {
            const percentage = maxValue > 0 ? (stage.value / maxValue) * 100 : 0;
            const conversionRate = index > 0 ? ((stage.value / (stages[index - 1]?.value || 1)) * 100) : 100;
            
            return (
              <Box key={stage.name} mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" fontWeight={600}>
                    {stage.name}
                  </Typography>
                  <Box display="flex" gap={2}>
                    <Typography variant="body2" color="text.secondary">
                      {stage.value} deals
                    </Typography>
                    {index > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        {conversionRate.toFixed(1)}% conversion
                      </Typography>
                    )}
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={percentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: stage.color,
                    },
                  }}
                />
              </Box>
            );
          })}
        </Box>
      </CardContent>
    </Card>
  );
};

const TopPerformers: React.FC = () => {
  const { data, isLoading } = useSWR('/api/analytics/top-performers');

  if (isLoading) {
    return (
      <Card>
        <CardHeader title="Top Performers" />
        <CardContent>
          <List>
            {[1, 2, 3, 4, 5].map((i) => (
              <ListItem key={i}>
                <ListItemAvatar>
                  <Skeleton variant="circular" width={40} height={40} />
                </ListItemAvatar>
                <ListItemText
                  primary={<Skeleton variant="text" width="60%" />}
                  secondary={<Skeleton variant="text" width="40%" />}
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader title="Top Performers" />
      <CardContent>
        <List>
          {data?.topPerformers?.map((performer: any, index: number) => (
            <React.Fragment key={performer.id}>
              <ListItem>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: index < 3 ? 'warning.main' : 'primary.main' }}>
                    {index < 3 ? <StarIcon /> : `${performer.firstName[0]}${performer.lastName[0]}`}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={`${performer.firstName} ${performer.lastName}`}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        ${performer.totalRevenue?.toLocaleString()} revenue
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {performer.dealsCount} deals closed
                      </Typography>
                    </Box>
                  }
                />
                <Chip
                  label={`#${index + 1}`}
                  size="small"
                  color={index < 3 ? 'warning' : 'default'}
                />
              </ListItem>
              {index < (data?.topPerformers?.length - 1) && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export const AdvancedAnalytics: React.FC<AdvancedAnalyticsProps> = ({ timeRange }) => {
  const { data: metricsData, isLoading: metricsLoading } = useSWR(`/api/analytics/metrics?timeRange=${timeRange}`);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const metrics = [
    {
      title: 'Total Revenue',
      value: metricsData?.totalRevenue ? formatCurrency(metricsData.totalRevenue) : '$0',
      change: metricsData?.revenueChange || 0,
      icon: <MoneyIcon />,
      color: 'success.main',
    },
    {
      title: 'New Customers',
      value: metricsData?.newCustomers || 0,
      change: metricsData?.customersChange || 0,
      icon: <PeopleIcon />,
      color: 'primary.main',
    },
    {
      title: 'Active Deals',
      value: metricsData?.activeDeals || 0,
      change: metricsData?.dealsChange || 0,
      icon: <TrendingUpIcon />,
      color: 'warning.main',
    },
    {
      title: 'Conversion Rate',
      value: metricsData?.conversionRate ? `${metricsData.conversionRate}%` : '0%',
      change: metricsData?.conversionChange || 0,
      icon: <AssignmentIcon />,
      color: 'info.main',
    },
  ];

  return (
    <Grid container spacing={3}>
      {/* Key Metrics */}
      {metrics.map((metric, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <MetricCard {...metric} isLoading={metricsLoading} />
        </Grid>
      ))}

      {/* Sales Trend Chart */}
      <Grid item xs={12} lg={8}>
        <SalesChart timeRange={timeRange} />
      </Grid>

      {/* Pipeline Distribution */}
      <Grid item xs={12} lg={4}>
        <PipelineChart />
      </Grid>

      {/* Conversion Funnel */}
      <Grid item xs={12} lg={8}>
        <ConversionFunnel />
      </Grid>

      {/* Top Performers */}
      <Grid item xs={12} lg={4}>
        <TopPerformers />
      </Grid>
    </Grid>
  );
};
