'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Avatar,
  Skeleton,
  Button,
  Checkbox,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Event as EventIcon,
  Note as NoteIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import useSWR from 'swr';
import { format, isToday, isTomorrow, isPast, parseISO } from 'date-fns';

interface Activity {
  id: string;
  type: string;
  subject: string;
  description?: string;
  dueDate: string;
  priority: string;
  isCompleted: boolean;
  contact?: {
    firstName: string;
    lastName: string;
  };
  company?: {
    name: string;
  };
  assignedTo?: {
    firstName: string;
    lastName: string;
  };
}

const activityIcons: Record<string, React.ReactNode> = {
  call: <PhoneIcon />,
  email: <EmailIcon />,
  meeting: <EventIcon />,
  task: <AssignmentIcon />,
  note: <NoteIcon />,
  demo: <EventIcon />,
  'follow-up': <PhoneIcon />,
};

const priorityColors: Record<string, string> = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error',
};

const ActivityItem: React.FC<{ activity: Activity; onComplete: (id: string) => void }> = ({
  activity,
  onComplete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleComplete = () => {
    onComplete(activity.id);
  };

  const formatDate = (dateString: string) => {
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return `Today ${format(date, 'h:mm a')}`;
    } else if (isTomorrow(date)) {
      return `Tomorrow ${format(date, 'h:mm a')}`;
    } else {
      return format(date, 'MMM d, h:mm a');
    }
  };

  const getDateChipProps = (dateString: string) => {
    const date = parseISO(dateString);
    
    if (isPast(date) && !activity.isCompleted) {
      return {
        color: 'error' as const,
        icon: <WarningIcon />,
        label: 'Overdue',
      };
    } else if (isToday(date)) {
      return {
        color: 'warning' as const,
        icon: <ScheduleIcon />,
        label: 'Today',
      };
    } else {
      return {
        color: 'default' as const,
        icon: <ScheduleIcon />,
        label: formatDate(dateString),
      };
    }
  };

  const dateChipProps = getDateChipProps(activity.dueDate);

  return (
    <ListItem
      sx={{
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        mb: 1,
        bgcolor: activity.isCompleted ? 'action.hover' : 'background.paper',
        opacity: activity.isCompleted ? 0.7 : 1,
      }}
    >
      <ListItemIcon>
        <Checkbox
          checked={activity.isCompleted}
          onChange={handleComplete}
          icon={<CheckCircleIcon color="disabled" />}
          checkedIcon={<CheckCircleIcon color="success" />}
        />
      </ListItemIcon>

      <ListItemIcon>
        <Avatar
          sx={{
            bgcolor: `${priorityColors[activity.priority]}.main`,
            width: 32,
            height: 32,
          }}
        >
          {activityIcons[activity.type] || <AssignmentIcon />}
        </Avatar>
      </ListItemIcon>

      <ListItemText
        primary={
          <Box display="flex" alignItems="center" gap={1}>
            <Typography
              variant="subtitle2"
              sx={{
                textDecoration: activity.isCompleted ? 'line-through' : 'none',
                fontWeight: activity.isCompleted ? 400 : 600,
              }}
            >
              {activity.subject}
            </Typography>
            <Chip
              size="small"
              variant="outlined"
              {...dateChipProps}
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          </Box>
        }
        secondary={
          <Box>
            {activity.description && (
              <Typography variant="caption" color="text.secondary" display="block">
                {activity.description}
              </Typography>
            )}
            <Box display="flex" alignItems="center" gap={1} mt={0.5}>
              {activity.contact && (
                <Typography variant="caption" color="text.secondary">
                  {activity.contact.firstName} {activity.contact.lastName}
                </Typography>
              )}
              {activity.company && (
                <Chip
                  label={activity.company.name}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.65rem', height: 18 }}
                />
              )}
              {activity.assignedTo && (
                <Tooltip title={`Assigned to: ${activity.assignedTo.firstName} ${activity.assignedTo.lastName}`}>
                  <Avatar sx={{ width: 16, height: 16, fontSize: '0.6rem' }}>
                    {activity.assignedTo.firstName[0]}{activity.assignedTo.lastName[0]}
                  </Avatar>
                </Tooltip>
              )}
            </Box>
          </Box>
        }
      />

      <ListItemSecondaryAction>
        <IconButton size="small" onClick={handleMenuOpen}>
          <MoreVertIcon fontSize="small" />
        </IconButton>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleMenuClose}>View Details</MenuItem>
          <MenuItem onClick={handleMenuClose}>Edit Activity</MenuItem>
          <MenuItem onClick={handleMenuClose}>Mark Complete</MenuItem>
        </Menu>
      </ListItemSecondaryAction>
    </ListItem>
  );
};

export const UpcomingTasks: React.FC = () => {
  const { data: upcomingActivities, isLoading, mutate } = useSWR('/api/activities/upcoming?days=7');

  const handleCompleteActivity = async (activityId: string) => {
    try {
      // Optimistically update the UI
      mutate(
        (current: Activity[]) =>
          current?.map((activity) =>
            activity.id === activityId
              ? { ...activity, isCompleted: true }
              : activity
          ),
        false
      );

      // Make API call to mark as complete
      const response = await fetch(`/api/activities/${activityId}/complete`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to complete activity');
      }

      // Revalidate the data
      mutate();
    } catch (error) {
      console.error('Error completing activity:', error);
      // Revert optimistic update on error
      mutate();
    }
  };

  return (
    <Card sx={{ height: 'fit-content' }}>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <AssignmentIcon color="primary" />
            <Typography variant="h6" fontWeight={600}>
              Upcoming Tasks
            </Typography>
          </Box>
        }
        action={
          <Button size="small" variant="outlined">
            View All
          </Button>
        }
      />
      <CardContent sx={{ pt: 0 }}>
        {isLoading ? (
          <List>
            {Array.from({ length: 5 }).map((_, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <Skeleton variant="circular" width={32} height={32} />
                </ListItemIcon>
                <ListItemText
                  primary={<Skeleton variant="text" width="60%" />}
                  secondary={<Skeleton variant="text" width="40%" />}
                />
              </ListItem>
            ))}
          </List>
        ) : upcomingActivities && upcomingActivities.length > 0 ? (
          <List sx={{ p: 0 }}>
            {upcomingActivities.slice(0, 10).map((activity: Activity) => (
              <ActivityItem
                key={activity.id}
                activity={activity}
                onComplete={handleCompleteActivity}
              />
            ))}
          </List>
        ) : (
          <Box textAlign="center" py={4}>
            <AssignmentIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              No upcoming tasks
            </Typography>
            <Button size="small" variant="outlined" sx={{ mt: 2 }}>
              Create Task
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
