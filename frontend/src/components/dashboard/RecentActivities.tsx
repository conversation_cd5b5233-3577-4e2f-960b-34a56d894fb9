'use client';

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Skeleton,
  Button,
  Divider,
} from '@mui/material';
import {
  History as HistoryIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Event as EventIcon,
  Note as NoteIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import useSWR from 'swr';
import { format, parseISO, formatDistanceToNow } from 'date-fns';

interface RecentActivity {
  id: string;
  type: string;
  subject: string;
  description?: string;
  createdAt: string;
  user?: {
    firstName: string;
    lastName: string;
  };
  contact?: {
    firstName: string;
    lastName: string;
  };
  company?: {
    name: string;
  };
  deal?: {
    title: string;
    amount: number;
  };
}

const activityIcons: Record<string, React.ReactNode> = {
  call: <PhoneIcon />,
  email: <EmailIcon />,
  meeting: <EventIcon />,
  task: <AssignmentIcon />,
  note: <NoteIcon />,
  demo: <EventIcon />,
  'follow-up': <PhoneIcon />,
  deal: <TrendingUpIcon />,
  contact: <PersonIcon />,
  company: <BusinessIcon />,
};

const activityColors: Record<string, string> = {
  call: 'primary.main',
  email: 'secondary.main',
  meeting: 'warning.main',
  task: 'info.main',
  note: 'success.main',
  demo: 'warning.main',
  'follow-up': 'primary.main',
  deal: 'success.main',
  contact: 'primary.main',
  company: 'secondary.main',
};

const ActivityItem: React.FC<{ activity: RecentActivity }> = ({ activity }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getActivityDescription = () => {
    if (activity.description) {
      return activity.description;
    }

    // Generate description based on activity type and related entities
    let description = '';
    
    if (activity.contact) {
      description += `with ${activity.contact.firstName} ${activity.contact.lastName}`;
    }
    
    if (activity.company) {
      description += description ? ` at ${activity.company.name}` : `at ${activity.company.name}`;
    }
    
    if (activity.deal) {
      description += description 
        ? ` regarding ${activity.deal.title} (${formatCurrency(activity.deal.amount)})`
        : `${activity.deal.title} (${formatCurrency(activity.deal.amount)})`;
    }

    return description || 'No additional details';
  };

  const getRelativeTime = (dateString: string) => {
    const date = parseISO(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  return (
    <>
      <ListItem alignItems="flex-start" sx={{ px: 0 }}>
        <ListItemAvatar>
          <Avatar
            sx={{
              bgcolor: activityColors[activity.type] || 'grey.500',
              width: 40,
              height: 40,
            }}
          >
            {activityIcons[activity.type] || <AssignmentIcon />}
          </Avatar>
        </ListItemAvatar>
        
        <ListItemText
          primary={
            <Box display="flex" alignItems="center" gap={1} mb={0.5}>
              <Typography variant="subtitle2" fontWeight={600}>
                {activity.subject}
              </Typography>
              <Chip
                label={activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.7rem', height: 20 }}
              />
            </Box>
          }
          secondary={
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {getActivityDescription()}
              </Typography>
              
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box display="flex" alignItems="center" gap={1}>
                  {activity.user && (
                    <Typography variant="caption" color="text.secondary">
                      by {activity.user.firstName} {activity.user.lastName}
                    </Typography>
                  )}
                  
                  {activity.contact && (
                    <Chip
                      label={`${activity.contact.firstName} ${activity.contact.lastName}`}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.65rem', height: 18 }}
                    />
                  )}
                  
                  {activity.company && (
                    <Chip
                      label={activity.company.name}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.65rem', height: 18 }}
                    />
                  )}
                </Box>
                
                <Typography variant="caption" color="text.secondary">
                  {getRelativeTime(activity.createdAt)}
                </Typography>
              </Box>
            </Box>
          }
        />
      </ListItem>
      <Divider variant="inset" component="li" />
    </>
  );
};

export const RecentActivities: React.FC = () => {
  const { data: recentActivities, isLoading } = useSWR('/api/activities?limit=10&sortBy=createdAt&sortOrder=DESC');

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <HistoryIcon color="primary" />
            <Typography variant="h6" fontWeight={600}>
              Recent Activities
            </Typography>
          </Box>
        }
        action={
          <Button size="small" variant="outlined">
            View All
          </Button>
        }
      />
      <CardContent sx={{ pt: 0 }}>
        {isLoading ? (
          <List>
            {Array.from({ length: 8 }).map((_, index) => (
              <React.Fragment key={index}>
                <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Skeleton variant="circular" width={40} height={40} />
                  </ListItemAvatar>
                  <ListItemText
                    primary={<Skeleton variant="text" width="60%" />}
                    secondary={
                      <Box>
                        <Skeleton variant="text" width="80%" />
                        <Skeleton variant="text" width="40%" />
                      </Box>
                    }
                  />
                </ListItem>
                {index < 7 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        ) : recentActivities?.activities && recentActivities.activities.length > 0 ? (
          <List sx={{ p: 0 }}>
            {recentActivities.activities.map((activity: RecentActivity, index: number) => (
              <React.Fragment key={activity.id}>
                <ActivityItem activity={activity} />
                {index === recentActivities.activities.length - 1 && (
                  <ListItem sx={{ px: 0, pt: 2 }}>
                    <ListItemText>
                      <Box textAlign="center">
                        <Button variant="outlined" size="small">
                          Load More Activities
                        </Button>
                      </Box>
                    </ListItemText>
                  </ListItem>
                )}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <Box textAlign="center" py={4}>
            <HistoryIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              No recent activities
            </Typography>
            <Button size="small" variant="outlined" sx={{ mt: 2 }}>
              Create Activity
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
