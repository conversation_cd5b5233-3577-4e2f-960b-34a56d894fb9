'use client';

import React, { createContext, useContext, useState, useC<PERSON>back, ReactNode } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert,
  Alert<PERSON><PERSON>le,
  Slide,
  SlideProps,
  IconButton,
  Box,
} from '@mui/material';
import { Close } from '@mui/icons-material';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface Notification {
  id: string;
  type: NotificationType;
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  action?: ReactNode;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  // Convenience methods
  success: (message: string, options?: Partial<Notification>) => void;
  error: (message: string, options?: Partial<Notification>) => void;
  warning: (message: string, options?: Partial<Notification>) => void;
  info: (message: string, options?: Partial<Notification>) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

const SlideTransition = React.forwardRef<HTMLDivElement, SlideProps>(
  function SlideTransition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  }
);

export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      id,
      duration: 6000,
      ...notification,
    };

    setNotifications((prev) => [...prev, newNotification]);

    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const success = useCallback((message: string, options?: Partial<Notification>) => {
    addNotification({ type: 'success', message, ...options });
  }, [addNotification]);

  const error = useCallback((message: string, options?: Partial<Notification>) => {
    addNotification({ type: 'error', message, persistent: true, ...options });
  }, [addNotification]);

  const warning = useCallback((message: string, options?: Partial<Notification>) => {
    addNotification({ type: 'warning', message, ...options });
  }, [addNotification]);

  const info = useCallback((message: string, options?: Partial<Notification>) => {
    addNotification({ type: 'info', message, ...options });
  }, [addNotification]);

  const value: NotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
};

const NotificationContainer: React.FC = () => {
  const context = useContext(NotificationContext);
  if (!context) return null;

  const { notifications, removeNotification } = context;

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: (theme) => theme.zIndex.snackbar,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        maxWidth: 400,
      }}
    >
      {notifications.map((notification) => (
        <Snackbar
          key={notification.id}
          open={true}
          sx={{ position: 'relative', transform: 'none !important' }}
        >
          <Alert
            severity={notification.type}
            variant="filled"
            sx={{ width: '100%' }}
            action={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {notification.action}
                <IconButton
                  size="small"
                  aria-label="close"
                  color="inherit"
                  onClick={() => removeNotification(notification.id)}
                >
                  <Close fontSize="small" />
                </IconButton>
              </Box>
            }
          >
            {notification.title && <AlertTitle>{notification.title}</AlertTitle>}
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Higher-order component for automatic error notifications
export const withErrorNotification = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    const { error } = useNotifications();

    const handleError = useCallback((err: Error | string) => {
      const message = typeof err === 'string' ? err : err.message;
      error(message, {
        title: 'Error',
        persistent: true,
      });
    }, [error]);

    return <Component {...props} onError={handleError} />;
  };
};

// Utility functions for common notification patterns
export const NotificationUtils = {
  // API operation notifications
  apiSuccess: (operation: string, entity?: string) => {
    const message = entity 
      ? `${entity} ${operation} successfully`
      : `${operation} completed successfully`;
    return { type: 'success' as const, message };
  },

  apiError: (operation: string, entity?: string, error?: string) => {
    const baseMessage = entity 
      ? `Failed to ${operation} ${entity}`
      : `${operation} failed`;
    const message = error ? `${baseMessage}: ${error}` : baseMessage;
    return { type: 'error' as const, message, persistent: true };
  },

  // Form notifications
  formSaved: (entityName: string) => ({
    type: 'success' as const,
    message: `${entityName} saved successfully`,
  }),

  formError: (error: string) => ({
    type: 'error' as const,
    message: `Please fix the following errors: ${error}`,
    persistent: true,
  }),

  // Connection notifications
  connectionLost: () => ({
    type: 'warning' as const,
    message: 'Connection lost. Attempting to reconnect...',
    persistent: true,
  }),

  connectionRestored: () => ({
    type: 'success' as const,
    message: 'Connection restored',
  }),
};
