'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Box,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Collapse,
  Button,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  Event as EventIcon,
} from '@mui/icons-material';

interface ResponsiveCardProps {
  type: 'contact' | 'company' | 'deal' | 'activity';
  data: any;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  onView?: (item: any) => void;
  actions?: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
  }>;
  expandable?: boolean;
  compact?: boolean;
}

const getTypeIcon = (type: string, data: any) => {
  switch (type) {
    case 'contact':
      return (
        <Avatar sx={{ bgcolor: 'primary.main' }}>
          {data.firstName?.[0]}{data.lastName?.[0]}
        </Avatar>
      );
    case 'company':
      return (
        <Avatar sx={{ bgcolor: 'secondary.main' }}>
          <BusinessIcon />
        </Avatar>
      );
    case 'deal':
      return (
        <Avatar sx={{ bgcolor: 'success.main' }}>
          <MoneyIcon />
        </Avatar>
      );
    case 'activity':
      return (
        <Avatar sx={{ bgcolor: 'warning.main' }}>
          <EventIcon />
        </Avatar>
      );
    default:
      return <Avatar />;
  }
};

const formatCurrency = (amount?: number) => {
  if (!amount) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
};

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  type,
  data,
  onEdit,
  onDelete,
  onView,
  actions = [],
  expandable = false,
  compact = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [expanded, setExpanded] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (action: () => void) => {
    handleMenuClose();
    action();
  };

  const renderPrimaryContent = () => {
    switch (type) {
      case 'contact':
        return (
          <Box>
            <Typography variant="h6" component="div" gutterBottom>
              {data.firstName} {data.lastName}
            </Typography>
            {data.title && (
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {data.title}
              </Typography>
            )}
            {data.company && (
              <Typography variant="body2" color="text.secondary">
                {data.company}
              </Typography>
            )}
          </Box>
        );

      case 'company':
        return (
          <Box>
            <Typography variant="h6" component="div" gutterBottom>
              {data.name}
            </Typography>
            {data.industry && (
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {data.industry}
              </Typography>
            )}
            {data.size && (
              <Chip label={data.size} size="small" variant="outlined" />
            )}
          </Box>
        );

      case 'deal':
        return (
          <Box>
            <Typography variant="h6" component="div" gutterBottom>
              {data.title}
            </Typography>
            <Typography variant="h5" color="primary" gutterBottom>
              {formatCurrency(data.amount)}
            </Typography>
            {data.stage && (
              <Chip label={data.stage} size="small" color="primary" />
            )}
          </Box>
        );

      case 'activity':
        return (
          <Box>
            <Typography variant="h6" component="div" gutterBottom>
              {data.subject}
            </Typography>
            {data.type && (
              <Chip label={data.type} size="small" variant="outlined" sx={{ mr: 1 }} />
            )}
            {data.dueDate && (
              <Typography variant="body2" color="text.secondary">
                Due: {formatDate(data.dueDate)}
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const renderSecondaryContent = () => {
    if (!expandable || !expanded) return null;

    switch (type) {
      case 'contact':
        return (
          <Box mt={2}>
            <Divider sx={{ mb: 2 }} />
            {data.email && (
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <EmailIcon fontSize="small" color="action" />
                <Typography variant="body2">{data.email}</Typography>
              </Box>
            )}
            {data.phone && (
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2">{data.phone}</Typography>
              </Box>
            )}
            {data.leadStatus && (
              <Box mt={1}>
                <Chip label={`Status: ${data.leadStatus}`} size="small" />
              </Box>
            )}
            {data.tags && data.tags.length > 0 && (
              <Box mt={1} display="flex" gap={0.5} flexWrap="wrap">
                {data.tags.map((tag: string) => (
                  <Chip key={tag} label={tag} size="small" variant="outlined" />
                ))}
              </Box>
            )}
          </Box>
        );

      case 'company':
        return (
          <Box mt={2}>
            <Divider sx={{ mb: 2 }} />
            {data.website && (
              <Typography variant="body2" gutterBottom>
                Website: {data.website}
              </Typography>
            )}
            {data.annualRevenue && (
              <Typography variant="body2" gutterBottom>
                Revenue: {formatCurrency(data.annualRevenue)}
              </Typography>
            )}
            {data.employeeCount && (
              <Typography variant="body2" gutterBottom>
                Employees: {data.employeeCount}
              </Typography>
            )}
          </Box>
        );

      case 'deal':
        return (
          <Box mt={2}>
            <Divider sx={{ mb: 2 }} />
            {data.probability && (
              <Typography variant="body2" gutterBottom>
                Probability: {data.probability}%
              </Typography>
            )}
            {data.expectedCloseDate && (
              <Typography variant="body2" gutterBottom>
                Expected Close: {formatDate(data.expectedCloseDate)}
              </Typography>
            )}
            {data.contact && (
              <Typography variant="body2" gutterBottom>
                Contact: {data.contact.firstName} {data.contact.lastName}
              </Typography>
            )}
            {data.company && (
              <Typography variant="body2" gutterBottom>
                Company: {data.company.name}
              </Typography>
            )}
          </Box>
        );

      case 'activity':
        return (
          <Box mt={2}>
            <Divider sx={{ mb: 2 }} />
            {data.description && (
              <Typography variant="body2" gutterBottom>
                {data.description}
              </Typography>
            )}
            {data.contact && (
              <Typography variant="body2" gutterBottom>
                Contact: {data.contact.firstName} {data.contact.lastName}
              </Typography>
            )}
            {data.assignedTo && (
              <Typography variant="body2" gutterBottom>
                Assigned to: {data.assignedTo.firstName} {data.assignedTo.lastName}
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const renderQuickActions = () => {
    if (!isMobile) return null;

    const quickActions = [];
    
    if (type === 'contact') {
      if (data.email) {
        quickActions.push(
          <IconButton
            key="email"
            size="small"
            href={`mailto:${data.email}`}
            sx={{ color: 'primary.main' }}
          >
            <EmailIcon />
          </IconButton>
        );
      }
      if (data.phone) {
        quickActions.push(
          <IconButton
            key="phone"
            size="small"
            href={`tel:${data.phone}`}
            sx={{ color: 'primary.main' }}
          >
            <PhoneIcon />
          </IconButton>
        );
      }
    }

    return quickActions.length > 0 ? (
      <Box display="flex" gap={1}>
        {quickActions}
      </Box>
    ) : null;
  };

  return (
    <Card
      sx={{
        mb: isMobile ? 1 : 2,
        cursor: onView ? 'pointer' : 'default',
        '&:hover': onView ? { boxShadow: 4 } : {},
        ...(compact && { '& .MuiCardContent-root': { pb: 1 } }),
      }}
      onClick={onView ? () => onView(data) : undefined}
    >
      <CardContent sx={{ pb: compact ? 1 : 2 }}>
        <Box display="flex" alignItems="flex-start" gap={2}>
          {!compact && getTypeIcon(type, data)}
          
          <Box flex={1}>
            {renderPrimaryContent()}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {renderQuickActions()}
            
            <IconButton size="small" onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>
          </Box>
        </Box>

        {renderSecondaryContent()}
      </CardContent>

      {(expandable || actions.length > 0) && (
        <CardActions sx={{ pt: 0, justifyContent: 'space-between' }}>
          {expandable && (
            <Button
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setExpanded(!expanded);
              }}
              endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            >
              {expanded ? 'Less' : 'More'}
            </Button>
          )}
          
          {actions.length > 0 && isMobile && (
            <Box display="flex" gap={1}>
              {actions.slice(0, 2).map((action, index) => (
                <Button
                  key={index}
                  size="small"
                  startIcon={action.icon}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick(data);
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </Box>
          )}
        </CardActions>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        {onView && (
          <MenuItem onClick={() => handleAction(() => onView(data))}>
            View Details
          </MenuItem>
        )}
        {onEdit && (
          <MenuItem onClick={() => handleAction(() => onEdit(data))}>
            Edit
          </MenuItem>
        )}
        {actions.map((action, index) => (
          <MenuItem key={index} onClick={() => handleAction(() => action.onClick(data))}>
            {action.icon && <Box mr={1}>{action.icon}</Box>}
            {action.label}
          </MenuItem>
        ))}
        {onDelete && (
          <MenuItem onClick={() => handleAction(() => onDelete(data))}>
            Delete
          </MenuItem>
        )}
      </Menu>
    </Card>
  );
};
