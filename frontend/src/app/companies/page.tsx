'use client'

import React, { useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { FiltersSection } from '@/components/ui/FiltersSection'
import { StatsGrid } from '@/components/ui/StatsCard'
import { CompaniesTable } from '../../components/companies/CompaniesTable'
import { CompanyForm } from '../../components/companies/CompanyForm'
import { CompanyFilters } from '../../components/companies/CompanyFilters'
import { CompanyHierarchy } from '../../components/companies/CompanyHierarchy'
import { formatNumber } from '@/lib/utils'
import { Building2, Plus, Download, Upload, GitFork as HierarchyIcon, Filter as FilterIcon, Search as SearchIcon } from 'lucide-react'

export default function CompaniesPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    industry: '',
    size: '',
    assignedToId: '',
    tags: [] as string[],
    minRevenue: '',
    maxRevenue: '',
    minEmployees: '',
    maxEmployees: '',
  })
  const [showFilters, setShowFilters] = useState(false)
  const [showCompanyForm, setShowCompanyForm] = useState(false)
  const [showHierarchy, setShowHierarchy] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<any>(null)

  const handleCreateCompany = () => {
    setSelectedCompany(null)
    setShowCompanyForm(true)
  }

  const handleEditCompany = (company: any) => {
    setSelectedCompany(company)
    setShowCompanyForm(true)
  }

  const handleCloseForm = () => {
    setShowCompanyForm(false)
    setSelectedCompany(null)
  }

  const handleFilterChange = (newFilters: any) => setFilters(newFilters)
  const handleExport = () => console.log('Export companies')
  const handleImport = () => console.log('Import companies')
  const handleShowHierarchy = () => setShowHierarchy(true)

  const activeFilterBadges = [
    ...(filters.industry ? [{ key: 'industry', label: 'Industry', value: filters.industry, onRemove: () => setFilters({ ...filters, industry: '' }) }] : []),
    ...(filters.size ? [{ key: 'size', label: 'Size', value: filters.size, onRemove: () => setFilters({ ...filters, size: '' }) }] : []),
    ...filters.tags.map((tag) => ({
      key: `tag:${tag}`,
      label: 'Tag',
      value: tag,
      onRemove: () => setFilters({ ...filters, tags: filters.tags.filter((t) => t !== tag) }),
    })),
  ]

  const stats: Array<{
    id: string
    label: string
    value: string | number
    change?: { value: number; type: 'increase' | 'decrease'; period?: string }
    icon?: React.ReactNode
  }> = [
    { id: 'total', label: 'Total Companies', value: formatNumber(89), change: { value: 12.5, type: 'increase', period: 'last month' }, icon: <Building2 className="h-6 w-6" /> },
    { id: 'enterprise', label: 'Enterprise', value: formatNumber(24), change: { value: 3.1, type: 'increase', period: 'last month' }, icon: <Building2 className="h-6 w-6" /> },
    { id: 'smb', label: 'SMB', value: formatNumber(50), change: { value: 1.2, type: 'decrease', period: 'last month' }, icon: <Building2 className="h-6 w-6" /> },
    { id: 'startups', label: 'Startups', value: formatNumber(15), change: { value: 6.4, type: 'increase', period: 'last month' }, icon: <Building2 className="h-6 w-6" /> },
  ]

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Companies"
          description="Track companies and organizational hierarchies"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton variant="outline" size="sm" leftIcon={<Download className="h-4 w-4" />} onClick={handleExport}>
                Export
              </EnhancedButton>
              <EnhancedButton variant="outline" size="sm" leftIcon={<Upload className="h-4 w-4" />} onClick={handleImport}>
                Import
              </EnhancedButton>
              <EnhancedButton variant="outline" size="sm" leftIcon={<HierarchyIcon className="h-4 w-4" />} onClick={handleShowHierarchy}>
                Hierarchy
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />} onClick={handleCreateCompany}>
                Add Company
              </EnhancedButton>
            </div>
          }
        />

        {/* Stats */}
        <StatsGrid items={stats} />

        {/* Search and Filters */}
        <FiltersSection
          search={searchQuery}
          onChangeSearch={setSearchQuery}
          placeholder="Search companies by name, domain, or industry..."
          showAdvanced={showFilters}
          onToggleAdvanced={() => setShowFilters((v) => !v)}
          activeFilters={activeFilterBadges}
          rightActions={
            <EnhancedButton variant={showFilters ? 'default' : 'outline'} size="sm" leftIcon={<FilterIcon className="h-4 w-4" />} onClick={() => setShowFilters((v) => !v)}>
              Filters
            </EnhancedButton>
          }
        >
          <CompanyFilters filters={filters} onChange={handleFilterChange} />
        </FiltersSection>

        {/* Companies Table */}
        <EnhancedCard variant="elevated" title="Companies" description="Browse and manage companies">
          <CompaniesTable searchQuery={searchQuery} filters={filters} onEditCompany={handleEditCompany} />
        </EnhancedCard>

        {/* Company Form Dialog */}
        {showCompanyForm && (
          <CompanyForm open={showCompanyForm} company={selectedCompany} onClose={handleCloseForm} />
        )}

        {/* Company Hierarchy Dialog */}
        {showHierarchy && (
          <CompanyHierarchy open={showHierarchy} onClose={() => setShowHierarchy(false)} />
        )}
      </Container>
    </AppShell>
  )
}
