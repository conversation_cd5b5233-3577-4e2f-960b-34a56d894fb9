'use client';

import React from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Container,
  CircularProgress,
  Al<PERSON>,
  Divider,
  Chip,
} from '@mui/material';
import { LoginOutlined, LogoutOutlined, AccountCircle } from '@mui/icons-material';

export default function AuthTestPage() {
  const { data: session, status } = useSession();

  const handleLogin = async () => {
    await signIn('keycloak', { callbackUrl: '/auth-test' });
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/auth-test' });
  };

  if (status === 'loading') {
    return (
      <Container maxWidth="md">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
        >
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box py={4}>
        <Typography variant="h4" component="h1" gutterBottom textAlign="center">
          Keycloak Authentication Test
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" textAlign="center" mb={4}>
          Test page to verify NextAuth + Keycloak integration
        </Typography>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Authentication Status
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Chip 
                label={status === 'authenticated' ? 'Authenticated' : 'Not Authenticated'} 
                color={status === 'authenticated' ? 'success' : 'error'}
                icon={<AccountCircle />}
              />
              <Typography variant="body2" color="text.secondary">
                Status: {status}
              </Typography>
            </Box>

            {status === 'authenticated' ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<LogoutOutlined />}
                onClick={handleLogout}
              >
                Sign Out
              </Button>
            ) : (
              <Button
                variant="contained"
                startIcon={<LoginOutlined />}
                onClick={handleLogin}
              >
                Sign In with Keycloak
              </Button>
            )}
          </CardContent>
        </Card>

        {status === 'authenticated' && session && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Session Information
              </Typography>
              <Box mb={2}>
                <Typography variant="subtitle2" color="primary">User Details:</Typography>
                <Typography variant="body2">Email: {session.user?.email || 'N/A'}</Typography>
                <Typography variant="body2">Name: {session.user?.name || 'N/A'}</Typography>
                <Typography variant="body2">Image: {session.user?.image || 'N/A'}</Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box mb={2}>
                <Typography variant="subtitle2" color="primary">Token Information:</Typography>
                <Typography variant="body2">Access Token: {session.accessToken ? 'Present' : 'Missing'}</Typography>
                <Typography variant="body2">Refresh Token: {session.refreshToken ? 'Present' : 'Missing'}</Typography>
                <Typography variant="body2">ID Token: {session.idToken ? 'Present' : 'Missing'}</Typography>
                <Typography variant="body2">Expires At: {session.expiresAt ? new Date(session.expiresAt * 1000).toLocaleString() : 'N/A'}</Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box mb={2}>
                <Typography variant="subtitle2" color="primary">Roles & Organization:</Typography>
                <Typography variant="body2">Roles: {session.roles ? session.roles.join(', ') : 'None'}</Typography>
                <Typography variant="body2">Organization ID: {session.orgId || 'N/A'}</Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box>
                <Typography variant="subtitle2" color="primary">Raw Session Data:</Typography>
                <Box 
                  component="pre" 
                  sx={{ 
                    backgroundColor: 'grey.100', 
                    p: 2, 
                    borderRadius: 1, 
                    overflow: 'auto',
                    fontSize: '0.75rem',
                    maxHeight: 300
                  }}
                >
                  {JSON.stringify(session, null, 2)}
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}

        {status === 'unauthenticated' && (
          <Alert severity="info">
            <Typography variant="body2">
              You are not currently authenticated. Click "Sign In with Keycloak" to test the authentication flow.
            </Typography>
          </Alert>
        )}

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Instructions
            </Typography>
            <Typography variant="body2" paragraph>
              1. Click "Sign In with Keycloak" to initiate the authentication flow
            </Typography>
            <Typography variant="body2" paragraph>
              2. You should be redirected to the Keycloak login page
            </Typography>
            <Typography variant="body2" paragraph>
              3. After successful login, you should be redirected back to this page
            </Typography>
            <Typography variant="body2" paragraph>
              4. The session information should be displayed above
            </Typography>
            <Typography variant="body2">
              5. Click "Sign Out" to test the logout flow
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
}
