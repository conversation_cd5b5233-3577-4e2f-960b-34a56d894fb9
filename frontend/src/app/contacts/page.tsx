'use client'

import React, { useState } from 'react'
import { App<PERSON>hell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedButton, IconButton } from '@/components/ui/enhanced-button'
import { EnhancedCard, ActionCard } from '@/components/ui/enhanced-card'
import { EnhancedBadge } from '@/components/ui/enhanced-badge'
import { FiltersSection } from '@/components/ui/FiltersSection'
import { StatsGrid } from '@/components/ui/StatsCard'
import { ContactsTable } from '../../components/contacts/ContactsTable'
import { ContactForm } from '../../components/contacts/ContactForm'
import { ContactFilters } from '../../components/contacts/ContactFilters'
import { formatNumber } from '@/lib/utils'
import { Users, Plus, MoreHorizontal, Download, Upload, Filter as FilterIcon } from 'lucide-react'

export default function ContactsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    leadStatus: '',
    leadSource: '',
    assignedToId: '',
    tags: [] as string[],
  })
  const [showFilters, setShowFilters] = useState(false)
  const [showContactForm, setShowContactForm] = useState(false)
  const [selectedContact, setSelectedContact] = useState<any>(null)

  const handleCreateContact = () => {
    setSelectedContact(null)
    setShowContactForm(true)
  }

  const handleEditContact = (contact: any) => {
    setSelectedContact(contact)
    setShowContactForm(true)
  }

  const handleCloseForm = () => {
    setShowContactForm(false)
    setSelectedContact(null)
  }

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
  }

  const handleExport = () => {
    console.log('Export contacts')
  }

  const handleImport = () => {
    console.log('Import contacts')
  }

  const activeFilterBadges = [
    ...(filters.leadStatus ? [{ key: 'leadStatus', label: 'Status', value: filters.leadStatus, onRemove: () => setFilters({ ...filters, leadStatus: '' }) }] : []),
    ...(filters.leadSource ? [{ key: 'leadSource', label: 'Source', value: filters.leadSource, onRemove: () => setFilters({ ...filters, leadSource: '' }) }] : []),
    ...filters.tags.map((tag) => ({
      key: `tag:${tag}`,
      label: 'Tag',
      value: tag,
      onRemove: () => setFilters({ ...filters, tags: filters.tags.filter((t) => t !== tag) }),
    })),
  ]

  // Mock stats
  const stats = [
    { id: 'total', label: 'Total Contacts', value: formatNumber(1247), change: { value: 8.2, type: 'increase', period: 'last month' }, icon: <Users className="h-6 w-6" /> },
    { id: 'leads', label: 'New Leads', value: formatNumber(182), change: { value: 5.4, type: 'increase', period: 'last 7 days' }, icon: <Users className="h-6 w-6" /> },
    { id: 'qualified', label: 'Qualified', value: formatNumber(320), change: { value: 2.1, type: 'decrease', period: 'last month' }, icon: <Users className="h-6 w-6" /> },
    { id: 'converted', label: 'Converted', value: formatNumber(96), change: { value: 1.2, type: 'increase', period: 'last month' }, icon: <Users className="h-6 w-6" /> },
  ]

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Contacts"
          description="Manage your customer contacts and relationships"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton variant="outline" size="sm" leftIcon={<Download className="h-4 w-4" />} onClick={handleExport}>
                Export
              </EnhancedButton>
              <EnhancedButton variant="outline" size="sm" leftIcon={<Upload className="h-4 w-4" />} onClick={handleImport}>
                Import
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />} onClick={handleCreateContact}>
                Add Contact
              </EnhancedButton>
            </div>
          }
        />

        {/* Stats */}
        <StatsGrid items={stats as Array<{
          id: string
          label: string
          value: string | number
          change?: { value: number; type: 'increase' | 'decrease'; period?: string }
          icon?: React.ReactNode
        }>} />

        {/* Search and Filters */}
        <FiltersSection
          search={searchQuery}
          onChangeSearch={setSearchQuery}
          placeholder="Search contacts by name, email, or company..."
          showAdvanced={showFilters}
          onToggleAdvanced={() => setShowFilters((v) => !v)}
          activeFilters={activeFilterBadges}
          rightActions={
            <EnhancedButton variant={showFilters ? 'default' : 'outline'} size="sm" leftIcon={<FilterIcon className="h-4 w-4" />} onClick={() => setShowFilters((v) => !v)}>
              Filters
            </EnhancedButton>
          }
        >
          <ContactFilters filters={filters} onChange={handleFilterChange} />
        </FiltersSection>

        {/* Contacts Table */}
        <EnhancedCard variant="elevated" title="Contacts List" description="Browse and manage contacts">
          <ContactsTable searchQuery={searchQuery} filters={filters} onEditContact={handleEditContact} />
        </EnhancedCard>

        {/* Contact Form Dialog */}
        {showContactForm && (
          <ContactForm open={showContactForm} contact={selectedContact} onClose={handleCloseForm} />
        )}
      </Container>
    </AppShell>
  )
}
