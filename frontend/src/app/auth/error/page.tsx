'use client';

import React from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import {
  <PERSON>,
  Button,
  Card,
  CardContent,
  Typography,
  Container,
  Alert,
} from '@mui/material';
import { ErrorOutline, Home, Refresh } from '@mui/icons-material';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const error = searchParams.get('error');

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case 'Configuration':
        return 'There is a problem with the server configuration.';
      case 'AccessDenied':
        return 'Access was denied. You may not have permission to access this application.';
      case 'Verification':
        return 'The verification token has expired or has already been used.';
      case 'Default':
        return 'An error occurred during authentication.';
      default:
        return 'An unexpected error occurred during authentication.';
    }
  };

  const getErrorDetails = (errorCode: string | null) => {
    switch (errorCode) {
      case 'Configuration':
        return 'Please contact your system administrator to resolve this issue.';
      case 'AccessDenied':
        return 'Please contact your organization administrator if you believe you should have access.';
      case 'Verification':
        return 'Please try signing in again.';
      default:
        return 'Please try again or contact support if the problem persists.';
    }
  };

  const handleRetry = () => {
    router.push('/login');
  };

  const handleHome = () => {
    router.push('/');
  };

  return (
    <Container maxWidth="sm">
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <Card sx={{ width: '100%', maxWidth: 500 }}>
          <CardContent sx={{ p: 4 }}>
            <Box textAlign="center" mb={3}>
              <ErrorOutline 
                sx={{ 
                  fontSize: 64, 
                  color: 'error.main',
                  mb: 2 
                }} 
              />
              <Typography variant="h4" component="h1" gutterBottom>
                Authentication Error
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                There was a problem signing you in
              </Typography>
            </Box>

            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Error:</strong> {getErrorMessage(error)}
              </Typography>
              <Typography variant="body2">
                {getErrorDetails(error)}
              </Typography>
            </Alert>

            {error && (
              <Box mb={3}>
                <Typography variant="caption" color="text.secondary">
                  Error Code: {error}
                </Typography>
              </Box>
            )}

            <Box display="flex" gap={2} justifyContent="center">
              <Button
                variant="contained"
                startIcon={<Refresh />}
                onClick={handleRetry}
              >
                Try Again
              </Button>
              <Button
                variant="outlined"
                startIcon={<Home />}
                onClick={handleHome}
              >
                Go Home
              </Button>
            </Box>

            <Box textAlign="center" mt={3}>
              <Typography variant="body2" color="text.secondary">
                If you continue to experience issues, please contact your system administrator.
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
}
