'use client'

import React, { useEffect, useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { Users, UserPlus, RefreshCcw } from 'lucide-react'

type UserItem = {
  id: string
  name: string
  email?: string
  role?: string
  status?: 'active' | 'invited' | 'disabled'
}

export default function UsersPage() {
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<UserItem[]>([])

  useEffect(() => {
    setLoading(true)
    const t = setTimeout(() => {
      setUsers([
        { id: 'u1', name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'active' },
        { id: 'u2', name: '<PERSON>', email: '<EMAIL>', role: 'Manager', status: 'active' },
        { id: 'u3', name: '<PERSON>', email: '<EMAIL>', role: 'Viewer', status: 'invited' },
      ])
      setLoading(false)
    }, 500)
    return () => clearTimeout(t)
  }, [])

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Users"
          description="Manage user accounts, invitations, and statuses"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton size="sm" variant="outline" leftIcon={<RefreshCcw className="h-4 w-4" />}>
                Refresh
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<UserPlus className="h-4 w-4" />}>
                Invite User
              </EnhancedButton>
            </div>
          }
        />

        <EnhancedCard
          variant="elevated"
          title="Overview"
          description="Glassy placeholder for users list and detail drawers."
          className="backdrop-blur-md bg-white/60 dark:bg-zinc-900/40"
        >
          <div className="flex flex-col items-center justify-center text-center py-10">
            <div className="p-3 rounded-xl bg-primary/10 text-primary mb-3">
              <Users className="h-6 w-6" />
            </div>
            <p className="text-lg font-semibold">User management coming soon</p>
            <p className="text-sm text-muted-foreground max-w-md">
              We will add searchable tables, bulk actions, and role assignment flows here.
            </p>
          </div>
        </EnhancedCard>

        <EnhancedCard variant="outlined" title="Sample Data" description={loading ? 'Loading...' : 'Mock users'}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {users.map(u => (
              <div key={u.id} className="rounded-lg border border-border/50 p-3 bg-background/50">
                <div className="font-medium">{u.name}</div>
                <div className="text-xs text-muted-foreground">{u.email}</div>
                <div className="mt-2 text-xs">Role: {u.role ?? '-'}</div>
                <div className="text-xs">Status: {u.status ?? '-'}</div>
              </div>
            ))}
          </div>
        </EnhancedCard>
      </Container>
    </AppShell>
  )
}