import NextAuth from 'next-auth'
import Key<PERSON>loakProvider from 'next-auth/providers/keycloak'
import type { NextAuthOptions } from 'next-auth'

const authOptions: NextAuthOptions = {
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID!,
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET!,
      issuer: process.env.KEYCLOAK_ISSUER!,
    }),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      if (account) {
        // Guard undefined fields to satisfy exactOptionalPropertyTypes
        token.accessToken = (account.access_token ?? '') as string
        token.refreshToken = (account.refresh_token ?? '') as string
        token.expiresAt = (account.expires_at ?? 0) as number
        token.idToken = (account.id_token ?? '') as string
        token.provider = account.provider
      }
      if (profile) {
        token.profile = profile
        token.roles = ((profile as any).realm_access?.roles || []) as string[]
        // Ensure orgId is always a string (empty string when absent) to satisfy exactOptionalPropertyTypes
        token.orgId = (((profile as any).org_id as string | undefined) ?? '') as string
      }
      return token
    },
    async session({ session, token }) {
      // Only expose minimal data to the client to reduce risk
      ;(session as any).accessToken = (token.accessToken as string) ?? ''
      ;(session as any).idToken = (token.idToken as string) ?? ''
      ;(session as any).roles = ((token.roles as string[]) ?? [])
      ;(session as any).orgId = ((token.orgId as string) ?? null)
      ;(session as any).expiresAt = ((token.expiresAt as number) ?? null)
      return session
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith('/')) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60,
  },
  // Harden cookies for session (httpOnly, sameSite, secure in production)
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.callback-url' : 'next-auth.callback-url',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production' ? '__Host-next-auth.csrf-token' : 'next-auth.csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error(code, metadata) {
      console.error('NextAuth Error:', code, metadata)
    },
    warn(code) {
      console.warn('NextAuth Warning:', code)
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === 'development') {
        console.log('NextAuth Debug:', code, metadata)
      }
    },
  },
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
