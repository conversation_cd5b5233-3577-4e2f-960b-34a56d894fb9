'use client'

import React from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Users, Building2, TrendingUp, DollarSign, Activity, Calendar, BarChart3, ArrowUpRight, ArrowDownRight, Eye, Plus, Info } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

export default function DashboardPage() {
  const stats = {
    totalContacts: 1247,
    totalCompanies: 156,
    totalDeals: 89,
    totalRevenue: 2450000
  }

  const recentActivity = [
    {
      id: '1',
      type: 'deal',
      title: 'New deal created: Enterprise Software License',
      description: 'Deal worth $50,000 added to pipeline',
      timestamp: new Date().toISOString(),
      user: '<PERSON>',
    },
    {
      id: '2',
      type: 'contact',
      title: 'Contact updated: <PERSON>',
      description: 'Contact information and preferences updated',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      user: '<PERSON>',
    },
    {
      id: '3',
      type: 'company',
      title: 'New company added: TechCorp Solutions',
      description: 'Enterprise client with 500+ employees',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      user: 'Mike Wilson',
    },
  ]

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Dashboard"
          description="Overview of your CRM performance and activity"
          actions={
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-primary/10 text-primary hover:bg-primary/15">
                <Info className="h-3 w-3 mr-1" />
                Demo Mode
              </Badge>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Quick Add
              </Button>
            </div>
          }
        />

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
              <Users className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalContacts.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground mt-1 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1 text-green-600" />
                +8.2% from last month
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Companies</CardTitle>
              <Building2 className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCompanies.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground mt-1 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1 text-green-600" />
                +12.5% from last month
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Deals</CardTitle>
              <TrendingUp className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDeals.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground mt-1 flex items-center">
                <ArrowDownRight className="h-3 w-3 mr-1 text-red-600" />
                -3.1% from last month
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
              <p className="text-xs text-muted-foreground mt-1 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1 text-green-600" />
                +15.3% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest updates and changes in your CRM</CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                View All
                <Eye className="h-4 w-4 ml-1" />
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="p-2 rounded-full bg-primary/10">
                      <Activity className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm">{activity.title}</p>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatDate(activity.timestamp)}
                        </span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{activity.user}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Add Contact
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Building2 className="h-4 w-4 mr-2" />
                  Add Company
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Create Deal
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Meeting
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Overview */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Analytics Overview</CardTitle>
              <CardDescription>Key performance metrics and trends</CardDescription>
            </div>
            <Button variant="outline" size="sm">
              View Details
              <ArrowUpRight className="h-4 w-4 ml-1" />
            </Button>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Conversion Rate</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">24.8%</span>
                  <div className="flex items-center text-green-600 text-sm">
                    <ArrowUpRight className="h-3 w-3" />
                    <span>+2.1%</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Monthly Growth</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">12.5%</span>
                  <div className="flex items-center text-green-600 text-sm">
                    <ArrowUpRight className="h-3 w-3" />
                    <span>+0.8%</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Activity Score</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">87</span>
                  <div className="flex items-center text-red-600 text-sm">
                    <ArrowDownRight className="h-3 w-3" />
                    <span>-3</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Explore */}
        <section className="space-y-3">
          <div className="flex items-end justify-between">
            <div>
              <h2 className="text-lg font-semibold">Explore OneCRM</h2>
              <p className="text-sm text-muted-foreground">Navigate to key CRM modules</p>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 justify-start">
              <Users className="h-5 w-5 mr-2" />
              Contacts
            </Button>
            <Button variant="outline" className="h-20 justify-start">
              <Building2 className="h-5 w-5 mr-2" />
              Companies
            </Button>
            <Button variant="outline" className="h-20 justify-start">
              <TrendingUp className="h-5 w-5 mr-2" />
              Deals
            </Button>
            <Button variant="outline" className="h-20 justify-start">
              <BarChart3 className="h-5 w-5 mr-2" />
              Analytics
            </Button>
          </div>
        </section>
      </Container>
    </AppShell>
  )
}
