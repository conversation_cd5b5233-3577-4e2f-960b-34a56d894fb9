'use client'

import React, { useState } from 'react'
import { App<PERSON><PERSON>, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { EnhancedCard } from '@/components/ui/enhanced-card'
/* Tabs import removed due to casing conflict; rendering tabs via local config in EnhancedCards */
import { StatsGrid } from '@/components/ui/StatsCard'
import { ActivitiesCard } from '@/components/ui/ActivitiesList'
import { AdvancedAnalytics } from '../../components/dashboard/AdvancedAnalytics'
import { InteractivePipeline } from '../../components/deals/InteractivePipeline'
import { formatNumber } from '@/lib/utils'
import { mutate } from 'swr'
import { Download, RefreshCcw, BarChart3, TrendingUp, Pie<PERSON>hart, Filter as FilterIcon } from 'lucide-react'

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d')

  const handleRefresh = () => {
    mutate(key => typeof key === 'string' && key.startsWith('/api/analytics'))
    mutate('/api/deals/pipeline')
    mutate('/api/contacts/stats')
    mutate('/api/companies/stats')
    mutate('/api/activities/stats')
  }

  const handleExport = () => {
    console.log('Export analytics data')
  }

  const timeRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '6m', label: 'Last 6 months' },
    { value: '1y', label: 'Last year' },
    { value: 'ytd', label: 'Year to date' },
  ]

  const stats: Array<{
    id: string
    label: string
    value: string | number
    change?: { value: number; type: 'increase' | 'decrease'; period?: string }
    icon?: React.ReactNode
  }> = [
    { id: 'contacts', label: 'Active Contacts', value: formatNumber(1247), change: { value: 2.1, type: 'increase', period: '30d' }, icon: <BarChart3 className="h-6 w-6" /> },
    { id: 'companies', label: 'Companies', value: formatNumber(89), change: { value: 1.4, type: 'increase', period: '30d' }, icon: <BarChart3 className="h-6 w-6" /> },
    { id: 'deals', label: 'Active Deals', value: formatNumber(156), change: { value: 0.7, type: 'decrease', period: '30d' }, icon: <BarChart3 className="h-6 w-6" /> },
  ]

  const tabs = [
    {
      key: 'overview',
      label: 'Overview',
      icon: <BarChart3 className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <EnhancedCard title="Advanced Analytics" description="Key metrics and trends">
            <AdvancedAnalytics timeRange={timeRange} />
          </EnhancedCard>
        </div>
      ),
    },
    {
      key: 'pipeline',
      label: 'Sales Pipeline',
      icon: <TrendingUp className="h-4 w-4" />,
      content: (
        <EnhancedCard title="Interactive Sales Pipeline" description="Drag and drop deals between stages">
          <InteractivePipeline searchQuery="" filters={{}} onEditDeal={(deal) => console.log('Edit deal:', deal)} />
        </EnhancedCard>
      ),
    },
    {
      key: 'performance',
      label: 'Performance',
      icon: <PieChart className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <EnhancedCard title="Team Performance" description="Individual and team performance metrics">
            {/* Add team performance charts here */}
            <div className="h-40 bg-muted rounded" />
          </EnhancedCard>
          <EnhancedCard title="Activity Analysis" description="Activity patterns and productivity insights">
            {/* Add activity analysis charts here */}
            <div className="h-40 bg-muted rounded" />
          </EnhancedCard>
          <EnhancedCard className="lg:col-span-2" title="Revenue Forecasting" description="Predictive revenue analysis based on current pipeline">
            {/* Add forecasting charts here */}
            <div className="h-48 bg-muted rounded" />
          </EnhancedCard>
        </div>
      ),
    },
  ]

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Analytics & Reports"
          description="Advanced reporting and business insights"
          actions={
            <div className="flex items-center gap-2">
              <select
                className="h-9 rounded-md border bg-background px-3 text-sm"
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
              >
                {timeRangeOptions.map((o) => (
                  <option key={o.value} value={o.value}>
                    {o.label}
                  </option>
                ))}
              </select>
              <EnhancedButton variant="outline" size="sm" leftIcon={<RefreshCcw className="h-4 w-4" />} onClick={handleRefresh}>
                Refresh
              </EnhancedButton>
              <EnhancedButton variant="outline" size="sm" leftIcon={<Download className="h-4 w-4" />} onClick={handleExport}>
                Export
              </EnhancedButton>
            </div>
          }
        />

        {/* Stats */}
        <StatsGrid items={stats} />

        {/* Tabs */}
        <EnhancedCard title="Overview" description="Key metrics and trends">
          <div className="space-y-6">
            <EnhancedCard title="Advanced Analytics" description="Key metrics and trends">
              <AdvancedAnalytics timeRange={timeRange} />
            </EnhancedCard>
          </div>
        </EnhancedCard>

        <EnhancedCard title="Sales Pipeline" description="Drag and drop deals between stages">
          <InteractivePipeline searchQuery="" filters={{}} onEditDeal={(deal) => console.log('Edit deal:', deal)} />
        </EnhancedCard>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <EnhancedCard title="Team Performance" description="Individual and team performance metrics">
            <div className="h-40 bg-muted rounded" />
          </EnhancedCard>
          <EnhancedCard title="Activity Analysis" description="Activity patterns and productivity insights">
            <div className="h-40 bg-muted rounded" />
          </EnhancedCard>
          <EnhancedCard className="lg:col-span-2" title="Revenue Forecasting" description="Predictive revenue analysis based on current pipeline">
            <div className="h-48 bg-muted rounded" />
          </EnhancedCard>
        </div>
      </Container>
    </AppShell>
  )
}
