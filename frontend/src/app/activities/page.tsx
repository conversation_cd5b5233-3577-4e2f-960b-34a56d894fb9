'use client'

import React, { useEffect, useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { ActivitiesCard } from '@/components/ui/ActivitiesList'
import { Activity, RefreshCcw, Plus } from 'lucide-react'

type ActivityItem = {
  id: string
  type?: 'activity' | 'deal' | 'company' | 'contact' | 'meeting'
  title: string
  description?: string
  timestamp: string | Date
  user?: string
}

export default function ActivitiesPage() {
  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState<ActivityItem[]>([])

  useEffect(() => {
    setLoading(true)
    const t = setTimeout(() => {
      setItems([
        { id: 'a1', type: 'activity', title: 'Follow-up email sent', description: 'Sent to <PERSON> from TechCorp', timestamp: new Date(), user: '<PERSON>' },
        { id: 'a2', type: 'meeting', title: 'Scheduled demo', description: 'Demo booked for 3 PM Fri', timestamp: new Date(Date.now() - 3600e3), user: '<PERSON> Lee' },
      ])
      setLoading(false)
    }, 500)
    return () => clearTimeout(t)
  }, [])

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Activities"
          description="Recent activities across your organization"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton size="sm" variant="outline" leftIcon={<RefreshCcw className="h-4 w-4" />}>
                Refresh
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />}>
                Log Activity
              </EnhancedButton>
            </div>
          }
        />

        <EnhancedCard
          variant="elevated"
          title="Overview"
          description="Glassy placeholder section for upcoming list/detail/create-edit screens."
          className="backdrop-blur-md bg-white/60 dark:bg-zinc-900/40"
        >
          <div className="flex flex-col items-center justify-center text-center py-10">
            <div className="p-3 rounded-xl bg-primary/10 text-primary mb-3">
              <Activity className="h-6 w-6" />
            </div>
            <p className="text-lg font-semibold">Glassy placeholder</p>
            <p className="text-sm text-muted-foreground max-w-md">
              Tomorrow we will extend this with list filters, detail panels, and inline create/edit.
            </p>
          </div>
        </EnhancedCard>

        <ActivitiesCard
          title="Recent Activities"
          description="Latest updates"
          items={items}
        />
      </Container>
    </AppShell>
  )
}