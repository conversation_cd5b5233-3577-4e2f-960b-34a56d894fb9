'use client'

import React, { useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { FiltersSection } from '@/components/ui/FiltersSection'
import { StatsGrid } from '@/components/ui/StatsCard'
import { DealsTable } from '../../components/deals/DealsTable'
import { DealsKanban } from '../../components/deals/DealsKanban'
import { DealsForecast } from '../../components/deals/DealsForecast'
import { DealForm } from '../../components/deals/DealForm'
import { DealFilters } from '../../components/deals/DealFilters'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { TrendingUp, Table2, LayoutPanelLeft, ChartNoAxesCombined, Plus, Filter as FilterIcon, Download, Upload } from 'lucide-react'

type ViewMode = 'table' | 'kanban' | 'forecast'

export default function DealsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('kanban')
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    stage: '',
    ownerId: '',
    contactId: '',
    companyId: '',
    source: '',
    type: '',
    priority: '',
    minAmount: '',
    maxAmount: '',
    minProbability: '',
    maxProbability: '',
    expectedCloseDateFrom: '',
    expectedCloseDateTo: '',
  })
  const [showFilters, setShowFilters] = useState(false)
  const [showDealForm, setShowDealForm] = useState(false)
  const [selectedDeal, setSelectedDeal] = useState<any>(null)

  const handleCreateDeal = () => {
    setSelectedDeal(null)
    setShowDealForm(true)
  }
  const handleEditDeal = (deal: any) => {
    setSelectedDeal(deal)
    setShowDealForm(true)
  }
  const handleCloseForm = () => {
    setShowDealForm(false)
    setSelectedDeal(null)
  }
  const handleFilterChange = (newFilters: any) => setFilters(newFilters)
  const handleExport = () => console.log('Export deals')
  const handleImport = () => console.log('Import deals')

  const activeFilterBadges = [
    ...(filters.stage ? [{ key: 'stage', label: 'Stage', value: filters.stage, onRemove: () => setFilters({ ...filters, stage: '' }) }] : []),
    ...(filters.priority ? [{ key: 'priority', label: 'Priority', value: filters.priority, onRemove: () => setFilters({ ...filters, priority: '' }) }] : []),
  ]

  const stats: Array<{
    id: string
    label: string
    value: string | number
    change?: { value: number; type: 'increase' | 'decrease'; period?: string }
    icon?: React.ReactNode
  }> = [
    { id: 'active', label: 'Active Deals', value: formatNumber(156), change: { value: 3.1, type: 'decrease', period: 'last month' }, icon: <TrendingUp className="h-6 w-6" /> },
    { id: 'pipeline', label: 'Pipeline Value', value: formatCurrency(820000), change: { value: 4.6, type: 'increase', period: 'last month' }, icon: <TrendingUp className="h-6 w-6" /> },
    { id: 'won', label: 'Won This Month', value: formatCurrency(210000), change: { value: 2.2, type: 'increase', period: 'last 7 days' }, icon: <TrendingUp className="h-6 w-6" /> },
    { id: 'velocity', label: 'Avg. Velocity', value: '34 days', change: { value: 1.1, type: 'decrease', period: 'last month' }, icon: <TrendingUp className="h-6 w-6" /> },
  ]

  // Local tabs definition removed (using button toggles below)

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Deals"
          description="Manage sales pipeline and deal progression"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton variant="outline" size="sm" leftIcon={<Download className="h-4 w-4" />} onClick={handleExport}>
                Export
              </EnhancedButton>
              <EnhancedButton variant="outline" size="sm" leftIcon={<Upload className="h-4 w-4" />} onClick={handleImport}>
                Import
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />} onClick={handleCreateDeal}>
                Add Deal
              </EnhancedButton>
            </div>
          }
        />

        {/* Stats */}
        <StatsGrid items={stats} />

        {/* Filters */}
        <FiltersSection
          search={searchQuery}
          onChangeSearch={setSearchQuery}
          placeholder="Search deals by title, company, or contact..."
          showAdvanced={showFilters}
          onToggleAdvanced={() => setShowFilters((v) => !v)}
          activeFilters={activeFilterBadges}
          rightActions={
            <EnhancedButton variant={showFilters ? 'default' : 'outline'} size="sm" leftIcon={<FilterIcon className="h-4 w-4" />} onClick={() => setShowFilters((v) => !v)}>
              Filters
            </EnhancedButton>
          }
        >
          <DealFilters filters={filters} onChange={handleFilterChange} />
        </FiltersSection>

        {/* Content */}
        <EnhancedCard variant="elevated" title="Pipeline" description="Switch views to analyze your pipeline">
          <div className="flex items-center gap-2 mb-4">
            <EnhancedButton variant={viewMode === 'kanban' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('kanban')} leftIcon={<LayoutPanelLeft className="h-4 w-4" />}>
              Kanban
            </EnhancedButton>
            <EnhancedButton variant={viewMode === 'table' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('table')} leftIcon={<Table2 className="h-4 w-4" />}>
              Table
            </EnhancedButton>
            <EnhancedButton variant={viewMode === 'forecast' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('forecast')} leftIcon={<ChartNoAxesCombined className="h-4 w-4" />}>
              Forecast
            </EnhancedButton>
          </div>
          {viewMode === 'kanban' && <DealsKanban searchQuery={searchQuery} filters={filters} onEditDeal={handleEditDeal} />}
          {viewMode === 'table' && <DealsTable searchQuery={searchQuery} filters={filters} onEditDeal={handleEditDeal} />}
          {viewMode === 'forecast' && <DealsForecast />}
        </EnhancedCard>

        {/* Deal Form Dialog */}
        {showDealForm && (
          <DealForm open={showDealForm} deal={selectedDeal} onClose={handleCloseForm} />
        )}
      </Container>
    </AppShell>
  )
}
