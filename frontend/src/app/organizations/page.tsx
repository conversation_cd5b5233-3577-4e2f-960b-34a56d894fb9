'use client'

import React, { useEffect, useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { Building2, Plus, RefreshCcw } from 'lucide-react'

export default function OrganizationsPage() {
  const [loading, setLoading] = useState(false)
  const [orgs, setOrgs] = useState<Array<{ id: string; name: string }>>([])

  useEffect(() => {
    setLoading(true)
    // Mock fetch
    const t = setTimeout(() => {
      setOrgs([
        { id: 'org_1', name: 'Acme Inc.' },
        { id: 'org_2', name: 'TechCorp' },
      ])
      setLoading(false)
    }, 500)
    return () => clearTimeout(t)
  }, [])

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Organizations"
          description="Manage tenant organizations and hierarchy"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton size="sm" variant="outline" leftIcon={<RefreshCcw className="h-4 w-4" />}>
                Refresh
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />}>
                New Organization
              </EnhancedButton>
            </div>
          }
        />

        <EnhancedCard
          variant="elevated"
          title="Overview"
          description="This is a placeholder section. Lists and details will be added here."
          className="backdrop-blur-md bg-white/60 dark:bg-zinc-900/40"
        >
          <div className="flex flex-col items-center justify-center text-center py-10">
            <div className="p-3 rounded-xl bg-primary/10 text-primary mb-3">
              <Building2 className="h-6 w-6" />
            </div>
            <p className="text-lg font-semibold">Glassy placeholder</p>
            <p className="text-sm text-muted-foreground max-w-md">
              Start by creating or importing organizations. Tomorrow we will extend this with list, detail and create/edit screens.
            </p>
            <div className="mt-6 flex items-center gap-2">
              <EnhancedButton variant="outline" size="sm">Import</EnhancedButton>
              <EnhancedButton size="sm">Create Organization</EnhancedButton>
            </div>
          </div>
        </EnhancedCard>
      </Container>
    </AppShell>
  )
}