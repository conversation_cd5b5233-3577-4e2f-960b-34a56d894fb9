'use client'

import React, { useEffect, useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { ShieldCheck, Plus, RefreshCcw } from 'lucide-react'

type RoleItem = {
  id: string
  name: string
  description?: string
  usersCount?: number
  permissions?: string[]
}

export default function RBACPage() {
  const [loading, setLoading] = useState(false)
  const [roles, setRoles] = useState<RoleItem[]>([])

  useEffect(() => {
    setLoading(true)
    const t = setTimeout(() => {
      setRoles([
        { id: 'r1', name: 'Admin', description: 'Full access', usersCount: 2, permissions: ['*'] },
        { id: 'r2', name: 'Manager', description: 'Team and pipeline management', usersCount: 5, permissions: ['deals:*', 'contacts:read', 'companies:read'] },
        { id: 'r3', name: 'Viewer', description: 'Read-only access', usersCount: 8, permissions: ['contacts:read', 'companies:read', 'deals:read'] },
      ])
      setLoading(false)
    }, 500)
    return () => clearTimeout(t)
  }, [])

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="RBAC"
          description="Roles and permissions for your organization"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton size="sm" variant="outline" leftIcon={<RefreshCcw className="h-4 w-4" />}>
                Refresh
              </EnhancedButton>
              <EnhancedButton size="sm" leftIcon={<Plus className="h-4 w-4" />}>
                New Role
              </EnhancedButton>
            </div>
          }
        />

        <EnhancedCard
          variant="elevated"
          title="Overview"
          description="Glassy placeholder to be extended with role editor and permission matrix."
          className="backdrop-blur-md bg-white/60 dark:bg-zinc-900/40"
        >
          <div className="flex flex-col items-center justify-center text-center py-10">
            <div className="p-3 rounded-xl bg-primary/10 text-primary mb-3">
              <ShieldCheck className="h-6 w-6" />
            </div>
            <p className="text-lg font-semibold">Role-based access control</p>
            <p className="text-sm text-muted-foreground max-w-md">
              Tomorrow we will add CRUD for roles, permission toggles, and policy previews.
            </p>
          </div>
        </EnhancedCard>

        <EnhancedCard variant="outlined" title="Sample Roles" description={loading ? 'Loading...' : 'Mock roles'}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {roles.map(r => (
              <div key={r.id} className="rounded-lg border border-border/50 p-3 bg-background/50">
                <div className="font-medium">{r.name}</div>
                <div className="text-xs text-muted-foreground">{r.description ?? '-'}</div>
                <div className="mt-2 text-xs">Users: {r.usersCount ?? 0}</div>
                <div className="text-xs">Perms: {r.permissions?.join(', ') ?? '-'}</div>
              </div>
            ))}
          </div>
        </EnhancedCard>
      </Container>
    </AppShell>
  )
}