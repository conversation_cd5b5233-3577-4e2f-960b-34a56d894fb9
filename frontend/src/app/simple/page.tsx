'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Card, 
  CardContent, 
  Grid, 
  Button,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';

export default function SimplePage() {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('http://localhost:3002/api/health');
      const data = await response.json();
      setHealthStatus(data);
    } catch (err) {
      setError('Failed to connect to backend API');
      console.error('Health check failed:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom align="center">
        🚀 OneCRM - Simple Demo
      </Typography>
      
      <Typography variant="h6" align="center" color="text.secondary" paragraph>
        Enterprise-grade Multi-tenant CRM Platform
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {/* Backend Health Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                Backend API Status
              </Typography>
              
              {loading && (
                <Box display="flex" alignItems="center" gap={2}>
                  <CircularProgress size={20} />
                  <Typography>Checking backend...</Typography>
                </Box>
              )}
              
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {healthStatus && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    Backend API is running successfully!
                  </Alert>
                  <Typography variant="body2" component="pre" sx={{ 
                    backgroundColor: '#f5f5f5', 
                    p: 2, 
                    borderRadius: 1,
                    fontSize: '0.8rem'
                  }}>
                    {JSON.stringify(healthStatus, null, 2)}
                  </Typography>
                </Box>
              )}
              
              <Button 
                variant="contained" 
                onClick={checkHealth} 
                disabled={loading}
                sx={{ mt: 2 }}
              >
                Refresh Status
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Frontend Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                Frontend Status
              </Typography>
              
              <Alert severity="success" sx={{ mb: 2 }}>
                Frontend is running successfully!
              </Alert>
              
              <Typography variant="body2" component="pre" sx={{ 
                backgroundColor: '#f5f5f5', 
                p: 2, 
                borderRadius: 1,
                fontSize: '0.8rem'
              }}>
                {JSON.stringify({
                  status: 'ok',
                  framework: 'Next.js 14.2.30',
                  ui: 'Material-UI',
                  port: 3000,
                  timestamp: new Date().toISOString()
                }, null, 2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Services Overview */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                🌟 OneCRM Services Overview
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Typography variant="h6" color="primary">Frontend</Typography>
                    <Typography variant="body2">Next.js + Material-UI</Typography>
                    <Typography variant="caption" color="success.main">✅ Port 3000</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Typography variant="h6" color="primary">Backend API</Typography>
                    <Typography variant="body2">NestJS + TypeScript</Typography>
                    <Typography variant="caption" color="success.main">✅ Port 3002</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Typography variant="h6" color="primary">Database</Typography>
                    <Typography variant="body2">PostgreSQL 15</Typography>
                    <Typography variant="caption" color="success.main">✅ Port 5432</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Typography variant="h6" color="primary">Cache</Typography>
                    <Typography variant="body2">Redis 7</Typography>
                    <Typography variant="caption" color="success.main">✅ Port 6379</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Features Overview */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                🎯 Enterprise Features Implemented
              </Typography>
              
              <Grid container spacing={2}>
                {[
                  'Multi-tenant Architecture',
                  'Subscription Management',
                  'Usage Monitoring',
                  'Feature Gating',
                  'Admin Dashboard',
                  'Customer Portal',
                  'Real-time Analytics',
                  'Hot Reload Development',
                  'Mock API System',
                  'Production-Ready Logging'
                ].map((feature, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Box sx={{ 
                      p: 2, 
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      backgroundColor: '#f8f9fa'
                    }}>
                      <Typography variant="body1">✅ {feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          OneCRM - Enterprise-grade SaaS CRM Platform
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Running with hot reload enabled and mock services for development
        </Typography>
      </Box>
    </Container>
  );
}
