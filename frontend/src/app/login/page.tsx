'use client'

import React, { useEffect, useState } from 'react'
import { signIn, useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedBadge } from '@/components/ui/enhanced-badge'
import { cn } from '@/lib/utils'
import { Loader2, LogIn, ShieldCheck, AlertCircle } from 'lucide-react'

export default function LoginPage() {
  const { status } = useSession()
  const router = useRouter()
  const search = useSearchParams()
  const [loading, setLoading] = useState(false)
  const callbackUrl = search?.get('callbackUrl') || '/dashboard'

  useEffect(() => {
    if (status === 'authenticated') {
      router.push('/dashboard')
    }
  }, [status, router])

  const handleLogin = async () => {
    try {
      setLoading(true)
      await signIn('keycloak', { callbackUrl })
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen grid place-items-center">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  if (status === 'authenticated') {
    return (
      <div className="min-h-screen grid place-items-center">
        <EnhancedBadge variant="info" size="lg" icon={<ShieldCheck className="h-3 w-3" />}>
          Redirecting to dashboard...
        </EnhancedBadge>
      </div>
    )
  }

  return (
    <div className="min-h-screen grid lg:grid-cols-2">
      {/* Left brand/hero panel */}
      <div className="relative hidden lg:flex items-center justify-center bg-gradient-to-br from-purple-600 via-indigo-600 to-purple-800 text-white">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(255,255,255,0.15),transparent_40%),radial-gradient(ellipse_at_bottom_right,rgba(255,255,255,0.1),transparent_40%)]" />
        <div className="relative z-10 px-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="h-10 w-10 rounded-xl bg-white/10 backdrop-blur-md flex items-center justify-center shadow-lg">
              <ShieldCheck className="h-6 w-6" />
            </div>
            <h1 className="text-3xl font-bold tracking-tight">OneCRM</h1>
          </div>
          <p className="text-white/85 text-lg">
            Unified, modern CRM platform. Secure SSO via Keycloak.
          </p>
          <div className="mt-8 grid grid-cols-2 gap-3 text-sm">
            <div className="rounded-lg bg-white/10 p-3 backdrop-blur-md shadow-md">
              <p className="font-medium">Enterprise SSO</p>
              <p className="text-white/80">Seamless, secure access</p>
            </div>
            <div className="rounded-lg bg-white/10 p-3 backdrop-blur-md shadow-md">
              <p className="font-medium">Futuristic UI</p>
              <p className="text-white/80">Minimal and responsive</p>
            </div>
            <div className="rounded-lg bg-white/10 p-3 backdrop-blur-md shadow-md">
              <p className="font-medium">RBAC Ready</p>
              <p className="text-white/80">Roles from Keycloak</p>
            </div>
            <div className="rounded-lg bg-white/10 p-3 backdrop-blur-md shadow-md">
              <p className="font-medium">Multi-tenant</p>
              <p className="text-white/80">Org-context support</p>
            </div>
          </div>
        </div>
      </div>

      {/* Right auth card */}
      <div className="flex items-center justify-center p-6 sm:p-8">
        <EnhancedCard
          variant="elevated"
          className="w-full max-w-md"
          title="Welcome back"
          description="Sign in to continue to OneCRM"
          footer={
            <div className="text-center text-xs text-muted-foreground">
              Secure authentication powered by Keycloak
            </div>
          }
        >
          {/* Possible error placeholder from query ?error= */}
          {search?.get('error') && (
            <div className="mb-4 flex items-center gap-2 rounded-md border border-destructive/30 bg-destructive/10 px-3 py-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{search.get('error')}</span>
            </div>
          )}

          <div className="space-y-4">
            <EnhancedButton
              fullWidth
              size="lg"
              loading={loading}
              loadingText="Redirecting..."
              leftIcon={<LogIn className="h-4 w-4" />}
              onClick={handleLogin}
            >
              Sign in with Keycloak
            </EnhancedButton>

            {/* Optional secondary actions */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <a href="/auth/error" className="hover:underline">Having trouble?</a>
              <span>Organization SSO only</span>
            </div>
          </div>
        </EnhancedCard>
      </div>
    </div>
  )
}

