'use client'

import React, { useEffect, useState } from 'react'
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { Settings as SettingsIcon, RefreshCcw, Save } from 'lucide-react'

type OrgSettings = {
  orgName: string
  timezone?: string
  locale?: string
  theme?: 'light' | 'dark' | 'system'
}

export default function SettingsPage() {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [settings, setSettings] = useState<OrgSettings>({
    orgName: 'Acme Corp',
    timezone: 'UTC',
    locale: 'en-US',
    theme: 'system',
  })

  useEffect(() => {
    setLoading(true)
    const t = setTimeout(() => {
      // mock fetch
      setSettings(prev => ({ ...prev }))
      setLoading(false)
    }, 400)
    return () => clearTimeout(t)
  }, [])

  const onSave = () => {
    setSaving(true)
    setTimeout(() => setSaving(false), 600)
  }

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        <PageHeader
          title="Settings"
          description="Organization preferences and application configuration"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedButton size="sm" variant="outline" leftIcon={<RefreshCcw className="h-4 w-4" />}>
                Refresh
              </EnhancedButton>
              <EnhancedButton size="sm" onClick={onSave} disabled={saving} leftIcon={<Save className="h-4 w-4" />}>
                {saving ? 'Saving...' : 'Save Changes'}
              </EnhancedButton>
            </div>
          }
        />

        <EnhancedCard
          variant="elevated"
          title="Overview"
          description="Glassy placeholder that will host forms, toggles, and integration settings."
          className="backdrop-blur-md bg-white/60 dark:bg-zinc-900/40"
        >
          <div className="flex flex-col items-center justify-center text-center py-10">
            <div className="p-3 rounded-xl bg-primary/10 text-primary mb-3">
              <SettingsIcon className="h-6 w-6" />
            </div>
            <p className="text-lg font-semibold">Settings coming soon</p>
            <p className="text-sm text-muted-foreground max-w-md">
              We will add organization profile, themes, locale/timezone, integrations, and audit logs here.
            </p>
          </div>
        </EnhancedCard>

        <EnhancedCard variant="outlined" title="Current (Mock) Settings" description={loading ? 'Loading...' : 'Values from mock state'}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="rounded-lg border border-border/50 p-3 bg-background/50">
              <div className="text-xs text-muted-foreground">Organization</div>
              <div className="font-medium">{settings.orgName}</div>
            </div>
            <div className="rounded-lg border border-border/50 p-3 bg-background/50">
              <div className="text-xs text-muted-foreground">Timezone</div>
              <div className="font-medium">{settings.timezone ?? '-'}</div>
            </div>
            <div className="rounded-lg border border-border/50 p-3 bg-background/50">
              <div className="text-xs text-muted-foreground">Locale</div>
              <div className="font-medium">{settings.locale ?? '-'}</div>
            </div>
            <div className="rounded-lg border border-border/50 p-3 bg-background/50">
              <div className="text-xs text-muted-foreground">Theme</div>
              <div className="font-medium">{settings.theme ?? '-'}</div>
            </div>
          </div>
        </EnhancedCard>
      </Container>
    </AppShell>
  )
}