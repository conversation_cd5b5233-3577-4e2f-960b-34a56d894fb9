// Mock services configuration
// This allows enabling/disabling mock services for development

export interface MockConfig {
  enabled: boolean;
  auth: {
    enabled: boolean;
    autoLogin: boolean;
    mockUser: {
      id: string;
      email: string;
      name: string;
      roles: string[];
      organizationId: string;
    };
  };
  api: {
    enabled: boolean;
    delay: number; // Simulate network delay
    errorRate: number; // Percentage of requests that should fail (0-100)
  };
  keycloak: {
    enabled: boolean;
    mockTokens: boolean;
  };
  notifications: {
    enabled: boolean;
    showMockData: boolean;
  };
}

// Default mock configuration - DISABLED for production Keycloak
export const defaultMockConfig: MockConfig = {
  enabled: false, // Completely disable mock mode
  auth: {
    enabled: false, // Always use real Keycloak authentication
    autoLogin: false,
    mockUser: {
      id: 'mock-user-123',
      email: '<EMAIL>',
      name: 'Admin User',
      roles: ['admin', 'user'],
      organizationId: 'mock-org-456',
    },
  },
  api: {
    enabled: false, // Use real API
    delay: 500,
    errorRate: 0,
  },
  keycloak: {
    enabled: true, // Always use real Keycloak
    mockTokens: false, // Never use mock tokens
  },
  notifications: {
    enabled: true,
    showMockData: false,
  },
};

// Environment-based configuration
export const getMockConfig = (): MockConfig => {
  const config = { ...defaultMockConfig };

  // Override with environment variables
  if (process.env.NEXT_PUBLIC_MOCK_AUTH === 'false') {
    config.auth.enabled = false;
  }
  
  if (process.env.NEXT_PUBLIC_MOCK_API === 'true') {
    config.api.enabled = true;
  }
  
  if (process.env.NEXT_PUBLIC_KEYCLOAK_ENABLED === 'true') {
    config.keycloak.enabled = true;
  }

  if (process.env.NEXT_PUBLIC_MOCK_DELAY) {
    config.api.delay = parseInt(process.env.NEXT_PUBLIC_MOCK_DELAY, 10);
  }

  return config;
};

// Global mock configuration
export const mockConfig = getMockConfig();

// Utility functions
export const isMockEnabled = (service: keyof MockConfig): boolean => {
  return mockConfig.enabled && (mockConfig[service] as any)?.enabled;
};

export const shouldUseMockAuth = (): boolean => {
  return isMockEnabled('auth');
};

export const shouldUseMockAPI = (): boolean => {
  return isMockEnabled('api');
};

export const shouldUseKeycloak = (): boolean => {
  return isMockEnabled('keycloak');
};

// Development helpers
export const logMockConfig = (): void => {
  if (process.env.NODE_ENV === 'development') {
    console.group('🎭 Mock Services Configuration');
    console.log('Overall Mock Enabled:', mockConfig.enabled);
    console.log('Mock Auth:', mockConfig.auth.enabled);
    console.log('Mock API:', mockConfig.api.enabled);
    console.log('Keycloak:', mockConfig.keycloak.enabled);
    console.log('Mock User:', mockConfig.auth.mockUser);
    console.groupEnd();
  }
};
