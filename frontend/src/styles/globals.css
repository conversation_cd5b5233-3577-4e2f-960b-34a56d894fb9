@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS variables and theme tokens */
:root {
  --radius-md: 14px;
  --radius-lg: 18px;
  --radius-xl: 24px;

  --bg: 255 255 255;
  --bg-muted: 248 248 255;
  --card: 255 255 255;
  --border: 228 228 247;

  --primary: 124 104 238; /* #7C68EE */
  --primary-foreground: 255 255 255;

  --foreground: 17 24 39;
  --muted-foreground: 100 116 139;

  --shadow-soft: 0 6px 24px rgba(124,104,238,0.12);
  --shadow-card: 0 10px 40px rgba(17,24,39,0.06);
}

.dark {
  --bg: 16 16 20;
  --bg-muted: 26 26 35;
  --card: 20 20 28;
  --border: 48 48 60;

  --primary: 149 128 255;
  --primary-foreground: 255 255 255;

  --foreground: 226 232 240;
  --muted-foreground: 148 163 184;

  --shadow-soft: 0 10px 32px rgba(149,128,255,0.16);
  --shadow-card: 0 14px 50px rgba(0,0,0,0.35);
}

html, body, #__next {
  height: 100%;
}

body {
  background-color: rgb(var(--bg));
  color: rgb(var(--foreground));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Light lavender ambient background option */
.bg-ambient {
  background-image: radial-gradient(1200px 600px at 100% -10%, rgba(124,104,238,0.15), rgba(124,104,238,0) 60%);
}

/* Card primitives to emulate sleek, minimal UI */
.card {
  @apply rounded-2xl bg-white shadow-[var(--shadow-card)] border;
  border-color: rgb(var(--border));
  background: linear-gradient(180deg, rgba(124,104,238,0.06), rgba(124,104,238,0)) , rgb(var(--card));
}

.dark .card {
  @apply bg-zinc-900/50;
  background: linear-gradient(180deg, rgba(149,128,255,0.06), rgba(149,128,255,0)) , rgb(var(--card));
}

/* Buttons */
.btn {
  @apply inline-flex items-center justify-center gap-2 rounded-xl px-4 py-2 text-sm font-medium transition-colors;
  box-shadow: var(--shadow-soft);
}

.btn-primary {
  @apply text-white;
  background-color: rgb(var(--primary));
}

.btn-primary:hover { filter: brightness(0.96); }
.btn-outline {
  @apply border bg-white text-gray-700;
  border-color: rgb(var(--border));
}
.dark .btn-outline { @apply bg-transparent text-gray-200; }

/* Utilities */
.glassy {
  @apply backdrop-blur-md;
  background-color: rgba(255,255,255,0.6);
}
.dark .glassy { background-color: rgba(24,24,32,0.45); }

.text-muted { color: rgb(var(--muted-foreground)); }