{"scripts": {"dev": "next dev --turbopack -p 3000", "dev:nodemon": "nodemon", "dev:hot": "nodemon", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "type": "module", "name": "@onecrm/frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-keycloak/web": "^3.4.0", "@rjsf/core": "^5.24.12", "@rjsf/mui": "^5.24.12", "@rjsf/utils": "^5.24.12", "@rjsf/validator-ajv8": "^5.24.12", "@tanstack/react-table": "^8.21.3", "axios": "^1.5.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "gsap": "^3.12.0", "keycloak-js": "^22.0.0", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "recharts": "^2.15.4", "sonner": "^2.0.7", "swr": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.0.0", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "eslint": "^8.0.0", "eslint-config-next": "15.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.0.0"}, "overrides": {"@types/react": "19.1.9", "@types/react-dom": "19.1.7"}}