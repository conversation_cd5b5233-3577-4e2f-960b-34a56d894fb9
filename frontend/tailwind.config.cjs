/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{ts,tsx}',
    './src/components/**/*.{ts,tsx}',
    './src/app/**/*.{ts,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)', 'Inter', 'ui-sans-serif', 'system-ui'],
      },
      colors: {
        primary: {
          DEFAULT: '#7C68EE',
          foreground: '#ffffff',
          50: '#f2f0ff',
          100: '#e6e1ff',
          200: '#ccc4ff',
          300: '#b3a6ff',
          400: '#9989ff',
          500: '#7C68EE',
          600: '#6f5dd6',
          700: '#5b4cb3',
          800: '#483b90',
          900: '#352a6d',
        },
      },
      borderRadius: {
        lg: '14px',
        xl: '18px',
        '2xl': '24px',
      },
      boxShadow: {
        soft: '0 6px 24px rgba(124,104,238,0.12)',
        card: '0 10px 40px rgba(17, 24, 39, 0.06)',
      },
      backgroundImage: {
        'lavender-radial': 'radial-gradient(1200px 600px at 100% -10%, rgba(124,104,238,0.15), rgba(124,104,238,0) 60%)',
        'lavender-linear': 'linear-gradient(180deg, rgba(124,104,238,0.08), rgba(124,104,238,0))',
      },
    },
  },
  plugins: [],
}