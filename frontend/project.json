{"name": "@onecrm/frontend", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "frontend/src", "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "next dev -p 3000", "cwd": "frontend"}}, "build": {"executor": "nx:run-commands", "options": {"command": "next build", "cwd": "frontend"}, "outputs": ["{projectRoot}/.next"]}, "start": {"executor": "nx:run-commands", "options": {"command": "next start", "cwd": "frontend"}, "dependsOn": ["build"]}, "lint": {"executor": "nx:run-commands", "options": {"command": "next lint", "cwd": "frontend"}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "frontend"}}, "test": {"executor": "nx:run-commands", "options": {"command": "jest", "cwd": "frontend"}}, "test:watch": {"executor": "nx:run-commands", "options": {"command": "jest --watch", "cwd": "frontend"}}, "test:coverage": {"executor": "nx:run-commands", "options": {"command": "jest --coverage", "cwd": "frontend"}}}, "tags": ["scope:frontend", "type:app"]}