# OneCRM Frontend Environment Configuration
# Copy this file to .env.local and update the values for your environment

# Kong Gateway Configuration (Primary API Gateway)
NEXT_PUBLIC_KONG_GATEWAY_URL=http://localhost:8000

# API Configuration (Fallback - Direct Backend Access)
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002

# Mock Services Configuration
# Set to 'false' to disable mock services and use real services
NEXT_PUBLIC_MOCK_AUTH=true
NEXT_PUBLIC_MOCK_API=false
NEXT_PUBLIC_MOCK_DELAY=500

# Keycloak Configuration (when not using mock auth)
NEXT_PUBLIC_KEYCLOAK_ENABLED=false
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend

# Development Configuration
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_DEBUG=true

# Feature Flags
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_NOTIFICATIONS=true
NEXT_PUBLIC_FEATURE_REAL_TIME=true

# Application Version
NEXT_PUBLIC_APP_VERSION=1.0.0

# Production Configuration (for production builds)
# NEXT_PUBLIC_KONG_GATEWAY_URL=https://api.onecrm.com
# NEXT_PUBLIC_MOCK_AUTH=false
# NEXT_PUBLIC_MOCK_API=false
# NEXT_PUBLIC_KEYCLOAK_ENABLED=true
# NEXT_PUBLIC_ENVIRONMENT=production
# NEXT_PUBLIC_DEBUG=false
