# OneCRM Frontend

Modern, multi-tenant CRM frontend built with Next.js 14, TypeScript, and Material UI.

## 🏗️ Architecture

### Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript with strict mode
- **UI Library**: Material UI (MUI) v5
- **Authentication**: Keycloak with @react-keycloak/web
- **API Gateway**: Kong Gateway integration
- **State Management**: SWR for server state
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest + React Testing Library + Playwright
- **Animations**: GSAP
- **Charts**: Recharts
- **Tables**: TanStack Table

### Monorepo Structure
```
frontend/
├── src/
│   ├── app/           # Next.js App Router pages
│   ├── components/    # React components
│   ├── hooks/         # Custom React hooks
│   ├── lib/           # Utilities and configurations
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Helper functions
├── libs/              # Shared libraries
│   ├── shared/        # Shared components and hooks
│   ├── types/         # Shared type definitions
│   └── utils/         # Shared utility functions
└── tests/             # Test files
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm 9+
- Kong Gateway (for API routing)
- Keycloak (for authentication)

### Installation
```bash
# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local

# Start development server
npm run dev
```

### Environment Configuration
```env
# Kong Gateway (Primary API Gateway)
NEXT_PUBLIC_KONG_GATEWAY_URL=http://localhost:8000

# Keycloak Authentication
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend

# Application
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 🔧 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript checks
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

### Code Quality
- **ESLint**: Configured with Next.js and TypeScript rules
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **Prettier**: Code formatting (configured in root)
- **Husky**: Git hooks for pre-commit checks

## 🏛️ Architecture Patterns

### Authentication Flow
1. **Keycloak Integration**: Single Sign-On with PKCE flow
2. **Token Management**: Automatic refresh and validation
3. **Role-Based Access**: Granular permissions system
4. **Multi-Tenancy**: Organization-based data isolation

### API Integration
1. **Kong Gateway**: All API calls routed through Kong
2. **Error Handling**: Comprehensive error boundaries
3. **Retry Logic**: Exponential backoff for failed requests
4. **Type Safety**: Zod schemas for runtime validation

### State Management
1. **Server State**: SWR for caching and synchronization
2. **Client State**: React hooks and context
3. **Form State**: React Hook Form with validation
4. **Global State**: Minimal use of context providers

## 📱 Features

### Core Functionality
- ✅ **Dashboard**: Analytics and overview
- ✅ **Contacts**: Contact management with search/filter
- ✅ **Companies**: Company profiles and relationships
- ✅ **Deals**: Sales pipeline management
- ✅ **Authentication**: Keycloak SSO integration
- ✅ **Multi-Tenancy**: Organization-based isolation

### UI/UX Features
- ✅ **Responsive Design**: Mobile-first approach
- ✅ **Dark/Light Theme**: User preference support
- ✅ **Accessibility**: WCAG 2.1 AA compliance
- ✅ **Internationalization**: i18n ready structure
- ✅ **Progressive Web App**: PWA capabilities

## 🧪 Testing

### Testing Strategy
- **Unit Tests**: Component and hook testing
- **Integration Tests**: API and user flow testing
- **E2E Tests**: Playwright for critical paths
- **Coverage**: 80% minimum threshold

### Running Tests
```bash
# Unit and integration tests
npm run test

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

## 🚀 Deployment

### Build Process
```bash
# Production build
npm run build

# Start production server
npm run start
```

### Environment Variables
Ensure all `NEXT_PUBLIC_*` variables are set for production deployment.

### Docker Support
```bash
# Build Docker image
docker build -f Dockerfile.prod -t onecrm-frontend .

# Run container
docker run -p 3000:3000 onecrm-frontend
```

## 📚 Documentation

### Component Documentation
- Components are documented with JSDoc comments
- Storybook integration (planned)
- Type definitions serve as API documentation

### API Documentation
- OpenAPI specs in `/docs/api/`
- Kong Gateway configuration in `/kong/config/`
- Authentication flow diagrams in `/docs/auth-flow.md`

## 🔒 Security

### Security Measures
- **CSP Headers**: Content Security Policy implementation
- **HTTPS Only**: Secure communication enforcement
- **Token Security**: Secure token storage and transmission
- **Input Validation**: Comprehensive input sanitization
- **RBAC**: Role-based access control

## 🎯 Performance

### Optimization Techniques
- **Code Splitting**: Dynamic imports for route-based splitting
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Webpack bundle analyzer
- **Caching**: SWR for API response caching
- **Lazy Loading**: Component and route lazy loading

### Performance Monitoring
- **Core Web Vitals**: LCP, FID, CLS tracking
- **Bundle Size**: Automated bundle size monitoring
- **API Performance**: Request timing and error tracking

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Implement changes with tests
3. Run quality checks: `npm run lint && npm run typecheck && npm run test`
4. Submit pull request with description
5. Code review and approval required

### Code Standards
- Follow TypeScript strict mode
- Use functional components with hooks
- Implement proper error boundaries
- Write comprehensive tests
- Document complex logic

## 📞 Support

For technical support or questions:
- **Documentation**: `/docs/` directory
- **Issues**: GitHub Issues
- **Team**: OneCRM Development Team

---

Built with ❤️ by the OneCRM Team
