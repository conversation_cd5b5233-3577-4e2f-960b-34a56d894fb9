<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OneCRM - Silent SSO Check</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <script>
        // This page is used by Keycloak for silent SSO checks
        // It should be served from the same domain as the main application
        
        // Get the parent window's Keycloak instance
        const keycloak = parent.window.keycloak;
        
        if (keycloak) {
            // Process the SSO check result
            keycloak.silentCheckSsoRedirectUri = window.location.href;
            
            // Parse the URL fragment for authentication result
            const fragment = window.location.hash.substring(1);
            const params = new URLSearchParams(fragment);
            
            if (params.get('error')) {
                // SSO check failed
                console.log('Silent SSO check failed:', params.get('error'));
            } else if (params.get('code')) {
                // SSO check succeeded
                console.log('Silent SSO check succeeded');
            }
            
            // Notify the parent window that the check is complete
            if (parent && parent.postMessage) {
                parent.postMessage({
                    type: 'keycloak-silent-check-sso',
                    success: !params.get('error'),
                    error: params.get('error'),
                    code: params.get('code')
                }, window.location.origin);
            }
        }
    </script>
</body>
</html>
