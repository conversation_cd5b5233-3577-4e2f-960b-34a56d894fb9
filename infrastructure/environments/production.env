# OneCRM Production Environment Configuration
# This file contains non-sensitive configuration for production

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
DEBUG=false

# Server Configuration
PORT=8000
FRONTEND_PORT=3000
HOST=0.0.0.0

# Domain Configuration
DOMAIN=onecrm.example.com
API_DOMAIN=api.onecrm.example.com
AUTH_DOMAIN=auth.onecrm.example.com
FRONTEND_URL=https://onecrm.example.com
API_URL=https://api.onecrm.example.com
KEYCLOAK_URL=https://auth.onecrm.example.com

# CORS Configuration
CORS_ORIGIN=https://onecrm.example.com
CORS_CREDENTIALS=true

# Database Configuration
DB_HOST=postgres-service
DB_PORT=5432
DB_NAME=onecrm
DB_SSL=true
DB_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=10000

# Redis Configuration
REDIS_HOST=redis-service
REDIS_PORT=6379
REDIS_DB=0
REDIS_TTL=3600

# JWT Configuration
JWT_EXPIRATION=1d
JWT_REFRESH_EXPIRATION=7d
JWT_ISSUER=onecrm
JWT_AUDIENCE=onecrm-users

# Keycloak Configuration
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-frontend
KEYCLOAK_ADMIN_CLIENT_ID=onecrm-admin

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,csv,txt
UPLOAD_PATH=/tmp/uploads

# Email Configuration
EMAIL_ENABLED=true
EMAIL_TEMPLATE_PATH=./templates/email

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_FILE_UPLOAD=true
FEATURE_EXPORT=true
FEATURE_IMPORT=true
FEATURE_WEBHOOKS=true
FEATURE_API_RATE_LIMITING=true

# Security Configuration
HELMET_ENABLED=true
CSRF_ENABLED=true
SESSION_SECURE=true
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROMETHEUS_ENABLED=true

# Logging Configuration
LOG_FORMAT=json
LOG_TIMESTAMP=true
LOG_COLORIZE=false
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Performance Configuration
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# SSL/TLS Configuration
SSL_ENABLED=true
FORCE_HTTPS=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000

# API Configuration
API_VERSION=v1
API_PREFIX=/api
API_DOCUMENTATION_ENABLED=false
API_THROTTLE_TTL=60
API_THROTTLE_LIMIT=100

# Webhook Configuration
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=1000

# Search Configuration
SEARCH_ENABLED=true
SEARCH_INDEX_BATCH_SIZE=100

# Notification Configuration
NOTIFICATION_QUEUE_ENABLED=true
NOTIFICATION_BATCH_SIZE=50

# Audit Configuration
AUDIT_ENABLED=true
AUDIT_RETENTION_DAYS=365

# Timezone Configuration
TZ=UTC
DEFAULT_TIMEZONE=UTC

# Locale Configuration
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR,de-DE

# Third-party Integrations
GOOGLE_MAPS_ENABLED=true
STRIPE_ENABLED=true
SENDGRID_ENABLED=true

# Development Tools (Disabled in Production)
SWAGGER_ENABLED=false
GRAPHQL_PLAYGROUND_ENABLED=false
DEBUG_ROUTES_ENABLED=false
