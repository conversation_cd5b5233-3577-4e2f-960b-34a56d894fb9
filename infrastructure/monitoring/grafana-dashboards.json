{"dashboard": {"id": null, "title": "OneCRM Production Dashboard", "tags": ["onecrm", "production"], "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"onecrm-backend\"}", "legendFormat": "Backend Status"}, {"expr": "up{job=\"onecrm-frontend\"}", "legendFormat": "Frontend Status"}, {"expr": "up{job=\"postgres\"}", "legendFormat": "Database Status"}, {"expr": "up{job=\"redis\"}", "legendFormat": "<PERSON>ache <PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"onecrm-backend\"}[5m])", "legendFormat": "{{method}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"onecrm-backend\"}[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"onecrm-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"onecrm-backend\"}[5m]))", "legendFormat": "99th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"onecrm-backend\",status=~\"4..\"}[5m])", "legendFormat": "4xx errors"}, {"expr": "rate(http_requests_total{job=\"onecrm-backend\",status=~\"5..\"}[5m])", "legendFormat": "5xx errors"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Active Users", "type": "stat", "targets": [{"expr": "onecrm_active_users_total", "legendFormat": "Active Users"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 16}}, {"id": 6, "title": "Database Connections", "type": "stat", "targets": [{"expr": "onecrm_database_connections_active", "legendFormat": "Active Connections"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 16}}, {"id": 7, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes{job=\"onecrm-backend\"}", "legendFormat": "Backend Memory"}, {"expr": "process_resident_memory_bytes{job=\"onecrm-frontend\"}", "legendFormat": "Frontend Memory"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"onecrm-backend\"}[5m])", "legendFormat": "Backend CPU"}, {"expr": "rate(process_cpu_seconds_total{job=\"onecrm-frontend\"}[5m])", "legendFormat": "Frontend CPU"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 9, "title": "Business Metrics", "type": "graph", "targets": [{"expr": "onecrm_contacts_total", "legendFormat": "Total Contacts"}, {"expr": "onecrm_deals_total", "legendFormat": "Total Deals"}, {"expr": "onecrm_companies_total", "legendFormat": "Total Companies"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}