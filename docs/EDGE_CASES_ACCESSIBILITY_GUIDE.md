# Edge Cases & Accessibility Guide for OneCRM
## Comprehensive Coverage for Production-Ready UX

### Overview

This document covers critical edge cases, accessibility requirements, and error handling patterns to ensure OneCRM provides a robust, inclusive user experience across all scenarios and user capabilities.

## Data Edge Cases

### 1. Empty States
```typescript
// Empty contact list with contextual actions
<div className="flex flex-col items-center justify-center py-12">
  <div className="mx-auto max-w-sm text-center">
    <UserPlus className="mx-auto h-12 w-12 text-muted-foreground" />
    <h3 className="mt-4 text-lg font-semibold">No contacts yet</h3>
    <p className="mt-2 text-sm text-muted-foreground">
      Get started by adding your first contact to begin building relationships.
    </p>
    <div className="mt-6 flex flex-col gap-2 sm:flex-row sm:justify-center">
      <Button>
        <Plus className="mr-2 h-4 w-4" />
        Add Contact
      </Button>
      <Button variant="outline">
        <Upload className="mr-2 h-4 w-4" />
        Import Contacts
      </Button>
    </div>
  </div>
</div>
```

### 2. Large Dataset Handling
```typescript
// Virtual scrolling for 10,000+ records
<VirtualizedList
  height={600}
  itemCount={contacts.length}
  itemSize={72}
  overscan={5}
  onItemsRendered={({ visibleStartIndex, visibleStopIndex }) => {
    // Load more data when approaching end
    if (visibleStopIndex > contacts.length - 10) {
      loadMoreContacts()
    }
  }}
>
  {({ index, style }) => (
    <div style={style}>
      <ContactListItem contact={contacts[index]} />
    </div>
  )}
</VirtualizedList>
```

### 3. Network Failure Handling
```typescript
// Offline state with retry mechanism
<div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4">
  <div className="flex items-center gap-3">
    <WifiOff className="h-5 w-5 text-destructive" />
    <div className="flex-1">
      <h4 className="font-medium text-destructive">Connection Lost</h4>
      <p className="text-sm text-destructive/80">
        Unable to sync data. Changes will be saved locally.
      </p>
    </div>
    <Button
      variant="outline"
      size="sm"
      onClick={retryConnection}
      disabled={isRetrying}
    >
      {isRetrying ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4" />
      )}
      Retry
    </Button>
  </div>
</div>
```

### 4. Data Validation & Error States
```typescript
// Form with comprehensive validation
<FormField
  control={form.control}
  name="email"
  render={({ field, fieldState }) => (
    <FormItem>
      <FormLabel>Email Address</FormLabel>
      <FormControl>
        <div className="relative">
          <Input
            type="email"
            placeholder="<EMAIL>"
            className={cn(
              fieldState.error && "border-destructive focus-visible:ring-destructive"
            )}
            {...field}
          />
          {fieldState.error && (
            <AlertCircle className="absolute right-3 top-3 h-4 w-4 text-destructive" />
          )}
        </div>
      </FormControl>
      <FormMessage />
      {!fieldState.error && field.value && (
        <FormDescription className="flex items-center gap-1 text-green-600">
          <CheckCircle className="h-3 w-3" />
          Valid email format
        </FormDescription>
      )}
    </FormItem>
  )}
/>
```

## Accessibility Implementation

### 1. Keyboard Navigation
```typescript
// Enhanced keyboard navigation for data tables
<Table
  role="grid"
  aria-label="Contacts table"
  onKeyDown={(e) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        focusNextRow()
        break
      case 'ArrowUp':
        e.preventDefault()
        focusPreviousRow()
        break
      case 'Enter':
      case ' ':
        e.preventDefault()
        activateCurrentRow()
        break
      case 'Home':
        e.preventDefault()
        focusFirstRow()
        break
      case 'End':
        e.preventDefault()
        focusLastRow()
        break
    }
  }}
>
  {/* Table content with proper ARIA attributes */}
</Table>
```

### 2. Screen Reader Support
```typescript
// Comprehensive ARIA labels and descriptions
<Card
  role="article"
  aria-labelledby={`contact-${contact.id}-name`}
  aria-describedby={`contact-${contact.id}-details`}
>
  <CardHeader>
    <CardTitle id={`contact-${contact.id}-name`}>
      {contact.firstName} {contact.lastName}
    </CardTitle>
    <CardDescription id={`contact-${contact.id}-details`}>
      {contact.title} at {contact.company}
      {contact.lastContactDate && (
        <span className="sr-only">
          , last contacted {formatDate(contact.lastContactDate)}
        </span>
      )}
    </CardDescription>
  </CardHeader>
  
  <CardContent>
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Mail className="h-4 w-4" aria-hidden="true" />
        <a
          href={`mailto:${contact.email}`}
          aria-label={`Send email to ${contact.firstName} ${contact.lastName}`}
        >
          {contact.email}
        </a>
      </div>
      
      <div className="flex items-center gap-2">
        <Phone className="h-4 w-4" aria-hidden="true" />
        <a
          href={`tel:${contact.phone}`}
          aria-label={`Call ${contact.firstName} ${contact.lastName}`}
        >
          {contact.phone}
        </a>
      </div>
    </div>
  </CardContent>
  
  <CardFooter>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          aria-label={`Actions for ${contact.firstName} ${contact.lastName}`}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem>
          <Edit className="mr-2 h-4 w-4" />
          Edit Contact
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Trash className="mr-2 h-4 w-4" />
          Delete Contact
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </CardFooter>
</Card>
```

### 3. Focus Management
```typescript
// Modal with proper focus management
export function ContactModal({ open, onOpenChange, contact }: ContactModalProps) {
  const [isOpen, setIsOpen] = useState(open)
  const firstInputRef = useRef<HTMLInputElement>(null)
  const lastFocusedElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      lastFocusedElement.current = document.activeElement as HTMLElement
      
      // Focus the first input when modal opens
      setTimeout(() => {
        firstInputRef.current?.focus()
      }, 100)
    } else {
      // Return focus to the previously focused element
      lastFocusedElement.current?.focus()
    }
  }, [isOpen])

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onOpenChange(false)
    }
    
    // Trap focus within modal
    if (e.key === 'Tab') {
      const focusableElements = modal.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      
      if (focusableElements) {
        const firstElement = focusableElements[0] as HTMLElement
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
        
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent
        onKeyDown={handleKeyDown}
        aria-labelledby="contact-modal-title"
        aria-describedby="contact-modal-description"
      >
        <DialogHeader>
          <DialogTitle id="contact-modal-title">
            {contact ? 'Edit Contact' : 'Add New Contact'}
          </DialogTitle>
          <DialogDescription id="contact-modal-description">
            {contact 
              ? 'Update contact information and save changes.'
              : 'Enter contact details to add them to your CRM.'
            }
          </DialogDescription>
        </DialogHeader>
        
        <form className="space-y-4">
          <Input
            ref={firstInputRef}
            placeholder="First Name"
            aria-label="First Name"
            required
          />
          {/* Additional form fields */}
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

### 4. Color Contrast & Visual Accessibility
```typescript
// High contrast mode support
<div className={cn(
  "rounded-lg border p-4 transition-colors",
  // Standard colors
  "bg-card text-card-foreground border-border",
  // High contrast mode
  "contrast-more:border-2 contrast-more:border-black",
  "dark:contrast-more:border-white",
  // Focus indicators
  "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
)}>
  <Badge
    variant={getStatusVariant(status)}
    className={cn(
      // Ensure sufficient contrast
      "contrast-more:border contrast-more:border-current",
      // Motion preferences
      "motion-safe:transition-colors motion-reduce:transition-none"
    )}
  >
    {status}
  </Badge>
</div>
```

## Mobile Edge Cases

### 1. Touch Target Sizing
```typescript
// Minimum 44px touch targets
<Button
  size="sm"
  className={cn(
    "min-h-[44px] min-w-[44px]", // Ensure minimum touch target
    "touch-manipulation", // Optimize for touch
    "select-none" // Prevent text selection on touch
  )}
>
  <Plus className="h-4 w-4" />
</Button>
```

### 2. Orientation Changes
```typescript
// Responsive layout for orientation changes
<div className={cn(
  "grid gap-4",
  // Portrait: single column
  "grid-cols-1",
  // Landscape: two columns on mobile
  "landscape:grid-cols-2 landscape:md:grid-cols-3",
  // Desktop: three columns
  "lg:grid-cols-3"
)}>
  {contacts.map((contact) => (
    <ContactCard key={contact.id} contact={contact} />
  ))}
</div>
```

### 3. Gesture Conflicts
```typescript
// Prevent gesture conflicts with swipe actions
<div
  className="touch-pan-y" // Allow vertical scrolling
  onTouchStart={handleTouchStart}
  onTouchMove={handleTouchMove}
  onTouchEnd={handleTouchEnd}
  style={{
    // Prevent pull-to-refresh on iOS
    overscrollBehavior: 'contain',
    // Prevent zoom on double-tap
    touchAction: 'manipulation'
  }}
>
  <SwipeableListItem
    leftActions={[/* ... */]}
    rightActions={[/* ... */]}
    threshold={0.3} // 30% swipe to trigger
  >
    {/* Content */}
  </SwipeableListItem>
</div>
```

## Performance Edge Cases

### 1. Memory Management
```typescript
// Cleanup and memory management
export function ContactsList() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const abortControllerRef = useRef<AbortController>()
  
  useEffect(() => {
    // Create new abort controller for this effect
    abortControllerRef.current = new AbortController()
    
    const loadContacts = async () => {
      try {
        const response = await fetch('/api/contacts', {
          signal: abortControllerRef.current?.signal
        })
        
        if (!response.ok) throw new Error('Failed to load contacts')
        
        const data = await response.json()
        setContacts(data)
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Failed to load contacts:', error)
        }
      }
    }
    
    loadContacts()
    
    // Cleanup function
    return () => {
      abortControllerRef.current?.abort()
    }
  }, [])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abortControllerRef.current?.abort()
    }
  }, [])
  
  return (
    <div>
      {/* Component content */}
    </div>
  )
}
```

### 2. Image Loading & Optimization
```typescript
// Optimized image loading with fallbacks
<Avatar className="h-10 w-10">
  <AvatarImage
    src={contact.avatar}
    alt={`${contact.firstName} ${contact.lastName}`}
    loading="lazy"
    onError={(e) => {
      // Fallback to initials if image fails
      e.currentTarget.style.display = 'none'
    }}
  />
  <AvatarFallback
    className={cn(
      "bg-muted text-muted-foreground",
      // Ensure readable contrast
      "contrast-more:bg-black contrast-more:text-white",
      "dark:contrast-more:bg-white dark:contrast-more:text-black"
    )}
  >
    {getInitials(contact.firstName, contact.lastName)}
  </AvatarFallback>
</Avatar>
```

## Security & Privacy Edge Cases

### 1. Data Sanitization
```typescript
// Sanitize user input to prevent XSS
import DOMPurify from 'dompurify'

function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: []  // No attributes allowed
  })
}

// Use in form validation
const schema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .transform(sanitizeInput),
  email: z.string()
    .email('Invalid email format')
    .transform(sanitizeInput),
})
```

### 2. Sensitive Data Handling
```typescript
// Mask sensitive information
function MaskedField({ value, type }: { value: string; type: 'phone' | 'email' }) {
  const [isRevealed, setIsRevealed] = useState(false)
  
  const getMaskedValue = () => {
    if (isRevealed) return value
    
    switch (type) {
      case 'phone':
        return value.replace(/(\d{3})\d{3}(\d{4})/, '$1***$2')
      case 'email':
        const [local, domain] = value.split('@')
        return `${local.slice(0, 2)}***@${domain}`
      default:
        return value
    }
  }
  
  return (
    <div className="flex items-center gap-2">
      <span className="font-mono">{getMaskedValue()}</span>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsRevealed(!isRevealed)}
        aria-label={isRevealed ? 'Hide value' : 'Reveal value'}
      >
        {isRevealed ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
      </Button>
    </div>
  )
}
```

This comprehensive guide ensures OneCRM handles all edge cases gracefully while maintaining the highest standards of accessibility and user experience across all devices and user capabilities.
