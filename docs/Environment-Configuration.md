# OneCRM Environment Configuration Guide

## 📁 Environment File Structure

```
oneCRM/
├── .env                           # Root: Shared configuration
├── .env.development              # Root: Development overrides
├── frontend/
│   ├── .env.local               # Frontend: Development config
│   └── .env.production          # Frontend: Production config
└── backend/
    └── crm-api/
        ├── .env                 # Backend: Development config
        └── .env.production      # Backend: Production config
```

## 🔧 Environment Dependencies

### Root Level (Monorepo)
**File**: `/.env`
**Used By**: Docker, deployment scripts, shared configuration
**Purpose**: Shared configuration across all services

```bash
# Shared Keycloak Configuration
KEYCLOAK_URL=https://stgsso.cubeone.in
KEYCLOAK_REALM=Service
KEYCLOAK_FRONTEND_CLIENT_ID=onecrm-frontend
KEYCLOAK_BACKEND_CLIENT_ID=onecrm-backend
```

### Frontend Environment
**Files**: 
- `/frontend/.env.local` (Development)
- `/frontend/.env.production` (Production)

**Used By**: Next.js application
**Purpose**: Frontend-specific configuration

#### Development (`/frontend/.env.local`)
```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002

# Authentication
NEXT_PUBLIC_AUTH_PROVIDER=mock
NEXT_PUBLIC_MOCK_AUTH=true

# Keycloak (Development)
NEXT_PUBLIC_KEYCLOAK_ENABLED=false
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=development-secret
KEYCLOAK_CLIENT_SECRET=kUrAmvunsN3igQp6kejrZ344Od7LF6FQ
```

#### Production (`/frontend/.env.production`)
```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.onecrm.cubeone.in

# Authentication
NEXT_PUBLIC_AUTH_PROVIDER=nextauth
NEXT_PUBLIC_MOCK_AUTH=false

# Keycloak (Production)
NEXT_PUBLIC_KEYCLOAK_ENABLED=true
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
```

### Backend Environment
**Files**: 
- `/backend/crm-api/.env` (Development)
- `/backend/crm-api/.env.production` (Production)

**Used By**: NestJS API application
**Purpose**: Backend-specific configuration

#### Development (`/backend/crm-api/.env`)
```bash
# Server Configuration
PORT=3002
NODE_ENV=development
API_BASE_URL=http://localhost:3002

# Keycloak
KEYCLOAK_URL=https://stgsso.cubeone.in
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=hhj319oCJP1JkFks9G8L7unkAwzbtfFn

# Database
DATABASE_URL=postgresql://onecrm:onecrm_dev_password@localhost:5432/onecrm
```

#### Production (`/backend/crm-api/.env.production`)
```bash
# Server Configuration
PORT=3002
NODE_ENV=production
API_BASE_URL=https://api.onecrm.cubeone.in

# Keycloak
KEYCLOAK_URL=https://stgsso.cubeone.in
KEYCLOAK_CLIENT_ID=onecrm-backend
# KEYCLOAK_CLIENT_SECRET=set-via-deployment

# Database
# DATABASE_URL=set-via-deployment
```

## 🔐 Security Best Practices

### Development Environment
- ✅ Secrets can be in `.env` files for convenience
- ✅ Mock authentication enabled
- ✅ Detailed logging enabled
- ✅ CORS allows localhost

### Production Environment
- ❌ **Never commit production secrets**
- ✅ Use deployment environment variables
- ✅ Real authentication only
- ✅ Minimal logging
- ✅ Strict CORS policy

## 🚀 Environment Loading Order

### Frontend (Next.js)
1. `.env.production` (production builds)
2. `.env.local` (always loaded, gitignored)
3. `.env` (default)

### Backend (NestJS)
1. `.env.production` (if NODE_ENV=production)
2. `.env` (default)
3. System environment variables (override all)

## 📝 Environment Variable Naming

### Frontend Variables
- `NEXT_PUBLIC_*` - Exposed to browser
- `NEXTAUTH_*` - NextAuth.js configuration
- `KEYCLOAK_*` - Server-side Keycloak secrets

### Backend Variables
- `KEYCLOAK_*` - Keycloak configuration
- `DB_*` - Database configuration
- `JWT_*` - JWT configuration
- `SMTP_*` - Email configuration

## 🔄 Switching Environments

### Development Mode
```bash
# Frontend
cd frontend
npm run dev  # Uses .env.local

# Backend
cd backend/crm-api
npm run dev  # Uses .env
```

### Production Mode
```bash
# Frontend
cd frontend
npm run build  # Uses .env.production
npm start

# Backend
cd backend/crm-api
NODE_ENV=production npm start  # Uses .env.production
```

## 🛠️ Environment Setup Commands

### Initial Setup
```bash
# Copy example files
cp .env.example .env
cp frontend/.env.example frontend/.env.local
cp backend/crm-api/.env.example backend/crm-api/.env

# Set development secrets
echo "NEXTAUTH_SECRET=$(openssl rand -base64 32)" >> frontend/.env.local
echo "JWT_SECRET=$(openssl rand -base64 32)" >> backend/crm-api/.env
```

### Validation
```bash
# Check frontend environment
cd frontend && npm run env:check

# Check backend environment
cd backend/crm-api && npm run env:check
```

## 🔍 Troubleshooting

### Common Issues

1. **Frontend can't connect to backend**
   - Check `NEXT_PUBLIC_API_BASE_URL` in frontend
   - Check `PORT` in backend
   - Verify CORS settings

2. **Keycloak authentication fails**
   - Verify `KEYCLOAK_CLIENT_ID` matches in both frontend/backend
   - Check `KEYCLOAK_CLIENT_SECRET` in backend
   - Ensure `KEYCLOAK_ISSUER` is correct

3. **Environment variables not loading**
   - Check file names (`.env.local` vs `.env`)
   - Verify file location
   - Restart development servers

### Debug Commands
```bash
# Check loaded environment variables
# Frontend
console.log(process.env.NEXT_PUBLIC_API_BASE_URL)

# Backend
console.log(process.env.KEYCLOAK_URL)
```

This configuration ensures proper separation of concerns and security across all environments.
