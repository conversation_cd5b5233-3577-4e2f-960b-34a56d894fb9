# OneCRM User Setup Quick Guide

## Quick Start: Setting Up Users with Different Roles

### Prerequisites
- Keycloak Admin Access: `https://stgsso.cubeone.in/admin`
- OneCRM Backend Running: `http://localhost:3002`
- Admin API Token

### 1. Create Organization

```bash
# Create a new organization
curl -X POST http://localhost:3002/api/organizations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "name": "Tech Solutions Inc",
    "domain": "techsolutions.com",
    "subscriptionTier": "enterprise",
    "maxUsers": 50,
    "settings": {
      "features": ["advanced_reporting", "api_access", "custom_fields"],
      "integrations": ["email", "calendar", "slack"]
    }
  }'
```

### 2. Setup Different User Types

#### A. Sales Manager Setup

**Step 1: Create in Keycloak**
```
Username: <EMAIL>
Email: <EMAIL>
First Name: Sarah
Last Name: Johnson
Enabled: Yes
Email Verified: Yes
```

**Step 2: Create in OneCRM**
```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "Sarah",
    "lastName": "Johnson",
    "role": "sales_manager",
    "orgId": "<organization_id>",
    "department": "sales",
    "isActive": true,
    "permissions": [
      "deal:create", "deal:read", "deal:update", "deal:delete",
      "contact:create", "contact:read", "contact:update", "contact:delete",
      "company:create", "company:read", "company:update", "company:delete",
      "activity:assign", "report:view_advanced", "report:create"
    ]
  }'
```

#### B. Sales Representative Setup

**Step 1: Create in Keycloak**
```
Username: <EMAIL>
Email: <EMAIL>
First Name: Mike
Last Name: Davis
```

**Step 2: Create in OneCRM**
```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "Mike",
    "lastName": "Davis",
    "role": "sales_rep",
    "orgId": "<organization_id>",
    "department": "sales",
    "reportsTo": "<sarah_johnson_user_id>",
    "isActive": true
  }'
```

#### C. Marketing Manager Setup

```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "Lisa",
    "lastName": "Chen",
    "role": "marketing_manager",
    "orgId": "<organization_id>",
    "department": "marketing",
    "isActive": true
  }'
```

#### D. Support Agent Setup

```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "Alex",
    "lastName": "Rodriguez",
    "role": "support_agent",
    "orgId": "<organization_id>",
    "department": "support",
    "isActive": true
  }'
```

#### E. Organization Viewer (External Consultant)

```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "External",
    "lastName": "Consultant",
    "role": "org_viewer",
    "orgId": "<organization_id>",
    "department": "external",
    "isActive": true,
    "accessExpiry": "2024-12-31T23:59:59Z"
  }'
```

### 3. Test User Permissions

#### Test Sales Manager Access
```bash
# Login as sales manager and test deal creation
curl -X POST http://localhost:3002/api/deals \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <sarah_token>" \
  -H "X-Org-Id: <organization_id>" \
  -d '{
    "title": "Enterprise Software Deal",
    "value": 50000,
    "stage": "qualification",
    "contactId": "<contact_id>",
    "companyId": "<company_id>"
  }'
```

#### Test Sales Rep Access
```bash
# Sales rep should be able to create contacts
curl -X POST http://localhost:3002/api/contacts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <mike_token>" \
  -H "X-Org-Id: <organization_id>" \
  -d '{
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "companyId": "<company_id>"
  }'

# But should NOT be able to delete deals
curl -X DELETE http://localhost:3002/api/deals/<deal_id> \
  -H "Authorization: Bearer <mike_token>" \
  -H "X-Org-Id: <organization_id>"
# Expected: 403 Forbidden
```

#### Test Viewer Access
```bash
# Viewer should be able to read contacts
curl -X GET http://localhost:3002/api/contacts \
  -H "Authorization: Bearer <consultant_token>" \
  -H "X-Org-Id: <organization_id>"

# But should NOT be able to create contacts
curl -X POST http://localhost:3002/api/contacts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <consultant_token>" \
  -H "X-Org-Id: <organization_id>" \
  -d '{"firstName": "Test", "lastName": "User"}'
# Expected: 403 Forbidden
```

### 4. Role Management Operations

#### Promote Sales Rep to Sales Manager
```bash
curl -X PATCH http://localhost:3002/api/users/<mike_user_id>/role \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "newRole": "sales_manager",
    "reason": "Performance promotion",
    "effectiveDate": "2024-01-01T00:00:00Z"
  }'
```

#### Grant Temporary Admin Access
```bash
curl -X POST http://localhost:3002/api/users/<user_id>/temporary-role \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "role": "org_admin",
    "duration": "7d",
    "reason": "Project leadership",
    "permissions": ["user:manage_roles", "org:manage_settings"]
  }'
```

#### Revoke User Access
```bash
curl -X PATCH http://localhost:3002/api/users/<user_id> \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "isActive": false,
    "reason": "Employee departure",
    "revokedBy": "<admin_user_id>"
  }'
```

### 5. Multi-Company Setup

#### Create Second Organization
```bash
curl -X POST http://localhost:3002/api/organizations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "name": "Marketing Agency Ltd",
    "domain": "marketingagency.com",
    "subscriptionTier": "professional",
    "maxUsers": 25
  }'
```

#### Create Cross-Organization User (System Admin)
```bash
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <super_admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "System",
    "lastName": "Administrator",
    "role": "system_admin",
    "orgId": null,
    "canAccessOrgs": ["<org1_id>", "<org2_id>"],
    "isActive": true
  }'
```

### 6. Verification Commands

#### Check User Permissions
```bash
curl -X GET http://localhost:3002/api/users/<user_id>/permissions \
  -H "Authorization: Bearer <admin_token>"
```

#### Audit Role Assignments
```bash
curl -X GET http://localhost:3002/api/audit/role-assignments \
  -H "Authorization: Bearer <admin_token>" \
  -H "X-Org-Id: <organization_id>"
```

#### Test Organization Isolation
```bash
# User from Org1 trying to access Org2 data
curl -X GET http://localhost:3002/api/contacts \
  -H "Authorization: Bearer <org1_user_token>" \
  -H "X-Org-Id: <org2_id>"
# Expected: 403 Forbidden
```

### Common Role Combinations

| Role | Typical Use Case | Key Permissions |
|------|------------------|-----------------|
| `super_admin` | Platform Owner | All permissions |
| `system_admin` | IT Administrator | System + Org management |
| `org_admin` | Company Owner | Full org access |
| `sales_manager` | Sales Director | Sales team + pipeline |
| `sales_rep` | Account Manager | Deals + contacts |
| `marketing_manager` | Marketing Director | Campaigns + leads |
| `marketing_user` | Content Creator | Marketing activities |
| `support_manager` | Support Director | Support team management |
| `support_agent` | Help Desk | Customer support |
| `org_viewer` | External Consultant | Read-only access |
| `api_user` | Integration System | Programmatic access |

This setup provides a complete multi-tenant RBAC system with proper isolation and security controls.
