# OneCRM Role-Based Access Control (RBAC) Guide

## Overview

OneCRM implements a comprehensive Role-Based Access Control (RBAC) system that provides fine-grained access control across organizations, users, and resources. The system is designed to support multi-tenant architecture with hierarchical roles and permissions.

## Table of Contents

1. [Role Hierarchy](#role-hierarchy)
2. [Permissions System](#permissions-system)
3. [User Management](#user-management)
4. [Organization Management](#organization-management)
5. [Setting Up Users](#setting-up-users)
6. [Role Assignment Workflows](#role-assignment-workflows)
7. [API Integration](#api-integration)
8. [Security Best Practices](#security-best-practices)

## Role Hierarchy

### System-Level Roles

#### Super Administrator (`super_admin`)
- **Level**: 10 (Highest)
- **Scope**: Entire system
- **Description**: Complete system access, can manage all organizations and users
- **Use Case**: System owners, technical administrators

#### System Administrator (`system_admin`)
- **Level**: 9
- **Scope**: System-wide
- **Description**: System administration without super admin privileges
- **Use Case**: IT administrators, system managers

### Organization-Level Roles

#### Organization Administrator (`org_admin`)
- **Level**: 8
- **Scope**: Single organization
- **Description**: Full control over organization, users, and data
- **Use Case**: Organization owners, C-level executives

#### Organization Manager (`org_manager`)
- **Level**: 7
- **Scope**: Single organization
- **Description**: Management access with user oversight capabilities
- **Use Case**: Department heads, senior managers

#### Organization User (`org_user`)
- **Level**: 6
- **Scope**: Single organization
- **Description**: Standard user with create/update permissions
- **Use Case**: Regular employees, team members

#### Organization Viewer (`org_viewer`)
- **Level**: 5
- **Scope**: Single organization
- **Description**: Read-only access to organization data
- **Use Case**: Contractors, temporary staff, auditors

### Department-Specific Roles

#### Sales Manager (`sales_manager`)
- **Level**: 7
- **Scope**: Sales department
- **Description**: Sales team management and pipeline oversight
- **Use Case**: Sales directors, team leads

#### Sales Representative (`sales_rep`)
- **Level**: 6
- **Scope**: Sales activities
- **Description**: Sales activities and customer management
- **Use Case**: Sales representatives, account managers

#### Marketing Manager (`marketing_manager`)
- **Level**: 7
- **Scope**: Marketing department
- **Description**: Marketing campaigns and lead management
- **Use Case**: Marketing directors, campaign managers

#### Marketing User (`marketing_user`)
- **Level**: 6
- **Scope**: Marketing activities
- **Description**: Marketing content and campaign execution
- **Use Case**: Marketing specialists, content creators

#### Support Manager (`support_manager`)
- **Level**: 7
- **Scope**: Support department
- **Description**: Customer support team management
- **Use Case**: Support directors, team leads

#### Support Agent (`support_agent`)
- **Level**: 6
- **Scope**: Support activities
- **Description**: Customer support and issue resolution
- **Use Case**: Support representatives, help desk agents

### Special Roles

#### API User (`api_user`)
- **Level**: 4
- **Scope**: API access only
- **Description**: Programmatic access to specific resources
- **Use Case**: Integration systems, third-party applications

#### Guest (`guest`)
- **Level**: 1 (Lowest)
- **Scope**: Limited read access
- **Description**: Minimal access for external users
- **Use Case**: External partners, limited access users

## Permissions System

### Organization Permissions
- `org:create` - Create new organizations
- `org:read` - View organization information
- `org:update` - Modify organization settings
- `org:delete` - Delete organizations
- `org:manage_users` - Manage organization users
- `org:manage_settings` - Configure organization settings

### User Permissions
- `user:create` - Create new users
- `user:read` - View user information
- `user:update` - Modify user details
- `user:delete` - Delete users
- `user:manage_roles` - Assign and modify user roles

### Contact Permissions
- `contact:create` - Create new contacts
- `contact:read` - View contact information
- `contact:update` - Modify contact details
- `contact:delete` - Delete contacts
- `contact:export` - Export contact data
- `contact:import` - Import contact data

### Company Permissions
- `company:create` - Create new companies
- `company:read` - View company information
- `company:update` - Modify company details
- `company:delete` - Delete companies
- `company:export` - Export company data
- `company:import` - Import company data

### Deal Permissions
- `deal:create` - Create new deals
- `deal:read` - View deal information
- `deal:update` - Modify deal details
- `deal:delete` - Delete deals
- `deal:manage_pipeline` - Configure sales pipeline
- `deal:view_reports` - Access deal reports

### Activity Permissions
- `activity:create` - Create new activities
- `activity:read` - View activity information
- `activity:update` - Modify activity details
- `activity:delete` - Delete activities
- `activity:assign` - Assign activities to users

### Reporting Permissions
- `report:view_basic` - View basic reports
- `report:view_advanced` - View advanced analytics
- `report:create` - Create custom reports
- `report:export` - Export report data

### System Permissions
- `system:admin` - System administration
- `system:settings` - System configuration
- `system:logs` - Access system logs
- `system:backup` - Backup and restore

### API Permissions
- `api:access` - Basic API access
- `api:admin` - Administrative API access

## Permission Inheritance

Roles inherit permissions from lower-level roles in the hierarchy:

```
Super Admin → System Admin → Org Admin → Org Manager → Org User → Org Viewer
                                      ↓
                            Department Managers → Department Users
```

### Examples:
- **Org Admin** inherits all permissions from Org Manager, Org User, and Org Viewer
- **Sales Manager** inherits permissions from Sales Rep, Org User, and Org Viewer
- **Marketing Manager** inherits permissions from Marketing User, Org User, and Org Viewer

## Multi-Tenant Security

### Organization Isolation
- Users can only access data within their assigned organization
- Super Admin and System Admin can access multiple organizations
- Cross-organization access is strictly controlled

### Resource Access Control
```typescript
// Example: User can only access contacts from their organization
canAccessResource(user, resourceOrgId, Permission.CONTACT_READ)
```

### Tenant Context Validation
- Every request validates organization context
- Automatic tenant isolation at the database level
- Audit logging for cross-tenant access attempts

## User Management

### Creating New Users

#### Step 1: Organization Setup
Before creating users, ensure the organization exists:

```bash
# Create organization via API
curl -X POST http://localhost:3002/api/organizations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "name": "Acme Corporation",
    "domain": "acme.com",
    "subscriptionTier": "enterprise",
    "settings": {
      "maxUsers": 100,
      "features": ["advanced_reporting", "api_access"]
    }
  }'
```

#### Step 2: User Creation in Keycloak
1. **Access Keycloak Admin Console**
   - URL: `https://stgsso.cubeone.in/admin`
   - Realm: `Service`

2. **Create User**
   ```
   Username: <EMAIL>
   Email: <EMAIL>
   First Name: John
   Last Name: Doe
   Email Verified: Yes
   Enabled: Yes
   ```

3. **Set Password**
   - Temporary: No
   - Password: <secure_password>

4. **Assign Realm Roles**
   - Based on user's intended role in the organization

#### Step 3: User Profile Creation
```bash
# Create user profile in OneCRM
curl -X POST http://localhost:3002/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "keycloakId": "<keycloak_user_id>",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "sales_rep",
    "orgId": "<organization_id>",
    "department": "sales",
    "isActive": true
  }'
```

### Role Assignment Workflows

#### Workflow 1: New Employee Onboarding

1. **HR Creates Basic Profile**
   - Role: `org_viewer` (temporary)
   - Access: Read-only during onboarding

2. **Department Manager Assigns Role**
   - Sales: `sales_rep` or `sales_manager`
   - Marketing: `marketing_user` or `marketing_manager`
   - Support: `support_agent` or `support_manager`

3. **Admin Validates and Activates**
   - Final role confirmation
   - Full system access granted

#### Workflow 2: Role Promotion

```typescript
// Example: Promote sales rep to sales manager
const promotion = {
  userId: "user_123",
  fromRole: UserRole.SALES_REP,
  toRole: UserRole.SALES_MANAGER,
  assignedBy: "admin_456",
  effectiveDate: new Date(),
  reason: "Performance promotion"
};

// Validate promotion authority
if (rbacService.canAssignRole(assignerRole, targetRole)) {
  await userService.updateRole(promotion);
}
```

#### Workflow 3: Temporary Access

```typescript
// Grant temporary elevated access
const tempAccess = {
  userId: "user_123",
  role: UserRole.ORG_MANAGER,
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  reason: "Project leadership"
};
```

### Department-Specific Setup

#### Sales Team Setup

1. **Sales Manager**
   ```json
   {
     "role": "sales_manager",
     "permissions": [
       "deal:create", "deal:read", "deal:update", "deal:delete",
       "contact:create", "contact:read", "contact:update",
       "company:create", "company:read", "company:update",
       "activity:assign", "report:view_advanced"
     ],
     "department": "sales",
     "canManage": ["sales_rep"]
   }
   ```

2. **Sales Representatives**
   ```json
   {
     "role": "sales_rep",
     "permissions": [
       "deal:create", "deal:read", "deal:update",
       "contact:create", "contact:read", "contact:update",
       "company:read", "company:update",
       "activity:create", "activity:read", "activity:update"
     ],
     "department": "sales",
     "reportsTo": "sales_manager"
   }
   ```

#### Marketing Team Setup

1. **Marketing Manager**
   ```json
   {
     "role": "marketing_manager",
     "permissions": [
       "contact:create", "contact:read", "contact:update", "contact:import",
       "company:create", "company:read", "company:update", "company:import",
       "activity:create", "activity:read", "activity:assign",
       "report:create", "report:export"
     ],
     "department": "marketing",
     "canManage": ["marketing_user"]
   }
   ```

2. **Marketing Users**
   ```json
   {
     "role": "marketing_user",
     "permissions": [
       "contact:create", "contact:read", "contact:update",
       "company:read", "company:update",
       "activity:create", "activity:read"
     ],
     "department": "marketing",
     "reportsTo": "marketing_manager"
   }
   ```

#### Support Team Setup

1. **Support Manager**
   ```json
   {
     "role": "support_manager",
     "permissions": [
       "contact:read", "contact:update",
       "company:read", "company:update",
       "activity:create", "activity:read", "activity:assign",
       "report:view_advanced"
     ],
     "department": "support",
     "canManage": ["support_agent"]
   }
   ```

2. **Support Agents**
   ```json
   {
     "role": "support_agent",
     "permissions": [
       "contact:read", "contact:update",
       "company:read", "company:update",
       "activity:create", "activity:read"
     ],
     "department": "support",
     "reportsTo": "support_manager"
   }
   ```

## API Integration

### Authentication Headers

All API requests must include proper authentication:

```bash
# Keycloak Bearer Token
Authorization: Bearer <keycloak_jwt_token>

# Organization Context
X-Org-Id: <organization_id>

# Request Tracking
X-Request-Id: <unique_request_id>
```

### Permission-Protected Endpoints

#### Using Decorators in Controllers

```typescript
import { RequirePermissions, Roles } from '@common/decorators';
import { Permission, UserRole } from '@common/enums';

@Controller('contacts')
export class ContactsController {

  // Requires specific permission
  @RequirePermissions(Permission.CONTACT_CREATE)
  @Post()
  async createContact(@Body() data: CreateContactDto) {
    // Implementation
  }

  // Requires specific role or higher
  @Roles(UserRole.SALES_REP, UserRole.SALES_MANAGER)
  @Get('sales-contacts')
  async getSalesContacts() {
    // Implementation
  }

  // Multiple permissions (user needs ANY of them)
  @RequireAnyPermission(
    Permission.CONTACT_READ,
    Permission.CONTACT_UPDATE
  )
  @Get(':id')
  async getContact(@Param('id') id: string) {
    // Implementation
  }

  // Multiple permissions (user needs ALL of them)
  @RequireAllPermissions(
    Permission.CONTACT_DELETE,
    Permission.ORG_MANAGE_USERS
  )
  @Delete(':id')
  async deleteContact(@Param('id') id: string) {
    // Implementation
  }
}
```

#### Programmatic Permission Checks

```typescript
import { RbacService, UserContext } from '@common/services';

@Injectable()
export class ContactsService {
  constructor(private rbacService: RbacService) {}

  async getContacts(user: UserContext, orgId: string) {
    // Check if user can access contacts in this organization
    if (!this.rbacService.canAccessResource(
      user,
      orgId,
      Permission.CONTACT_READ
    )) {
      throw new ForbiddenException('Access denied');
    }

    // Proceed with business logic
    return this.contactRepository.findByOrg(orgId);
  }

  async assignContactToUser(
    assignerUser: UserContext,
    contactId: string,
    targetUserId: string
  ) {
    // Check if assigner has permission
    if (!this.rbacService.hasPermission(
      assignerUser,
      Permission.CONTACT_UPDATE
    )) {
      throw new ForbiddenException('Cannot assign contacts');
    }

    // Additional business logic
    return this.performAssignment(contactId, targetUserId);
  }
}
```

### Role-Based Data Filtering

```typescript
// Automatic data filtering based on user role
@Injectable()
export class DataFilterService {

  filterContactsByRole(contacts: Contact[], user: UserContext): Contact[] {
    switch (user.role) {
      case UserRole.SALES_REP:
        // Sales reps see only their assigned contacts
        return contacts.filter(c => c.assignedTo === user.id);

      case UserRole.SALES_MANAGER:
        // Sales managers see all sales team contacts
        return contacts.filter(c =>
          c.department === 'sales' || c.assignedTo === user.id
        );

      case UserRole.ORG_ADMIN:
        // Org admins see all contacts in their organization
        return contacts.filter(c => c.orgId === user.orgId);

      case UserRole.SUPER_ADMIN:
        // Super admins see everything
        return contacts;

      default:
        return [];
    }
  }
}
```

## Security Best Practices

### 1. Principle of Least Privilege
- Assign minimum required permissions
- Regular permission audits
- Time-limited elevated access

### 2. Role Assignment Guidelines

#### DO:
- ✅ Assign roles based on job function
- ✅ Use department-specific roles when possible
- ✅ Implement approval workflows for role changes
- ✅ Set expiration dates for temporary access
- ✅ Log all role assignments and changes

#### DON'T:
- ❌ Assign higher roles than necessary
- ❌ Share accounts between users
- ❌ Skip approval processes
- ❌ Grant permanent elevated access
- ❌ Ignore failed permission checks

### 3. Organization Security

```typescript
// Example: Secure organization setup
const organizationSecurity = {
  // Enforce strong password policies
  passwordPolicy: {
    minLength: 12,
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAge: 90 // days
  },

  // Session management
  sessionPolicy: {
    maxDuration: 8 * 60 * 60, // 8 hours
    idleTimeout: 30 * 60, // 30 minutes
    maxConcurrentSessions: 3
  },

  // Access controls
  accessControls: {
    ipWhitelist: ['***********/24'],
    requireMFA: true,
    allowedLoginHours: '06:00-22:00',
    maxFailedAttempts: 5
  }
};
```

### 4. Audit and Monitoring

```typescript
// Audit logging for security events
interface SecurityAuditLog {
  timestamp: Date;
  userId: string;
  orgId: string;
  action: string;
  resource: string;
  permission: Permission;
  result: 'granted' | 'denied';
  ipAddress: string;
  userAgent: string;
  riskScore: number;
}

// Example audit events
const auditEvents = [
  'user.login',
  'user.logout',
  'role.assigned',
  'role.removed',
  'permission.granted',
  'permission.denied',
  'data.accessed',
  'data.modified',
  'organization.created',
  'organization.deleted'
];
```

### 5. Emergency Procedures

#### Account Lockout
```bash
# Immediately disable user account
curl -X PATCH http://localhost:3002/api/users/{userId} \
  -H "Authorization: Bearer <admin_token>" \
  -d '{"isActive": false, "reason": "security_incident"}'
```

#### Role Revocation
```bash
# Remove all roles from user
curl -X DELETE http://localhost:3002/api/users/{userId}/roles \
  -H "Authorization: Bearer <admin_token>" \
  -d '{"reason": "security_breach", "revokedBy": "admin_id"}'
```

#### Organization Suspension
```bash
# Suspend entire organization
curl -X PATCH http://localhost:3002/api/organizations/{orgId} \
  -H "Authorization: Bearer <super_admin_token>" \
  -d '{"status": "suspended", "reason": "security_investigation"}'
```

## Testing RBAC Implementation

### Unit Tests
```typescript
describe('RbacService', () => {
  it('should grant permission for valid role', () => {
    const user: UserContext = {
      id: 'user1',
      email: '<EMAIL>',
      role: UserRole.SALES_REP,
      orgId: 'org1',
      isActive: true
    };

    const hasPermission = rbacService.hasPermission(
      user,
      Permission.CONTACT_READ
    );

    expect(hasPermission).toBe(true);
  });

  it('should deny permission for insufficient role', () => {
    const user: UserContext = {
      id: 'user1',
      email: '<EMAIL>',
      role: UserRole.ORG_VIEWER,
      orgId: 'org1',
      isActive: true
    };

    const hasPermission = rbacService.hasPermission(
      user,
      Permission.CONTACT_DELETE
    );

    expect(hasPermission).toBe(false);
  });
});
```

### Integration Tests
```typescript
describe('Contacts API with RBAC', () => {
  it('should allow sales rep to create contacts', async () => {
    const token = await getTokenForRole(UserRole.SALES_REP);

    const response = await request(app)
      .post('/api/contacts')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Org-Id', 'test-org')
      .send(contactData)
      .expect(201);

    expect(response.body.id).toBeDefined();
  });

  it('should deny viewer from creating contacts', async () => {
    const token = await getTokenForRole(UserRole.ORG_VIEWER);

    await request(app)
      .post('/api/contacts')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Org-Id', 'test-org')
      .send(contactData)
      .expect(403);
  });
});
```

This comprehensive RBAC system ensures secure, scalable, and maintainable access control across the OneCRM platform.
