# OneCRM RBAC (Role-Based Access Control) Setup

## RBAC Architecture Overview

```mermaid
graph TD
    A[Super Admin] --> B[Organization Admin]
    B --> C[Department Manager]
    C --> D[Sales Rep]
    C --> E[Support Agent]
    C --> F[Marketing User]
    B --> G[Viewer]

    H[Permissions] --> I[Create]
    H --> J[Read]
    H --> K[Update]
    H --> L[Delete]
    H --> M[Manage Users]
    H --> N[View Reports]
    H --> O[Export Data]

    A -.-> |All Permissions| H
    B -.-> |Org Level Permissions| H
    C -.-> |Department Permissions| H
    D -.-> |Own + Team Data| H
    E -.-> |Support Cases Only| H
    F -.-> |Marketing Data Only| H
    G -.-> |Read Only| J
```

## Multi-Tenant RBAC Structure

```mermaid
graph TB
    subgraph "System Level"
        SA[Super Admin]
    end

    subgraph "Organization A"
        OA1[Org Admin A] --> DM1[Sales Manager A]
        OA1 --> DM2[Support Manager A]
        DM1 --> SR1[Sales Rep A1]
        DM1 --> SR2[Sales Rep A2]
        DM2 --> SUP1[Support Agent A1]
        OA1 --> V1[Viewer A]
    end

    subgraph "Organization B"
        OA2[Org Admin B] --> DM3[Sales Manager B]
        OA2 --> DM4[Marketing Manager B]
        DM3 --> SR3[Sales Rep B1]
        DM4 --> MU1[Marketing User B1]
        OA2 --> V2[Viewer B]
    end

    SA --> OA1
    SA --> OA2

    style SA fill:#ff6b6b
    style OA1 fill:#4ecdc4
    style OA2 fill:#4ecdc4
    style DM1 fill:#45b7d1
    style DM2 fill:#45b7d1
    style DM3 fill:#45b7d1
    style DM4 fill:#45b7d1
```

## Permission Matrix

```mermaid
graph LR
    subgraph "Resources"
        R1[Contacts]
        R2[Companies]
        R3[Deals]
        R4[Users]
        R5[Reports]
        R6[Settings]
    end

    subgraph "Actions"
        A1[Create]
        A2[Read]
        A3[Update]
        A4[Delete]
        A5[Export]
        A6[Manage]
    end

    subgraph "Roles"
        SUPER[Super Admin]
        ORG[Org Admin]
        DEPT[Dept Manager]
        SALES[Sales Rep]
        SUPPORT[Support Agent]
        MARKETING[Marketing User]
        VIEW[Viewer]
    end

    SUPER -.-> |Full Access| R1
    SUPER -.-> |Full Access| R2
    SUPER -.-> |Full Access| R3
    SUPER -.-> |Full Access| R4
    SUPER -.-> |Full Access| R5
    SUPER -.-> |Full Access| R6

    ORG -.-> |CRUD + Export| R1
    ORG -.-> |CRUD + Export| R2
    ORG -.-> |CRUD + Export| R3
    ORG -.-> |Create/Read/Update| R4
    ORG -.-> |Read + Export| R5
    ORG -.-> |Read/Update| R6

    VIEW -.-> |Read Only| R1
    VIEW -.-> |Read Only| R2
    VIEW -.-> |Read Only| R3
    VIEW -.-> |Read Only| R5
```

## Role Hierarchy & Permissions

### 1. Super Admin
**Scope**: System-wide access across all organizations
**Permissions**:
- ✅ Manage all organizations
- ✅ Create/delete organization admins
- ✅ System configuration
- ✅ Global reports and analytics
- ✅ Backup/restore operations
- ✅ Security settings

### 2. Organization Admin
**Scope**: Single organization access
**Permissions**:
- ✅ Manage organization users
- ✅ Create/edit departments
- ✅ Assign department managers
- ✅ Organization-wide reports
- ✅ Data export/import
- ✅ Organization settings
- ❌ Cannot access other organizations

### 3. Department Manager
**Scope**: Department-level access
**Permissions**:
- ✅ Manage department users
- ✅ View/edit department data
- ✅ Assign leads to team members
- ✅ Department reports
- ✅ Approve deals above threshold
- ❌ Cannot manage other departments

### 4. Sales Representative
**Scope**: Own data + assigned leads/accounts
**Permissions**:
- ✅ Create/edit own contacts
- ✅ Manage assigned leads
- ✅ Create deals up to limit
- ✅ View team performance
- ✅ Update account information
- ❌ Cannot delete contacts
- ❌ Cannot access other reps' data

### 5. Support Agent
**Scope**: Support cases and related contacts
**Permissions**:
- ✅ View all contacts (read-only)
- ✅ Create/manage support cases
- ✅ Update contact support history
- ✅ View support analytics
- ❌ Cannot access sales data
- ❌ Cannot modify contact details

### 6. Marketing User
**Scope**: Marketing campaigns and lead generation
**Permissions**:
- ✅ Create/manage campaigns
- ✅ Import leads
- ✅ View marketing analytics
- ✅ Manage email templates
- ✅ Lead scoring and qualification
- ❌ Cannot access individual deals
- ❌ Cannot modify contact ownership

### 7. Viewer
**Scope**: Read-only access to assigned data
**Permissions**:
- ✅ View contacts (limited fields)
- ✅ View public reports
- ✅ Export limited data
- ❌ Cannot create/edit/delete
- ❌ Cannot access sensitive information

## Keycloak Role Configuration

### 1. Create Realm Roles

**Navigate**: Realm roles → Create role

```
Role Name: super-admin
Description: System administrator with full access
Composite: false

Role Name: org-admin
Description: Organization administrator
Composite: false

Role Name: dept-manager
Description: Department manager
Composite: false

Role Name: sales-rep
Description: Sales representative
Composite: false

Role Name: support-agent
Description: Support agent
Composite: false

Role Name: marketing-user
Description: Marketing user
Composite: false

Role Name: viewer
Description: Read-only user
Composite: false
```

### 2. Create Composite Roles (Optional)

**Navigate**: Realm roles → Create role

```
Role Name: manager
Description: Composite role for all managers
Composite: true
Associated Roles: dept-manager, org-admin

Role Name: staff
Description: Composite role for all staff
Composite: true
Associated Roles: sales-rep, support-agent, marketing-user
```

### 3. Create Client Roles (Fine-grained permissions)

**Navigate**: Clients → onecrm-frontend → Roles

```
contacts:create
contacts:read
contacts:update
contacts:delete
contacts:export

companies:create
companies:read
companies:update
companies:delete

deals:create
deals:read
deals:update
deals:delete
deals:approve

users:create
users:read
users:update
users:delete

reports:view
reports:export
reports:admin

settings:view
settings:update
settings:admin
```

### 4. Map Client Roles to Realm Roles

**Navigate**: Realm roles → Select role → Composite roles

**Super Admin Mapping**:
- All client roles

**Org Admin Mapping**:
```
contacts:* (all contact permissions)
companies:* (all company permissions)
deals:* (all deal permissions)
users:create, users:read, users:update
reports:view, reports:export, reports:admin
settings:view, settings:update
```

**Dept Manager Mapping**:
```
contacts:create, contacts:read, contacts:update
companies:read, companies:update
deals:create, deals:read, deals:update, deals:approve
users:read, users:update (department only)
reports:view, reports:export
```

**Sales Rep Mapping**:
```
contacts:create, contacts:read, contacts:update
companies:read, companies:update
deals:create, deals:read, deals:update
reports:view
```

**Support Agent Mapping**:
```
contacts:read
companies:read
reports:view (support only)
```

**Marketing User Mapping**:
```
contacts:create, contacts:read, contacts:update
companies:read
reports:view (marketing only)
```

**Viewer Mapping**:
```
contacts:read (limited)
companies:read (limited)
deals:read (limited)
reports:view (public only)
```

## User Attributes for RBAC

### 1. Required Attributes

**Navigate**: Users → Select user → Attributes

```
organization_id: org-001
department: Sales
territory: North America
manager_id: user-123
cost_center: CC-001
security_clearance: standard
data_access_level: department
```

### 2. Attribute Mappers

**Navigate**: Clients → onecrm-frontend → Client scopes → Mappers

**Organization Mapper**:
```
Name: organization-mapper
Mapper Type: User Attribute
User Attribute: organization_id
Token Claim Name: organization_id
Claim JSON Type: String
☑ Add to ID token
☑ Add to access token
```

**Department Mapper**:
```
Name: department-mapper
Mapper Type: User Attribute
User Attribute: department
Token Claim Name: department
Claim JSON Type: String
☑ Add to ID token
☑ Add to access token
```

**Manager Mapper**:
```
Name: manager-mapper
Mapper Type: User Attribute
User Attribute: manager_id
Token Claim Name: manager_id
Claim JSON Type: String
☑ Add to ID token
☑ Add to access token
```

## Admin Management Interface

### 1. Organization Admin Capabilities

**User Management**:
- Create users within organization
- Assign roles (except super-admin)
- Deactivate/reactivate users
- Reset passwords
- Manage user attributes

**Department Management**:
- Create/edit departments
- Assign department managers
- Move users between departments
- Set department permissions

**Data Management**:
- Organization-wide data access
- Bulk data operations
- Data export/import
- Audit trail access

### 2. Department Manager Capabilities

**Team Management**:
- View department users
- Request role changes (approval required)
- Assign leads/accounts
- Set user territories

**Performance Management**:
- Department analytics
- Team performance reports
- Goal setting and tracking
- Commission calculations

### 3. Self-Service Capabilities

**All Users**:
- Update profile information
- Change password
- View assigned data
- Personal dashboard
- Activity history

## RBAC Implementation in Frontend

### 1. Permission Checking Hook

```typescript
// src/hooks/usePermissions.ts
export const usePermissions = () => {
  const { user } = useAuth();
  
  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };
  
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false;
  };
  
  const canAccess = (resource: string, action: string): boolean => {
    return hasPermission(`${resource}:${action}`);
  };
  
  return { hasPermission, hasRole, canAccess };
};
```

### 2. Protected Component Wrapper

```typescript
// src/components/auth/ProtectedComponent.tsx
interface ProtectedComponentProps {
  permission?: string;
  role?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const ProtectedComponent: React.FC<ProtectedComponentProps> = ({
  permission,
  role,
  fallback = null,
  children
}) => {
  const { hasPermission, hasRole } = usePermissions();
  
  const hasAccess = permission ? hasPermission(permission) : 
                   role ? hasRole(role) : true;
  
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};
```

### 3. Route Protection

```typescript
// src/components/auth/ProtectedRoute.tsx
export const ProtectedRoute: React.FC<{
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
}> = ({ children, requiredPermission, requiredRole }) => {
  const { hasPermission, hasRole } = usePermissions();
  const router = useRouter();
  
  useEffect(() => {
    const hasAccess = requiredPermission ? hasPermission(requiredPermission) :
                     requiredRole ? hasRole(requiredRole) : true;
    
    if (!hasAccess) {
      router.push('/unauthorized');
    }
  }, [requiredPermission, requiredRole]);
  
  return <>{children}</>;
};
```
