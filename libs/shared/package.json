{"name": "@onecrm/shared", "version": "1.0.0", "description": "Shared components and hooks for OneCRM", "main": "src/index.ts", "types": "src/index.ts", "private": true, "scripts": {"build": "tsc", "typecheck": "tsc --noEmit"}, "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}