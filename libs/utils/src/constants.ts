// Application constants

// Environment
export const NODE_ENV = process.env.NODE_ENV || 'development';
export const IS_DEVELOPMENT = NODE_ENV === 'development';
export const IS_PRODUCTION = NODE_ENV === 'production';

// API Configuration
export const API_CONFIG = {
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 10,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ],
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  DISPLAY_WITH_TIME: 'MMM dd, yyyy HH:mm',
  ISO: 'yyyy-MM-dd',
  ISO_WITH_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
} as const;

// Status Options
export const STATUS_OPTIONS = [
  { value: 'active', label: 'Active', color: 'success' },
  { value: 'inactive', label: 'Inactive', color: 'default' },
  { value: 'pending', label: 'Pending', color: 'warning' },
  { value: 'archived', label: 'Archived', color: 'secondary' },
] as const;

// Priority Options
export const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: 'info' },
  { value: 'medium', label: 'Medium', color: 'warning' },
  { value: 'high', label: 'High', color: 'error' },
  { value: 'urgent', label: 'Urgent', color: 'error' },
] as const;

// Deal Stages
export const DEAL_STAGES = [
  { value: 'lead', label: 'Lead', color: 'info' },
  { value: 'qualified', label: 'Qualified', color: 'primary' },
  { value: 'proposal', label: 'Proposal', color: 'warning' },
  { value: 'negotiation', label: 'Negotiation', color: 'secondary' },
  { value: 'closed-won', label: 'Closed Won', color: 'success' },
  { value: 'closed-lost', label: 'Closed Lost', color: 'error' },
] as const;

// User Roles
export const USER_ROLES = [
  { value: 'viewer', label: 'Viewer' },
  { value: 'user', label: 'User' },
  { value: 'admin', label: 'Admin' },
] as const;

// Organization Plans
export const ORG_PLANS = [
  { value: 'free', label: 'Free', features: ['Up to 100 contacts', 'Basic reporting'] },
  { value: 'pro', label: 'Pro', features: ['Up to 1000 contacts', 'Advanced reporting', 'API access'] },
  { value: 'enterprise', label: 'Enterprise', features: ['Unlimited contacts', 'Custom integrations', 'Priority support'] },
] as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'onecrm_auth_token',
  USER_PREFERENCES: 'onecrm_user_preferences',
  THEME: 'onecrm_theme',
  LANGUAGE: 'onecrm_language',
} as const;

// Regular Expressions
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  URL: /^https?:\/\/.+/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
} as const;
