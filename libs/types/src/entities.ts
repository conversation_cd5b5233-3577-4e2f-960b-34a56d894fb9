import { z } from 'zod';

// Contact schema
export const ContactSchema = z.object({
  id: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  company: z.string().optional(),
  position: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['active', 'inactive', 'prospect']).default('active'),
  assignedToId: z.string().optional(),
  companyId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  orgId: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Contact = z.infer<typeof ContactSchema>;

// Company schema
export const CompanySchema = z.object({
  id: z.string(),
  name: z.string(),
  industry: z.string().optional(),
  website: z.string().url().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['active', 'inactive', 'prospect']).default('active'),
  assignedToId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  orgId: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Company = z.infer<typeof CompanySchema>;

// Deal schema
export const DealSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  value: z.number().optional(),
  currency: z.string().default('USD'),
  stage: z.enum(['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost']),
  probability: z.number().min(0).max(100).optional(),
  expectedCloseDate: z.string().optional(),
  actualCloseDate: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  assignedToId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  orgId: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Deal = z.infer<typeof DealSchema>;
