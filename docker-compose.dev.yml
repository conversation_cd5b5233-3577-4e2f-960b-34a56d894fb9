# Docker Compose for OneCRM Development Environment

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: onecrm-postgres
    environment:
      POSTGRES_DB: onecrm
      POSTGRES_USER: onecrm
      POSTGRES_PASSWORD: onecrm_dev_password
    ports:
      - "5433:5432"  # Changed from 5432 to 5433 to avoid conflicts
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - onecrm-network

  # Redis for caching and sessions
  redis:
    image: redis:alpine
    container_name: onecrm-redis
    ports:
      - "6380:6379"  # Changed from 6379 to 6380 to avoid conflicts
    volumes:
      - redis_data:/data
    networks:
      - onecrm-network

  # Keycloak for authentication
  keycloak:
    image: quay.io/keycloak/keycloak:22.0
    container_name: onecrm-keycloak
    environment:
      KEYCLOAK_ADMIN: admin
      <PERSON>_ADMIN_PASSWORD: admin
      <PERSON>: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: onecrm
      KC_DB_PASSWORD: onecrm_dev_password
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    command: start-dev
    volumes:
      - keycloak_data:/opt/keycloak/data
    networks:
      - onecrm-network

  # Kong Gateway
  kong:
    image: kong/kong-gateway:3.4
    container_name: onecrm-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
      KONG_ADMIN_GUI_LISTEN: 0.0.0.0:8002
    ports:
      - "8000:8000"  # Kong proxy
      - "8001:8001"  # Kong admin API
      - "8002:8002"  # Kong Manager
      - "8443:8443"  # Kong proxy SSL
      - "8444:8444"  # Kong admin API SSL
    volumes:
      - ./kong/config/kong.yml:/kong/kong.yml:ro
    networks:
      - onecrm-network

  # OneCRM NestJS Backend (crm-api)
  crm-api:
    container_name: onecrm-crm-api
    build:
      context: .
      dockerfile: docker/crm-api/Dockerfile
    env_file:
      - ./backend/crm-api/.env.example
    environment:
      # Ensure the app binds to all interfaces in container
      HOST: 0.0.0.0
      PORT: 3001
      # Override service hosts to use docker service names
      DB_HOST: postgres
      DB_PORT: 5432
      DB_SSL: "false"
      DB_REJECT_UNAUTHORIZED: "false"
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KEYCLOAK_URL: http://keycloak:8080
      # Ensure production start command if image defaults change
      NODE_ENV: production
      LOG_FILE_ENABLED: false
      TYPEORM_SSL: "false"
    depends_on:
      - postgres
      - redis
      - keycloak
      - kong
    ports:
      - "3002:3001"   # Map host 3002 -> container 3001 to avoid conflict with frontend on 3001
    volumes:
      - crm_api_logs:/app/logs
    networks:
      - onecrm-network

  # Next.js Frontend (dev with live reload)
  frontend:
    container_name: onecrm-frontend
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
    environment:
      HOST: 0.0.0.0
      PORT: 3000
      NODE_ENV: development
      NEXT_TELEMETRY_DISABLED: 1
      # Ensure frontend proxied API matches our next.config.cjs rewrites
      NEXT_PUBLIC_API_BASE_URL: http://localhost:3002
    ports:
      - "3000:3000"
    depends_on:
      - crm-api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - onecrm-network

volumes:
  postgres_data:
  redis_data:
  keycloak_data:
  crm_api_logs:

networks:
  onecrm-network:
    driver: bridge
