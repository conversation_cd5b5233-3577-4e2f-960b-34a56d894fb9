# Kong Gateway Configuration for Microservices Architecture
# OneCRM Microservices Development Configuration
# Version: 3.0

_format_version: "3.0"
_transform: true

# ============================================================================
# MICROSERVICES CONFIGURATION
# ============================================================================

services:
  # User Management Microservice
  - name: user-service
    url: http://host.docker.internal:3010
    protocol: http
    host: host.docker.internal
    port: 3010
    tags:
      - microservice
      - user
      - management
    routes:
      - name: user-service-route
        paths:
          - /api/users
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - users
          - microservice
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          policy: local

  # Contact Management Microservice
  - name: contact-service
    url: http://host.docker.internal:3011
    protocol: http
    host: host.docker.internal
    port: 3011
    tags:
      - microservice
      - contact
      - crm
    routes:
      - name: contact-service-route
        paths:
          - /api/contacts
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - contacts
          - microservice
    plugins:
      - name: rate-limiting
        config:
          minute: 200
          hour: 2000
          policy: local

  # Company Management Microservice
  - name: company-service
    url: http://host.docker.internal:3012
    protocol: http
    host: host.docker.internal
    port: 3012
    tags:
      - microservice
      - company
      - crm
    routes:
      - name: company-service-route
        paths:
          - /api/companies
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - companies
          - microservice

  # Deal Management Microservice
  - name: deal-service
    url: http://host.docker.internal:3013
    protocol: http
    host: host.docker.internal
    port: 3013
    tags:
      - microservice
      - deal
      - sales
    routes:
      - name: deal-service-route
        paths:
          - /api/deals
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - deals
          - microservice

  # Analytics Microservice
  - name: analytics-service
    url: http://host.docker.internal:3014
    protocol: http
    host: host.docker.internal
    port: 3014
    tags:
      - microservice
      - analytics
      - reporting
    routes:
      - name: analytics-service-route
        paths:
          - /api/analytics
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - analytics
          - microservice
    plugins:
      - name: rate-limiting
        config:
          minute: 50
          hour: 500
          policy: local

  # Email Service Microservice
  - name: email-service
    url: http://host.docker.internal:3015
    protocol: http
    host: host.docker.internal
    port: 3015
    tags:
      - microservice
      - email
      - communication
    routes:
      - name: email-service-route
        paths:
          - /api/email
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - email
          - microservice
    plugins:
      - name: rate-limiting
        config:
          minute: 30
          hour: 300
          policy: local

  # Search Service Microservice
  - name: search-service
    url: http://host.docker.internal:3016
    protocol: http
    host: host.docker.internal
    port: 3016
    tags:
      - microservice
      - search
      - elasticsearch
    routes:
      - name: search-service-route
        paths:
          - /api/search
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - search
          - microservice

  # Webhook Service Microservice
  - name: webhook-service
    url: http://host.docker.internal:3017
    protocol: http
    host: host.docker.internal
    port: 3017
    tags:
      - microservice
      - webhook
      - integration
    routes:
      - name: webhook-service-route
        paths:
          - /api/webhooks
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - webhooks
          - microservice

  # Audit Log Microservice
  - name: audit-service
    url: http://host.docker.internal:3018
    protocol: http
    host: host.docker.internal
    port: 3018
    tags:
      - microservice
      - audit
      - logging
    routes:
      - name: audit-service-route
        paths:
          - /api/audit
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - audit
          - microservice

  # Integration Service Microservice
  - name: integration-service
    url: http://host.docker.internal:3019
    protocol: http
    host: host.docker.internal
    port: 3019
    tags:
      - microservice
      - integration
      - third-party
    routes:
      - name: integration-service-route
        paths:
          - /api/integrations
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - integrations
          - microservice

# ============================================================================
# GLOBAL PLUGINS FOR MICROSERVICES
# ============================================================================

plugins:
  # Service Discovery and Health Monitoring
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
  
  # Request Tracing
  - name: request-id
    config:
      header_name: "Kong-Request-ID"
      generator: "uuid"
      echo_downstream: true
  
  # Distributed Tracing
  - name: correlation-id
    config:
      header_name: "Kong-Correlation-ID"
      generator: "uuid"
      echo_downstream: true
  
  # Circuit Breaker (commented out - requires Kong Enterprise)
  # - name: proxy-cache
  #   config:
  #     response_code:
  #       - 200
  #       - 301
  #       - 404
  #     request_method:
  #       - GET
  #       - HEAD
  #     content_type:
  #       - text/plain
  #       - application/json
  #     cache_ttl: 300
  #     strategy: memory
  
  # Global CORS for Microservices
  - name: cors
    config:
      origins:
        - "http://localhost:3000"
        - "http://localhost:3001"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Content-Type
        - Authorization
        - X-Org-Id
        - X-User-Id
        - X-Request-ID
        - X-Correlation-ID
      credentials: true
      max_age: 3600

# ============================================================================
# MICROSERVICES UPSTREAMS
# ============================================================================

upstreams:
  # User Service Upstream
  - name: user-service-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - user
      - microservice
    targets:
      - target: "host.docker.internal:3010"
        weight: 100

  # Contact Service Upstream
  - name: contact-service-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - contact
      - microservice
    targets:
      - target: "host.docker.internal:3011"
        weight: 100

  # Company Service Upstream
  - name: company-service-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - company
      - microservice
    targets:
      - target: "host.docker.internal:3012"
        weight: 100

# ============================================================================
# MICROSERVICES CONSUMERS
# ============================================================================

consumers:
  # Frontend Application
  - username: "frontend-app"
    custom_id: "frontend-microservices"
    tags:
      - frontend
      - microservices
  
  # Mobile Application
  - username: "mobile-app"
    custom_id: "mobile-microservices"
    tags:
      - mobile
      - microservices
  
  # Internal Service Communication
  - username: "internal-services"
    custom_id: "internal-microservices"
    tags:
      - internal
      - microservices
  
  # External API Consumers
  - username: "external-api"
    custom_id: "external-microservices"
    tags:
      - external
      - api
      - microservices

# ============================================================================
# CONFIGURATION NOTES
# ============================================================================

# Microservices Architecture Features:
# - Individual service routing
# - Service-specific rate limiting
# - Health check monitoring
# - Distributed tracing support
# - Service discovery ready
# - Load balancing per service
# - Independent scaling capability
# 
# Service Ports:
# - User Service: 3010
# - Contact Service: 3011
# - Company Service: 3012
# - Deal Service: 3013
# - Analytics Service: 3014
# - Email Service: 3015
# - Search Service: 3016
# - Webhook Service: 3017
# - Audit Service: 3018
# - Integration Service: 3019
# 
# Development Usage:
# 1. Start individual microservices on their respective ports
# 2. Kong will route requests based on path prefixes
# 3. Each service can be developed and deployed independently
# 4. Health checks ensure service availability
# 5. Metrics collection for monitoring
# 
# Production Considerations:
# - Add authentication plugins per service
# - Configure service mesh integration
# - Set up proper SSL/TLS
# - Implement circuit breakers
# - Add caching layers
# - Configure external monitoring
