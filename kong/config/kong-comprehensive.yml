# Kong Gateway Comprehensive Configuration for OneCRM
# This configuration provides a complete setup for development, staging, and production environments
# Version: 3.0
# Last Updated: 2025-01-30

_format_version: "3.0"
_transform: true

# ============================================================================
# SERVICES CONFIGURATION
# ============================================================================

services:
  # CRM API Service - Main application backend
  - name: crm-api
    url: http://host.docker.internal:3001
    protocol: http
    host: host.docker.internal
    port: 3001
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - crm
      - api
      - backend
      - v1
    routes:
      - name: crm-api-route
        paths:
          - /api
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        regex_priority: 0
        path_handling: v1
        request_buffering: true
        response_buffering: true
        tags:
          - api
          - crm
    plugins:
      # CORS Configuration
      - name: cors
        config:
          origins:
            - "http://localhost:3000"
            - "http://localhost:3001"
            - "https://app.onecrm.com"
            - "https://staging.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
            - HEAD
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - X-Org-Id
            - X-User-Id
            - X-Request-ID
            - X-Correlation-ID
            - Cache-Control
            - If-Modified-Since
          exposed_headers:
            - X-Auth-Token
            - X-Tenant-Id
            - X-Kong-Request-ID
            - X-Kong-Upstream-Latency
            - X-RateLimit-Limit
            - X-RateLimit-Remaining
          credentials: true
          max_age: 3600
          preflight_continue: false
      
      # Rate Limiting
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          day: 10000
          month: 100000
          year: 1000000
          policy: local
          hide_client_headers: false
          fault_tolerant: true
          redis_host: null
          redis_port: 6379
          redis_password: null
          redis_timeout: 2000
          redis_database: 0
      
      # Request Transformation
      - name: request-transformer
        config:
          remove:
            headers:
              - X-Client-Org-Id
              - X-Internal-Token
            querystring: []
            body: []
          rename:
            headers: []
            querystring: []
            body: []
          replace:
            headers: []
            querystring: []
            body: []
          add:
            headers:
              - "X-Kong-Request-ID:$(kong.request.get_header('kong-request-id'))"
              - "X-Request-Start:$(kong.request.get_start_time())"
              - "X-Forwarded-Proto:$(kong.request.get_scheme())"
            querystring: []
            body: []
          append:
            headers: []
            querystring: []
            body: []
      
      # Response Transformation
      - name: response-transformer
        config:
          remove:
            headers:
              - X-Internal-Service
            json: []
          rename:
            headers: []
            json: []
          replace:
            headers: []
            json: []
          add:
            headers:
              - "X-Kong-Upstream-Latency:$(kong.response.get_header('x-kong-upstream-latency'))"
              - "X-Kong-Proxy-Latency:$(kong.response.get_header('x-kong-proxy-latency'))"
              - "X-Content-Type-Options:nosniff"
              - "X-Frame-Options:SAMEORIGIN"
              - "X-XSS-Protection:1; mode=block"
              - "Referrer-Policy:strict-origin-when-cross-origin"
            json: []
          append:
            headers: []
            json: []
      
      # Request Size Limiting
      - name: request-size-limiting
        config:
          allowed_payload_size: 10
          size_unit: megabytes
          require_content_length: false
      
      # IP Restriction (commented out for development)
      # - name: ip-restriction
      #   config:
      #     allow:
      #       - "127.0.0.1"
      #       - "10.0.0.0/8"
      #       - "**********/12"
      #       - "***********/16"
      #     deny: []
      #     status: 403
      #     message: "Your IP address is not allowed"

  # Authentication Service
  - name: auth-service
    url: http://host.docker.internal:3002
    protocol: http
    host: host.docker.internal
    port: 3002
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - auth
      - service
      - security
    routes:
      - name: auth-service-route
        paths:
          - /auth
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - auth
          - security
    plugins:
      # CORS for Auth Service
      - name: cors
        config:
          origins:
            - "http://localhost:3000"
            - "https://app.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Content-Type
            - Authorization
          credentials: true
          max_age: 3600
      
      # Rate Limiting for Auth
      - name: rate-limiting
        config:
          minute: 50
          hour: 500
          policy: local
          hide_client_headers: false

  # Tenant Management Service
  - name: tenant-service
    url: http://host.docker.internal:3003
    protocol: http
    host: host.docker.internal
    port: 3003
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - tenant
      - service
      - multi-tenancy
    routes:
      - name: tenant-service-route
        paths:
          - /tenants
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - tenant
          - multi-tenancy
    plugins:
      # CORS for Tenant Service
      - name: cors
        config:
          origins:
            - "http://localhost:3000"
            - "https://app.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          credentials: true

  # File Upload Service
  - name: file-service
    url: http://host.docker.internal:3004
    protocol: http
    host: host.docker.internal
    port: 3004
    path: /
    connect_timeout: 120000
    write_timeout: 120000
    read_timeout: 120000
    retries: 2
    tags:
      - file
      - upload
      - storage
    routes:
      - name: file-service-route
        paths:
          - /files
        methods:
          - GET
          - POST
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - file
          - upload
    plugins:
      # Larger payload for file uploads
      - name: request-size-limiting
        config:
          allowed_payload_size: 100
          size_unit: megabytes
      
      # Rate limiting for file uploads
      - name: rate-limiting
        config:
          minute: 20
          hour: 200
          policy: local

  # Notification Service
  - name: notification-service
    url: http://host.docker.internal:3005
    protocol: http
    host: host.docker.internal
    port: 3005
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - notification
      - service
      - messaging
    routes:
      - name: notification-service-route
        paths:
          - /notifications
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - notification
          - messaging

# ============================================================================
# GLOBAL PLUGINS CONFIGURATION
# ============================================================================

plugins:
  # Prometheus Metrics Collection
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true

  # Request ID Generation
  - name: request-id
    config:
      header_name: "Kong-Request-ID"
      generator: "uuid"
      echo_downstream: true

  # Correlation ID for Distributed Tracing
  - name: correlation-id
    config:
      header_name: "Kong-Correlation-ID"
      generator: "uuid"
      echo_downstream: true

  # Global Request Logging
  - name: file-log
    config:
      path: "/tmp/kong-access.log"
      reopen: true

  # HTTP Log for External Monitoring (commented out for development)
  # - name: http-log
  #   config:
  #     http_endpoint: "https://monitoring.onecrm.com/kong-logs"
  #     method: "POST"
  #     timeout: 10000
  #     keepalive: 60000
  #     content_type: "application/json"
  #     flush_timeout: 2
  #     retry_count: 10

  # Datadog Integration (commented out for development)
  # - name: datadog
  #   config:
  #     host: "datadog-agent"
  #     port: 8125
  #     metrics:
  #       - name: "request_count"
  #         stat_type: "counter"
  #         sample_rate: 1
  #       - name: "latency"
  #         stat_type: "timer"
  #         sample_rate: 1
  #       - name: "request_size"
  #         stat_type: "timer"
  #         sample_rate: 1
  #       - name: "status_count"
  #         stat_type: "counter"
  #         sample_rate: 1
  #       - name: "response_size"
  #         stat_type: "timer"
  #         sample_rate: 1

# ============================================================================
# CONSUMERS CONFIGURATION
# ============================================================================

consumers:
  # Development Consumer
  - username: "dev-client"
    custom_id: "dev-001"
    tags:
      - development
      - testing

  # Frontend Application Consumer
  - username: "onecrm-frontend"
    custom_id: "frontend-001"
    tags:
      - frontend
      - application

  # Mobile Application Consumer
  - username: "onecrm-mobile"
    custom_id: "mobile-001"
    tags:
      - mobile
      - application

  # API Integration Consumer
  - username: "api-integration"
    custom_id: "integration-001"
    tags:
      - integration
      - api

# ============================================================================
# UPSTREAMS CONFIGURATION (Load Balancing)
# ============================================================================

upstreams:
  # CRM API Upstream with Health Checks
  - name: crm-api-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: "/"
    slots: 10000
    healthchecks:
      active:
        type: http
        http_path: "/api/health"
        https_verify_certificate: false
        healthy:
          interval: 30
          http_statuses:
            - 200
            - 201
            - 202
            - 204
          successes: 1
        unhealthy:
          interval: 30
          http_statuses:
            - 429
            - 500
            - 502
            - 503
            - 504
            - 505
          tcp_failures: 3
          timeouts: 3
          http_failures: 3
      passive:
        type: http
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 204
            - 301
            - 302
            - 303
            - 304
          successes: 5
        unhealthy:
          http_statuses:
            - 429
            - 500
            - 502
            - 503
            - 504
            - 505
          tcp_failures: 3
          timeouts: 7
          http_failures: 5
    tags:
      - crm
      - api
      - upstream
    targets:
      - target: "host.docker.internal:3001"
        weight: 100
        tags:
          - primary
          - crm-api

  # Auth Service Upstream
  - name: auth-service-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/auth/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - auth
      - service
      - upstream
    targets:
      - target: "host.docker.internal:3002"
        weight: 100
        tags:
          - primary
          - auth-service

  # Tenant Service Upstream
  - name: tenant-service-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/tenants/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - tenant
      - service
      - upstream
    targets:
      - target: "host.docker.internal:3003"
        weight: 100
        tags:
          - primary
          - tenant-service

# ============================================================================
# CERTIFICATES CONFIGURATION (SSL/TLS)
# ============================================================================

certificates:
  # Development Self-Signed Certificate (placeholder)
  # - cert: "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"
  #   key: "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
  #   tags:
  #     - development
  #     - self-signed

  # Production Certificate (placeholder)
  # - cert: "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"
  #   key: "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
  #   tags:
  #     - production
  #     - letsencrypt

# ============================================================================
# SNI CONFIGURATION (Server Name Indication)
# ============================================================================

snis:
  # Development SNI
  # - name: "dev.onecrm.local"
  #   certificate:
  #     id: "dev-cert-id"
  #   tags:
  #     - development

  # Production SNI
  # - name: "api.onecrm.com"
  #   certificate:
  #     id: "prod-cert-id"
  #   tags:
  #     - production

# ============================================================================
# VAULTS CONFIGURATION (Secret Management)
# ============================================================================

vaults:
  # Environment Variables Vault
  - name: env
    prefix: env
    config:
      prefix: "KONG_VAULT_"
    tags:
      - environment
      - secrets

  # AWS Secrets Manager (commented out for development)
  # - name: aws
  #   prefix: aws
  #   config:
  #     region: "us-east-1"
  #   tags:
  #     - aws
  #     - secrets

  # HashiCorp Vault (commented out for development)
  # - name: hcv
  #   prefix: hcv
  #   config:
  #     protocol: "https"
  #     host: "vault.onecrm.com"
  #     port: 8200
  #     mount: "secret"
  #     kv: "v2"
  #     auth_method: "token"
  #   tags:
  #     - hashicorp
  #     - vault
  #     - secrets

# ============================================================================
# TARGETS CONFIGURATION (Additional Load Balancer Targets)
# ============================================================================

targets: []

# ============================================================================
# CA CERTIFICATES CONFIGURATION
# ============================================================================

ca_certificates: []

# ============================================================================
# CONFIGURATION METADATA
# ============================================================================

# Configuration Information:
# - Environment: Development/Staging/Production
# - Version: 1.0.0
# - Last Updated: 2025-01-30
# - Maintainer: OneCRM Development Team
#
# Features Included:
# - Multi-service routing (CRM API, Auth, Tenant, File, Notification)
# - Comprehensive CORS configuration
# - Rate limiting with different policies
# - Request/Response transformation
# - Health checks and load balancing
# - Security headers
# - Monitoring and metrics collection
# - Request ID and correlation ID tracking
# - File upload handling
# - Consumer management
# - SSL/TLS certificate management (placeholder)
# - Secret management with vaults
#
# Usage:
# - Development: Use with docker-compose or local Kong installation
# - Production: Customize URLs, certificates, and security settings
# - Staging: Use production-like settings with staging endpoints
#
# Security Considerations:
# - Enable IP restrictions for production
# - Configure proper SSL certificates
# - Set up authentication plugins (OAuth2, JWT, etc.)
# - Configure rate limiting based on actual usage patterns
# - Enable request/response logging for monitoring
# - Set up proper CORS origins for production
#
# Performance Optimizations:
# - Configure upstream health checks
# - Set appropriate timeouts
# - Enable caching plugins where appropriate
# - Configure load balancing algorithms
# - Monitor metrics and adjust rate limits
