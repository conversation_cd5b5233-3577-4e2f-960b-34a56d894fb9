# Kong Gateway Configuration for API Versioning
# OneCRM API Versioning Strategy Configuration
# Version: 3.0

_format_version: "3.0"
_transform: true

# ============================================================================
# API VERSIONING SERVICES CONFIGURATION
# ============================================================================

services:
  # CRM API Version 1 (Legacy)
  - name: crm-api-v1
    url: http://host.docker.internal:3001
    protocol: http
    host: host.docker.internal
    port: 3001
    path: /v1
    tags:
      - crm
      - api
      - v1
      - legacy
    routes:
      # Path-based versioning
      - name: crm-api-v1-path
        paths:
          - /api/v1
        strip_path: true
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v1
          - path-versioning
      
      # Header-based versioning
      - name: crm-api-v1-header
        paths:
          - /api
        headers:
          Accept-Version:
            - "1.0"
            - "1.x"
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v1
          - header-versioning
      
      # Query parameter versioning
      - name: crm-api-v1-query
        paths:
          - /api
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v1
          - query-versioning
    plugins:
      # Version-specific rate limiting
      - name: rate-limiting
        config:
          minute: 50
          hour: 500
          policy: local
          hide_client_headers: false
      
      # Deprecation warning headers
      - name: response-transformer
        config:
          add:
            headers:
              - "X-API-Version:1.0"
              - "X-API-Deprecated:true"
              - "X-API-Sunset:2025-12-31"
              - "Warning:299 - \"API version 1.0 is deprecated. Please migrate to v2.0\""
      
      # Request transformation for v1 compatibility
      - name: request-transformer
        config:
          add:
            headers:
              - "X-API-Version:1.0"
              - "X-Legacy-Request:true"

  # CRM API Version 2 (Current)
  - name: crm-api-v2
    url: http://host.docker.internal:3002
    protocol: http
    host: host.docker.internal
    port: 3002
    path: /v2
    tags:
      - crm
      - api
      - v2
      - current
    routes:
      # Path-based versioning
      - name: crm-api-v2-path
        paths:
          - /api/v2
        strip_path: true
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v2
          - path-versioning
      
      # Header-based versioning
      - name: crm-api-v2-header
        paths:
          - /api
        headers:
          Accept-Version:
            - "2.0"
            - "2.x"
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v2
          - header-versioning
      
      # Default version (no version specified)
      - name: crm-api-v2-default
        paths:
          - /api
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        regex_priority: 1
        tags:
          - v2
          - default-version
    plugins:
      # Current version rate limiting
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          policy: local
          hide_client_headers: false
      
      # Version headers
      - name: response-transformer
        config:
          add:
            headers:
              - "X-API-Version:2.0"
              - "X-API-Status:current"
      
      # Request transformation for v2
      - name: request-transformer
        config:
          add:
            headers:
              - "X-API-Version:2.0"

  # CRM API Version 3 (Beta)
  - name: crm-api-v3-beta
    url: http://host.docker.internal:3003
    protocol: http
    host: host.docker.internal
    port: 3003
    path: /v3
    tags:
      - crm
      - api
      - v3
      - beta
    routes:
      # Path-based versioning for beta
      - name: crm-api-v3-beta-path
        paths:
          - /api/v3
          - /api/beta
        strip_path: true
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v3
          - beta
          - path-versioning
      
      # Header-based versioning for beta
      - name: crm-api-v3-beta-header
        paths:
          - /api
        headers:
          Accept-Version:
            - "3.0"
            - "3.x"
            - "beta"
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - v3
          - beta
          - header-versioning
    plugins:
      # Beta version rate limiting (more restrictive)
      - name: rate-limiting
        config:
          minute: 30
          hour: 300
          policy: local
          hide_client_headers: false
      
      # Beta warning headers
      - name: response-transformer
        config:
          add:
            headers:
              - "X-API-Version:3.0-beta"
              - "X-API-Status:beta"
              - "Warning:299 - \"This is a beta version. Features may change without notice\""
      
      # Request transformation for v3 beta
      - name: request-transformer
        config:
          add:
            headers:
              - "X-API-Version:3.0-beta"
              - "X-Beta-Request:true"

  # Version Router Service (Smart Routing)
  - name: version-router
    url: http://host.docker.internal:3100
    protocol: http
    host: host.docker.internal
    port: 3100
    tags:
      - version
      - router
      - smart-routing
    routes:
      - name: version-router-route
        paths:
          - /api/smart
        strip_path: true
        preserve_host: false
        protocols:
          - http
          - https
        tags:
          - smart-routing
    plugins:
      # Smart routing based on client capabilities
      - name: request-transformer
        config:
          add:
            headers:
              - "X-Smart-Routing:enabled"

# ============================================================================
# VERSION-SPECIFIC PLUGINS
# ============================================================================

plugins:
  # Global version detection
  - name: request-transformer
    config:
      add:
        headers:
          - "X-Kong-Request-ID:$(kong.request.get_header('kong-request-id'))"
          - "X-Request-Time:$(kong.request.get_start_time())"
  
  # Global response headers
  - name: response-transformer
    config:
      add:
        headers:
          - "X-Kong-Gateway:OneCRM-API-Gateway"
          - "X-Supported-Versions:1.0,2.0,3.0-beta"
  
  # Prometheus metrics with version labels
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true

# ============================================================================
# VERSION-AWARE CONSUMERS
# ============================================================================

consumers:
  # Legacy Client (v1 only)
  - username: "legacy-client"
    custom_id: "legacy-v1"
    tags:
      - legacy
      - v1-only
  
  # Modern Client (v2 preferred)
  - username: "modern-client"
    custom_id: "modern-v2"
    tags:
      - modern
      - v2-preferred
  
  # Beta Tester (v3 access)
  - username: "beta-tester"
    custom_id: "beta-v3"
    tags:
      - beta
      - v3-access
  
  # Multi-version Client
  - username: "multi-version-client"
    custom_id: "multi-version"
    tags:
      - multi-version
      - flexible

# ============================================================================
# VERSION-SPECIFIC UPSTREAMS
# ============================================================================

upstreams:
  # Version 1 Upstream
  - name: crm-api-v1-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/v1/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - v1
      - legacy
    targets:
      - target: "host.docker.internal:3001"
        weight: 100
        tags:
          - v1
          - primary

  # Version 2 Upstream
  - name: crm-api-v2-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/v2/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - v2
      - current
    targets:
      - target: "host.docker.internal:3002"
        weight: 100
        tags:
          - v2
          - primary

  # Version 3 Beta Upstream
  - name: crm-api-v3-beta-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/v3/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - v3
      - beta
    targets:
      - target: "host.docker.internal:3003"
        weight: 100
        tags:
          - v3
          - beta

# ============================================================================
# CONFIGURATION NOTES
# ============================================================================

# API Versioning Strategies Supported:
# 
# 1. Path-based Versioning:
#    - /api/v1/contacts
#    - /api/v2/contacts
#    - /api/v3/contacts
# 
# 2. Header-based Versioning:
#    - Accept-Version: 1.0
#    - Accept-Version: 2.0
#    - Accept-Version: 3.0-beta
# 
# 3. Query Parameter Versioning:
#    - /api/contacts?version=1.0
#    - /api/contacts?version=2.0
# 
# 4. Smart Routing:
#    - Automatic version selection based on client capabilities
#    - Fallback to compatible versions
# 
# Version Lifecycle Management:
# - v1: Deprecated (sunset date: 2025-12-31)
# - v2: Current stable version
# - v3: Beta version (features may change)
# 
# Client Migration Strategy:
# 1. Deprecation warnings for v1 clients
# 2. Gradual feature migration to v2
# 3. Beta testing program for v3
# 4. Sunset timeline communication
# 
# Monitoring and Analytics:
# - Version usage metrics
# - Client migration tracking
# - Performance comparison across versions
# - Error rate monitoring per version
# 
# Best Practices:
# - Semantic versioning (MAJOR.MINOR.PATCH)
# - Backward compatibility within major versions
# - Clear deprecation timeline
# - Comprehensive documentation per version
# - Client SDK version alignment
