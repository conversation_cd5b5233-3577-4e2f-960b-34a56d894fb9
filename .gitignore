# ===================================
# OneCRM Project .gitignore
# ===================================
# Comprehensive gitignore for Next.js, Node.js, TypeScript, Docker, Keycloak, Kong, and testing

# ===================================
# Dependencies & Package Managers
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.yarn-integrity
.pnpm-store/
.pnpm-lock.yaml
package-lock.json
yarn.lock

# ===================================
# Environment Variables & Secrets
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging
.env.production
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
.secrets/
.env.vault

# ===================================
# Next.js & Frontend Build Output
# ===================================
.next/
out/
build/
dist/
.vercel/
.turbo/

# Next.js cache
.next/cache/
.next/static/chunks/
.next/server/

# Frontend specific
frontend/.next/
frontend/out/
frontend/build/
frontend/dist/
frontend/.turbo/

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo
.tsbuildinfo
tsconfig.tsbuildinfo
frontend/tsconfig.tsbuildinfo

# ===================================
# Testing & Coverage
# ===================================
coverage/
*.lcov
.nyc_output
test-results/
playwright-report/
test-results.xml
junit.xml

# Jest
jest-html-reporters-attach/
jest-stare/

# Playwright
frontend/test-results/
frontend/playwright-report/
frontend/playwright/.cache/

# Coverage reports
frontend/coverage/
backend/coverage/
libs/*/coverage/

# ===================================
# Logs & Runtime Data
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ===================================
# Cache & Temporary Files
# ===================================
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache
.node_repl_history
tmp/
temp/
.tmp/
.temp/

# ===================================
# Nx Monorepo
# ===================================
.nx/cache/
.nx/workspace-data/
dist/
tmp/

# ===================================
# Database Files
# ===================================
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# PostgreSQL
*.sql.backup
*.dump

# ===================================
# Docker & Containerization
# ===================================
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
.docker/
Dockerfile.local

# Docker volumes
postgres-data/
redis-data/
keycloak-data/

# ===================================
# Keycloak
# ===================================
keycloak-data/
keycloak/data/
keycloak/logs/
keycloak/standalone/data/
keycloak/standalone/log/
keycloak/standalone/tmp/

# ===================================
# Kong Gateway
# ===================================
kong/config/generated/
kong/logs/
kong/data/
kong/tmp/

# ===================================
# Infrastructure & Deployment
# ===================================
# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# Kubernetes
*.kubeconfig
kustomization.yaml.bak

# Helm
charts/*/charts/
charts/*/requirements.lock

# ===================================
# IDE & Editor Files
# ===================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
.metadata/

# VS Code specific (keep some useful files)
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# ===================================
# OS Generated Files
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# Backup & Archive Files
# ===================================
*.backup
*.bak
*.orig
*.rej
*.tar
*.tar.gz
*.zip
*.rar

# ===================================
# Monitoring & Analytics
# ===================================
# Application monitoring
newrelic_agent.log
.newrelic/

# Performance monitoring
lighthouse-report.html
bundle-analyzer-report.html

# ===================================
# Development Tools
# ===================================
# Storybook
.out/
.storybook-out/
storybook-static/

# ESLint
.eslintcache

# Prettier
.prettierignore.local

# Husky
.husky/_/

# ===================================
# Package Manager Artifacts
# ===================================
*.tgz
*.tar.gz
.yarn-integrity
.pnp.*
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# ===================================
# Security & Compliance
# ===================================
# Security scan results
security-report.json
audit-report.json

# License compliance
license-report.json

# ===================================
# Custom OneCRM Specific
# ===================================
# Mock data (if sensitive)
mock-data/sensitive/

# Generated API documentation
docs/api/generated/

# Performance benchmarks
benchmarks/results/

# Migration scripts output
migrations/output/

# ===================================
# Keep Important Files
# ===================================
# Keep example environment files
!.env.example
!frontend/.env.example
!backend/.env.example

# Keep Docker compose templates
!docker-compose.template.yml

# Keep configuration templates
!config/*.template.*
!*/config/*.template.*
